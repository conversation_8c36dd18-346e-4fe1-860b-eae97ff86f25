{"permissions": {"allow": ["Bash(npx @modelcontextprotocol/cli list-servers:*)", "Bash(npm search:*)", "Bash(npm install:*)", "Bash(npx @supabase/mcp-server-supabase:*)", "<PERSON><PERSON>(cat:*)", "Bash(echo $NEXT_PUBLIC_SUPABASE_URL)", "Bash(node:*)", "Bash(NEXT_PUBLIC_SUPABASE_URL=https://cjfdzpiqgnxewhdivcrc.supabase.co NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNqZmR6cGlxZ254ZXdoZGl2Y3JjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MDE5MjMsImV4cCI6MjA2NTQ3NzkyM30.aJyteSRSZXDfRDMUIf0tdtWPm0msfPL85m5ZPGRAVpU node fetch-project-data.js <EMAIL>)"], "deny": []}}