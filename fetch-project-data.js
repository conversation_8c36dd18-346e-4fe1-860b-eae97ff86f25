const { createClient } = require('@supabase/supabase-js');

// Note: You'll need to set these environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'your-supabase-url';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'your-supabase-anon-key';

if (!supabaseUrl || !supabaseKey || supabaseUrl === 'your-supabase-url') {
  console.error('❌ Please set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY environment variables');
  console.log('You can find these in your Supabase project settings');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function fetchProjectData(email) {
  try {
    console.log(`🔍 Fetching data for email: ${email}`);
    console.log('====================================');

    // 1. Check if user profile exists
    console.log('\n📋 1. User Profile:');
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('email', email)
      .single();

    if (profileError) {
      console.log(`   ❌ Profile not found: ${profileError.message}`);
    } else {
      console.log(`   ✅ Profile found: ${profile.display_name || profile.email}`);
      console.log(`   📝 Role: ${profile.role}`);
      console.log(`   📅 Created: ${new Date(profile.created_at).toLocaleDateString()}`);
    }

    // 2. Check HielLinks profiles
    console.log('\n🔗 2. HielLinks Profiles:');
    if (profile) {
      const { data: hielProfiles, error: hielError } = await supabase
        .from('hiel_profiles')
        .select(`
          *,
          links:hiel_links(*)
        `)
        .eq('user_id', profile.id);

      if (hielError) {
        console.log(`   ❌ Error fetching HielLinks: ${hielError.message}`);
      } else if (!hielProfiles || hielProfiles.length === 0) {
        console.log('   📭 No HielLinks profiles found');
      } else {
        hielProfiles.forEach((hielProfile, index) => {
          console.log(`   ✅ Profile ${index + 1}: @${hielProfile.username}`);
          console.log(`      📝 Business: ${hielProfile.business_name}`);
          console.log(`      🎨 Status: ${hielProfile.status}`);
          console.log(`      👁️ Views: ${hielProfile.view_count || 0}`);
          console.log(`      🔗 Links: ${hielProfile.links?.length || 0}`);
          if (hielProfile.links && hielProfile.links.length > 0) {
            hielProfile.links.forEach((link, linkIndex) => {
              console.log(`         ${linkIndex + 1}. ${link.title} (${link.type})`);
            });
          }
        });
      }
    }

    // 3. Check team applications
    console.log('\n👥 3. Team Applications:');
    const { data: applications, error: appError } = await supabase
      .from('team_applications')
      .select('*')
      .eq('email', email);

    if (appError) {
      console.log(`   ❌ Error fetching applications: ${appError.message}`);
    } else if (!applications || applications.length === 0) {
      console.log('   📭 No team applications found');
    } else {
      applications.forEach((app, index) => {
        console.log(`   ✅ Application ${index + 1}:`);
        console.log(`      👤 Name: ${app.name}`);
        console.log(`      💼 Role: ${app.desired_role}`);
        console.log(`      📊 Status: ${app.status}`);
        console.log(`      🏆 Experience: ${app.experience_level}`);
        console.log(`      📅 Applied: ${new Date(app.created_at).toLocaleDateString()}`);
      });
    }

    // 4. Check inquiries
    console.log('\n💬 4. Contact Inquiries:');
    const { data: inquiries, error: inquiryError } = await supabase
      .from('inquiries')
      .select('*')
      .eq('email', email);

    if (inquiryError) {
      console.log(`   ❌ Error fetching inquiries: ${inquiryError.message}`);
    } else if (!inquiries || inquiries.length === 0) {
      console.log('   📭 No inquiries found');
    } else {
      inquiries.forEach((inquiry, index) => {
        console.log(`   ✅ Inquiry ${index + 1}:`);
        console.log(`      📝 Subject: ${inquiry.subject || 'No subject'}`);
        console.log(`      📊 Status: ${inquiry.status}`);
        console.log(`      📅 Created: ${new Date(inquiry.created_at).toLocaleDateString()}`);
      });
    }

    // 5. Check chat permissions
    console.log('\n💬 5. Chat Permissions:');
    if (profile) {
      const { data: chatPerms, error: chatError } = await supabase
        .from('chat_permissions')
        .select('*')
        .eq('user_id', profile.id);

      if (chatError) {
        console.log(`   ❌ Error fetching chat permissions: ${chatError.message}`);
      } else if (!chatPerms || chatPerms.length === 0) {
        console.log('   📭 No chat permissions found');
      } else {
        chatPerms.forEach((perm, index) => {
          console.log(`   ✅ Permission ${index + 1}:`);
          console.log(`      🔓 Enabled: ${perm.is_enabled ? 'Yes' : 'No'}`);
          console.log(`      📧 Type: ${perm.chat_type || 'application'}`);
          console.log(`      👤 Enabled by: ${perm.enabled_by || 'System'}`);
        });
      }
    }

    // 6. Check tasks created by the user
    console.log('\n📋 6. Tasks:');
    if (profile) {
      const { data: tasks, error: taskError } = await supabase
        .from('tasks')
        .select('*')
        .eq('created_by', profile.id);

      if (taskError) {
        console.log(`   ❌ Error fetching tasks: ${taskError.message}`);
      } else if (!tasks || tasks.length === 0) {
        console.log('   📭 No tasks found');
      } else {
        tasks.forEach((task, index) => {
          console.log(`   ✅ Task ${index + 1}:`);
          console.log(`      📝 Title: ${task.title}`);
          console.log(`      📊 Status: ${task.status}`);
          console.log(`      ⚡ Priority: ${task.priority}`);
          console.log(`      📅 Created: ${new Date(task.created_at).toLocaleDateString()}`);
        });
      }
    }

    console.log('\n✅ Data fetch completed!');
    
  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
  }
}

// Check command line arguments
const email = process.argv[2] || '<EMAIL>';

console.log('🚀 HielTech Project Data Fetcher');
console.log('================================');

fetchProjectData(email).then(() => {
  console.log('\n👋 Done!');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Script failed:', error);
  process.exit(1);
});