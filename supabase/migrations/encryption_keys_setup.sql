-- Migration: Set up encryption keys and vault configuration
-- Created: $(date)
-- Purpose: Implement application-level encryption for sensitive data

-- Create application encryption keys using Vault
DO $$
DECLARE
    chat_key_id UUID;
    profile_key_id UUID;
    analytics_key_id UUID;
BEGIN
    -- Chat messages encryption key
    SELECT vault.create_secret(
        'chat_encryption_key_' || gen_random_uuid()::text,
        'chat_encryption_key',
        'AES-256 key for encrypting chat messages'
    ) INTO chat_key_id;
    
    -- Profile data encryption key
    SELECT vault.create_secret(
        'profile_encryption_key_' || gen_random_uuid()::text,
        'profile_encryption_key', 
        'AES-256 key for encrypting sensitive profile data'
    ) INTO profile_key_id;
    
    -- Analytics data encryption key
    SELECT vault.create_secret(
        'analytics_encryption_key_' || gen_random_uuid()::text,
        'analytics_encryption_key',
        'AES-256 key for encrypting analytics data'
    ) INTO analytics_key_id;
    
    RAISE NOTICE 'Encryption keys created successfully';
END $$;

-- Create table for managing encryption metadata
CREATE TABLE IF NOT EXISTS encryption_metadata (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    table_name TEXT NOT NULL,
    column_name TEXT NOT NULL,
    encryption_method TEXT NOT NULL DEFAULT 'AES-GCM',
    key_name TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    UNIQUE(table_name, column_name)
);

-- Enable RLS on encryption metadata
ALTER TABLE encryption_metadata ENABLE ROW LEVEL SECURITY;

-- Only allow service role to access encryption metadata
CREATE POLICY "encryption_metadata_service_only" ON encryption_metadata
    FOR ALL USING (auth.role() = 'service_role');

-- Add encryption status columns to sensitive tables
ALTER TABLE chat_messages 
ADD COLUMN IF NOT EXISTS is_encrypted BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS encryption_key_id TEXT;

ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS encrypted_fields JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS encryption_version INTEGER DEFAULT 1;

ALTER TABLE inquiries
ADD COLUMN IF NOT EXISTS encrypted_fields JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS encryption_version INTEGER DEFAULT 1;

ALTER TABLE team_applications
ADD COLUMN IF NOT EXISTS encrypted_fields JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS encryption_version INTEGER DEFAULT 1;

-- Create helper function to get encryption key
CREATE OR REPLACE FUNCTION get_encryption_key(key_name TEXT)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    key_value TEXT;
BEGIN
    -- Only allow service role to access encryption keys
    IF auth.role() != 'service_role' THEN
        RAISE EXCEPTION 'Access denied: Only service role can access encryption keys';
    END IF;
    
    SELECT decrypted_secret INTO key_value
    FROM vault.decrypted_secrets
    WHERE name = key_name;
    
    IF key_value IS NULL THEN
        RAISE EXCEPTION 'Encryption key not found: %', key_name;
    END IF;
    
    RETURN key_value;
END;
$$;

-- Create trigger function for updated_at
CREATE OR REPLACE FUNCTION update_encryption_metadata_updated_at()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$;

-- Create trigger for encryption_metadata
CREATE TRIGGER update_encryption_metadata_updated_at
    BEFORE UPDATE ON encryption_metadata
    FOR EACH ROW
    EXECUTE FUNCTION update_encryption_metadata_updated_at();

-- Insert encryption metadata for sensitive fields
INSERT INTO encryption_metadata (table_name, column_name, key_name) VALUES
    ('chat_messages', 'message', 'chat_encryption_key'),
    ('profiles', 'email', 'profile_encryption_key'),
    ('profiles', 'display_name', 'profile_encryption_key'),
    ('profiles', 'bio', 'profile_encryption_key'),
    ('inquiries', 'email', 'profile_encryption_key'),
    ('inquiries', 'phone', 'profile_encryption_key'),
    ('inquiries', 'message', 'profile_encryption_key'),
    ('team_applications', 'email', 'profile_encryption_key'),
    ('team_applications', 'phone', 'profile_encryption_key'),
    ('team_applications', 'motivation', 'profile_encryption_key'),
    ('hiel_analytics', 'visitor_ip', 'analytics_encryption_key')
ON CONFLICT (table_name, column_name) DO NOTHING;

COMMENT ON TABLE encryption_metadata IS 'Tracks which fields are encrypted and with which keys';
COMMENT ON FUNCTION get_encryption_key IS 'Securely retrieves encryption keys from vault (service role only)'; 