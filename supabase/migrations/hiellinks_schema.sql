-- HielLinks Service Database Schema
-- This migration creates all necessary tables for the HielLinks service

-- Create hiel_profiles table
CREATE TABLE hiel_profiles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  username TEXT UNIQUE NOT NULL,
  business_name TEXT NOT NULL,
  description TEXT,
  logo_url TEXT,
  background_image_url TEXT,
  theme_color TEXT DEFAULT '#3B82F6',
  text_color TEXT DEFAULT '#FFFFFF',
  location TEXT,
  latitude DECIMAL(10, 8),
  longitude DECIMAL(11, 8),
  phone TEXT,
  email TEXT,
  website TEXT,
  is_active BOOLEAN DEFAULT true,
  is_featured BOOLEAN DEFAULT false,
  view_count INTEGER DEFAULT 0,
  click_count INTEGER DEFAULT 0,
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'suspended')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create hiel_links table
CREATE TABLE hiel_links (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  profile_id UUID REFERENCES hiel_profiles(id) ON DELETE CASCADE NOT NULL,
  title TEXT NOT NULL,
  url TEXT NOT NULL,
  type TEXT DEFAULT 'custom' CHECK (type IN ('website', 'social', 'contact', 'custom')),
  platform TEXT CHECK (platform IN ('instagram', 'facebook', 'twitter', 'linkedin', 'youtube', 'tiktok', 'whatsapp', 'telegram', 'github', 'other')),
  icon TEXT,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  click_count INTEGER DEFAULT 0,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create hiel_analytics table
CREATE TABLE hiel_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  profile_id UUID REFERENCES hiel_profiles(id) ON DELETE CASCADE NOT NULL,
  link_id UUID REFERENCES hiel_links(id) ON DELETE CASCADE,
  event_type TEXT NOT NULL CHECK (event_type IN ('profile_view', 'link_click')),
  visitor_ip INET,
  user_agent TEXT,
  referrer TEXT,
  country TEXT,
  city TEXT,
  device_type TEXT,
  browser TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create hiel_settings table
CREATE TABLE hiel_settings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
  max_profiles INTEGER DEFAULT 1,
  max_links_per_profile INTEGER DEFAULT 5,
  max_storage_mb INTEGER DEFAULT 10,
  can_use_custom_domain BOOLEAN DEFAULT false,
  can_use_analytics BOOLEAN DEFAULT true,
  can_remove_branding BOOLEAN DEFAULT false,
  subscription_type TEXT DEFAULT 'free' CHECK (subscription_type IN ('free', 'premium', 'enterprise')),
  subscription_expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_hiel_profiles_user_id ON hiel_profiles(user_id);
CREATE INDEX idx_hiel_profiles_username ON hiel_profiles(username);
CREATE INDEX idx_hiel_profiles_status ON hiel_profiles(status);
CREATE INDEX idx_hiel_profiles_is_active ON hiel_profiles(is_active);
CREATE INDEX idx_hiel_profiles_is_featured ON hiel_profiles(is_featured);

CREATE INDEX idx_hiel_links_profile_id ON hiel_links(profile_id);
CREATE INDEX idx_hiel_links_sort_order ON hiel_links(sort_order);
CREATE INDEX idx_hiel_links_is_active ON hiel_links(is_active);

CREATE INDEX idx_hiel_analytics_profile_id ON hiel_analytics(profile_id);
CREATE INDEX idx_hiel_analytics_link_id ON hiel_analytics(link_id);
CREATE INDEX idx_hiel_analytics_event_type ON hiel_analytics(event_type);
CREATE INDEX idx_hiel_analytics_created_at ON hiel_analytics(created_at);

CREATE INDEX idx_hiel_settings_user_id ON hiel_settings(user_id);

-- Enable Row Level Security (RLS)
ALTER TABLE hiel_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE hiel_links ENABLE ROW LEVEL SECURITY;
ALTER TABLE hiel_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE hiel_settings ENABLE ROW LEVEL SECURITY;

-- RLS Policies for hiel_profiles
CREATE POLICY "Users can view published profiles" ON hiel_profiles
  FOR SELECT USING (status = 'published' AND is_active = true);

CREATE POLICY "Users can view own profiles" ON hiel_profiles
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own profiles" ON hiel_profiles
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own profiles" ON hiel_profiles
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own profiles" ON hiel_profiles
  FOR DELETE USING (auth.uid() = user_id);

-- Admin policies for hiel_profiles
CREATE POLICY "Admins can manage all profiles" ON hiel_profiles
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- RLS Policies for hiel_links
CREATE POLICY "Users can view links of published profiles" ON hiel_links
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM hiel_profiles 
      WHERE hiel_profiles.id = hiel_links.profile_id 
      AND hiel_profiles.status = 'published' 
      AND hiel_profiles.is_active = true
    )
  );

CREATE POLICY "Users can manage own profile links" ON hiel_links
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM hiel_profiles 
      WHERE hiel_profiles.id = hiel_links.profile_id 
      AND hiel_profiles.user_id = auth.uid()
    )
  );

-- Admin policies for hiel_links
CREATE POLICY "Admins can manage all links" ON hiel_links
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- RLS Policies for hiel_analytics
CREATE POLICY "Users can view own profile analytics" ON hiel_analytics
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM hiel_profiles 
      WHERE hiel_profiles.id = hiel_analytics.profile_id 
      AND hiel_profiles.user_id = auth.uid()
    )
  );

CREATE POLICY "System can insert analytics" ON hiel_analytics
  FOR INSERT WITH CHECK (true);

-- Admin policies for hiel_analytics
CREATE POLICY "Admins can view all analytics" ON hiel_analytics
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- RLS Policies for hiel_settings
CREATE POLICY "Users can view own settings" ON hiel_settings
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own settings" ON hiel_settings
  FOR UPDATE USING (auth.uid() = user_id);

-- Admin policies for hiel_settings
CREATE POLICY "Admins can manage all settings" ON hiel_settings
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- Create function to automatically create hiel_settings for new users
CREATE OR REPLACE FUNCTION create_hiel_settings_for_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO hiel_settings (user_id)
  VALUES (NEW.id);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create hiel_settings when a user signs up
CREATE TRIGGER on_auth_user_created_hiel_settings
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION create_hiel_settings_for_user();

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER update_hiel_profiles_updated_at
  BEFORE UPDATE ON hiel_profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_hiel_links_updated_at
  BEFORE UPDATE ON hiel_links
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_hiel_settings_updated_at
  BEFORE UPDATE ON hiel_settings
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to validate username format
CREATE OR REPLACE FUNCTION validate_hiel_username(username TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  -- Username must be 3-30 characters, alphanumeric and underscores only, no consecutive underscores
  RETURN username ~ '^[a-zA-Z0-9][a-zA-Z0-9_]*[a-zA-Z0-9]$' 
    AND LENGTH(username) BETWEEN 3 AND 30
    AND username NOT LIKE '%__%';
END;
$$ LANGUAGE plpgsql;

-- Add constraint to validate username format
ALTER TABLE hiel_profiles ADD CONSTRAINT valid_username 
  CHECK (validate_hiel_username(username));

-- Insert default admin settings for existing admin user
INSERT INTO hiel_settings (
  user_id, 
  max_profiles, 
  max_links_per_profile, 
  max_storage_mb, 
  can_use_custom_domain, 
  can_use_analytics, 
  can_remove_branding, 
  subscription_type
)
SELECT 
  id, 
  999, 
  999, 
  1000, 
  true, 
  true, 
  true, 
  'enterprise'
FROM profiles 
WHERE role = 'admin'
ON CONFLICT (user_id) DO NOTHING;
