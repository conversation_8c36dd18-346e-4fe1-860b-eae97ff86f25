-- Social Media Planner Database Schema
-- This migration creates all necessary tables for the social media planning feature

-- Create social_media_accounts table
CREATE TABLE social_media_accounts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  platform TEXT NOT NULL CHECK (platform IN ('instagram', 'facebook', 'twitter', 'linkedin')),
  platform_user_id TEXT NOT NULL,
  username TEXT NOT NULL,
  display_name TEXT NOT NULL,
  avatar_url TEXT,
  access_token TEXT NOT NULL,
  refresh_token TEXT,
  token_expires_at TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT true,
  scopes TEXT[] DEFAULT ARRAY[]::TEXT[],
  account_type TEXT DEFAULT 'personal' CHECK (account_type IN ('personal', 'business', 'creator')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, platform, platform_user_id)
);

-- <PERSON>reate social_media_posts table
CREATE TABLE social_media_posts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  account_id UUID REFERENCES social_media_accounts(id) ON DELETE CASCADE NOT NULL,
  platform TEXT NOT NULL CHECK (platform IN ('instagram', 'facebook', 'twitter', 'linkedin')),
  post_type TEXT DEFAULT 'image' CHECK (post_type IN ('image', 'video', 'carousel', 'story', 'reel', 'text')),
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'scheduled', 'published', 'failed', 'cancelled')),
  
  -- Content
  caption TEXT,
  media_urls TEXT[] DEFAULT ARRAY[]::TEXT[],
  hashtags TEXT[] DEFAULT ARRAY[]::TEXT[],
  mentions TEXT[] DEFAULT ARRAY[]::TEXT[],
  
  -- Scheduling
  scheduled_at TIMESTAMP WITH TIME ZONE,
  published_at TIMESTAMP WITH TIME ZONE,
  
  -- Platform-specific data
  platform_post_id TEXT,
  platform_data JSONB DEFAULT '{}'::JSONB,
  
  -- Metrics (populated after publishing)
  likes_count INTEGER DEFAULT 0,
  comments_count INTEGER DEFAULT 0,
  shares_count INTEGER DEFAULT 0,
  views_count INTEGER DEFAULT 0,
  engagement_rate DECIMAL(5,4) DEFAULT 0,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create social_media_analytics table
CREATE TABLE social_media_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  account_id UUID REFERENCES social_media_accounts(id) ON DELETE CASCADE NOT NULL,
  platform TEXT NOT NULL CHECK (platform IN ('instagram', 'facebook', 'twitter', 'linkedin')),
  period TEXT DEFAULT 'day' CHECK (period IN ('day', 'week', 'month', 'year')),
  
  -- Metrics
  followers_count INTEGER DEFAULT 0,
  posts_count INTEGER DEFAULT 0,
  total_likes INTEGER DEFAULT 0,
  total_comments INTEGER DEFAULT 0,
  total_shares INTEGER DEFAULT 0,
  engagement_rate DECIMAL(5,4) DEFAULT 0,
  reach INTEGER DEFAULT 0,
  impressions INTEGER DEFAULT 0,
  
  -- Top posts data
  top_posts JSONB DEFAULT '[]'::JSONB,
  
  date DATE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(account_id, period, date)
);

-- Create social_media_settings table
CREATE TABLE social_media_settings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
  
  -- Default settings
  default_hashtags JSONB DEFAULT '{}'::JSONB, -- platform -> hashtags mapping
  auto_schedule_optimal_times BOOLEAN DEFAULT false,
  optimal_posting_times JSONB DEFAULT '{}'::JSONB, -- platform -> times mapping
  content_approval_required BOOLEAN DEFAULT false,
  analytics_tracking_enabled BOOLEAN DEFAULT true,
  
  -- Notification settings
  notify_on_publish BOOLEAN DEFAULT true,
  notify_on_metrics_update BOOLEAN DEFAULT false,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_social_media_accounts_user_id ON social_media_accounts(user_id);
CREATE INDEX idx_social_media_accounts_platform ON social_media_accounts(platform);
CREATE INDEX idx_social_media_accounts_active ON social_media_accounts(is_active);

CREATE INDEX idx_social_media_posts_user_id ON social_media_posts(user_id);
CREATE INDEX idx_social_media_posts_account_id ON social_media_posts(account_id);
CREATE INDEX idx_social_media_posts_status ON social_media_posts(status);
CREATE INDEX idx_social_media_posts_scheduled_at ON social_media_posts(scheduled_at);
CREATE INDEX idx_social_media_posts_platform ON social_media_posts(platform);

CREATE INDEX idx_social_media_analytics_account_id ON social_media_analytics(account_id);
CREATE INDEX idx_social_media_analytics_date ON social_media_analytics(date);
CREATE INDEX idx_social_media_analytics_period ON social_media_analytics(period);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_social_media_accounts_updated_at 
  BEFORE UPDATE ON social_media_accounts 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_social_media_posts_updated_at 
  BEFORE UPDATE ON social_media_posts 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_social_media_settings_updated_at 
  BEFORE UPDATE ON social_media_settings 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE social_media_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE social_media_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE social_media_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE social_media_settings ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Users can only access their own social media accounts
CREATE POLICY "Users can view their own social media accounts" ON social_media_accounts
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own social media accounts" ON social_media_accounts
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own social media accounts" ON social_media_accounts
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own social media accounts" ON social_media_accounts
  FOR DELETE USING (auth.uid() = user_id);

-- Users can only access their own posts
CREATE POLICY "Users can view their own social media posts" ON social_media_posts
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own social media posts" ON social_media_posts
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own social media posts" ON social_media_posts
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own social media posts" ON social_media_posts
  FOR DELETE USING (auth.uid() = user_id);

-- Users can only access analytics for their own accounts
CREATE POLICY "Users can view analytics for their own accounts" ON social_media_analytics
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM social_media_accounts 
      WHERE social_media_accounts.id = social_media_analytics.account_id 
      AND social_media_accounts.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert analytics for their own accounts" ON social_media_analytics
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM social_media_accounts 
      WHERE social_media_accounts.id = social_media_analytics.account_id 
      AND social_media_accounts.user_id = auth.uid()
    )
  );

-- Users can only access their own settings
CREATE POLICY "Users can view their own social media settings" ON social_media_settings
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own social media settings" ON social_media_settings
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own social media settings" ON social_media_settings
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own social media settings" ON social_media_settings
  FOR DELETE USING (auth.uid() = user_id);

-- Create function to automatically create settings for new users
CREATE OR REPLACE FUNCTION create_default_social_media_settings()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO social_media_settings (user_id)
  VALUES (NEW.id);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-create settings for new users
-- Note: This assumes the auth.users table exists, adjust if needed
-- CREATE TRIGGER create_user_social_media_settings
--   AFTER INSERT ON auth.users
--   FOR EACH ROW EXECUTE FUNCTION create_default_social_media_settings();

-- Add comments for documentation
COMMENT ON TABLE social_media_accounts IS 'Stores connected social media accounts for users';
COMMENT ON TABLE social_media_posts IS 'Stores social media posts (drafts, scheduled, and published)';
COMMENT ON TABLE social_media_analytics IS 'Stores analytics data for social media accounts';
COMMENT ON TABLE social_media_settings IS 'Stores user preferences for social media planning';

COMMENT ON COLUMN social_media_accounts.platform_user_id IS 'The user ID from the social media platform';
COMMENT ON COLUMN social_media_accounts.access_token IS 'OAuth access token (encrypted in production)';
COMMENT ON COLUMN social_media_accounts.scopes IS 'Array of granted OAuth scopes';

COMMENT ON COLUMN social_media_posts.platform_data IS 'Platform-specific metadata stored as JSON';
COMMENT ON COLUMN social_media_posts.hashtags IS 'Array of hashtags without the # symbol';
COMMENT ON COLUMN social_media_posts.mentions IS 'Array of mentioned usernames without the @ symbol';

COMMENT ON COLUMN social_media_analytics.top_posts IS 'JSON array of top performing posts for the period';

-- Sample insert statements (commented out for production)
/*
-- Example: Insert a Facebook account
INSERT INTO social_media_accounts (user_id, platform, platform_user_id, username, display_name, access_token, scopes, account_type)
VALUES (
  'user-uuid-here',
  'facebook',
  'facebook-user-id',
  'username',
  'Display Name',
  'encrypted-access-token',
  ARRAY['email', 'public_profile', 'pages_show_list'],
  'business'
);

-- Example: Insert a draft post
INSERT INTO social_media_posts (user_id, account_id, platform, post_type, caption, hashtags, status)
VALUES (
  'user-uuid-here',
  'account-uuid-here',
  'instagram',
  'image',
  'Amazing sunset! 🌅',
  ARRAY['sunset', 'photography', 'nature'],
  'draft'
);
*/ 