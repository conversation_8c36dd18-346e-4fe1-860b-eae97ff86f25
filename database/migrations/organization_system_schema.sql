-- ====================================
-- ORGANIZATIONAL SYSTEM DATABASE MIGRATION
-- ====================================

-- Drop existing tables if they exist (in correct order due to foreign keys)
DROP TABLE IF EXISTS organization_analytics CASCADE;
DROP TABLE IF EXISTS organization_subscriptions CASCADE;
DROP TABLE IF EXISTS organization_messages CASCADE;
DROP TABLE IF EXISTS organization_channels CASCADE;
DROP TABLE IF EXISTS task_time_entries CASCADE;
DROP TABLE IF EXISTS organization_tasks CASCADE;
DROP TABLE IF EXISTS organization_projects CASCADE;
DROP TABLE IF EXISTS organization_invitations CASCADE;
DROP TABLE IF EXISTS organization_members CASCADE;
DROP TABLE IF EXISTS organizations CASCADE;

-- ====================================
-- ORGANIZATIONS TABLE
-- ====================================
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    logo_url TEXT,
    domain VARCHAR(255),
    industry VARCHAR(100),
    size VARCHAR(20) CHECK (size IN ('startup', 'small', 'medium', 'large', 'enterprise')),
    subscription_tier VARCHAR(20) DEFAULT 'free' CHECK (subscription_tier IN ('free', 'pro', 'enterprise')),
    country VARCHAR(100),
    timezone VARCHAR(100),
    website TEXT,
    phone VARCHAR(50),
    address TEXT,
    
    -- Settings stored as JSONB for flexibility
    settings JSONB DEFAULT '{
        "default_project_visibility": "organization",
        "enable_time_tracking": true,
        "enable_budget_tracking": false,
        "default_task_priority": "medium",
        "enable_public_chat": true,
        "enable_guest_access": false,
        "chat_retention_days": 365,
        "require_2fa": false,
        "allowed_domains": [],
        "session_timeout_minutes": 480,
        "features": {
            "advanced_analytics": false,
            "custom_branding": false,
            "api_access": false,
            "integrations": false,
            "custom_fields": false,
            "advanced_permissions": false
        },
        "brand_colors": {
            "primary": "#3B82F6",
            "secondary": "#8B5CF6",
            "accent": "#10B981"
        }
    }'::jsonb,
    
    -- Stats stored as JSONB
    stats JSONB DEFAULT '{
        "total_members": 0,
        "active_projects": 0,
        "completed_projects": 0,
        "total_tasks": 0,
        "completed_tasks": 0,
        "storage_used_mb": 0,
        "storage_limit_mb": 1024
    }'::jsonb,
    
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ====================================
-- ORGANIZATION MEMBERS TABLE
-- ====================================
CREATE TABLE organization_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    role VARCHAR(20) DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'project_manager', 'member', 'viewer', 'guest')),
    permissions TEXT[] DEFAULT '{}',
    department VARCHAR(100),
    title VARCHAR(100),
    employee_id VARCHAR(50),
    hourly_rate DECIMAL(10,2),
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    invited_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    invitation_token VARCHAR(255),
    invitation_expires_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'pending_invitation', 'suspended')),
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(organization_id, user_id)
);

-- ====================================
-- ORGANIZATION INVITATIONS TABLE
-- ====================================
CREATE TABLE organization_invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'project_manager', 'member', 'viewer', 'guest')),
    permissions TEXT[] DEFAULT '{}',
    invited_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'expired', 'revoked')),
    token VARCHAR(255) UNIQUE NOT NULL,
    message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(organization_id, email, status) -- Prevent duplicate pending invitations
);

-- ====================================
-- ORGANIZATION PROJECTS TABLE
-- ====================================
CREATE TABLE organization_projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'planning' CHECK (status IN ('planning', 'active', 'on_hold', 'completed', 'cancelled')),
    priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
    visibility VARCHAR(20) DEFAULT 'organization' CHECK (visibility IN ('private', 'organization', 'public')),
    
    -- Project details
    start_date DATE,
    due_date DATE,
    budget DECIMAL(12,2),
    estimated_hours INTEGER,
    actual_hours INTEGER DEFAULT 0,
    
    -- Team assignment
    project_manager_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    team_members UUID[] DEFAULT '{}',
    
    -- Client information
    client_info JSONB DEFAULT '{}'::jsonb,
    
    -- Project structure
    tags TEXT[] DEFAULT '{}',
    custom_fields JSONB DEFAULT '{}'::jsonb,
    
    -- Analytics
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    tasks_total INTEGER DEFAULT 0,
    tasks_completed INTEGER DEFAULT 0,
    
    created_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ====================================
-- ORGANIZATION TASKS TABLE
-- ====================================
CREATE TABLE organization_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    project_id UUID REFERENCES organization_projects(id) ON DELETE CASCADE,
    
    title VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'todo' CHECK (status IN ('todo', 'in_progress', 'review', 'done', 'cancelled')),
    priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    
    -- Assignment
    assignee_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    reporter_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE SET NULL,
    
    -- Time management
    due_date TIMESTAMP WITH TIME ZONE,
    estimated_hours INTEGER,
    actual_hours INTEGER DEFAULT 0,
    
    -- Dependencies and structure
    dependencies UUID[] DEFAULT '{}',
    subtasks UUID[] DEFAULT '{}',
    parent_task_id UUID REFERENCES organization_tasks(id) ON DELETE SET NULL,
    
    -- Labels and organization
    labels TEXT[] DEFAULT '{}',
    custom_fields JSONB DEFAULT '{}'::jsonb,
    
    -- Counts (denormalized for performance)
    comments_count INTEGER DEFAULT 0,
    attachments_count INTEGER DEFAULT 0,
    
    created_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ====================================
-- TASK TIME ENTRIES TABLE
-- ====================================
CREATE TABLE task_time_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id UUID NOT NULL REFERENCES organization_tasks(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    hours DECIMAL(5,2) NOT NULL CHECK (hours > 0),
    description TEXT,
    billable BOOLEAN DEFAULT false,
    date DATE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ====================================
-- ORGANIZATION CHANNELS TABLE
-- ====================================
CREATE TABLE organization_channels (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    type VARCHAR(20) DEFAULT 'public' CHECK (type IN ('public', 'private', 'direct', 'project')),
    
    -- Channel settings
    is_archived BOOLEAN DEFAULT false,
    is_readonly BOOLEAN DEFAULT false,
    message_retention_days INTEGER DEFAULT 365,
    
    -- Project integration
    project_id UUID REFERENCES organization_projects(id) ON DELETE CASCADE,
    
    -- Members and permissions
    members UUID[] DEFAULT '{}',
    admins UUID[] DEFAULT '{}',
    
    -- Analytics
    message_count INTEGER DEFAULT 0,
    last_activity_at TIMESTAMP WITH TIME ZONE,
    
    created_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(organization_id, name)
);

-- ====================================
-- ORGANIZATION MESSAGES TABLE
-- ====================================
CREATE TABLE organization_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    channel_id UUID NOT NULL REFERENCES organization_channels(id) ON DELETE CASCADE,
    sender_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    content TEXT NOT NULL,
    message_type VARCHAR(20) DEFAULT 'text' CHECK (message_type IN ('text', 'file', 'system', 'task_update', 'project_update')),
    
    -- Rich content
    attachments JSONB DEFAULT '[]'::jsonb,
    mentions UUID[] DEFAULT '{}',
    reactions JSONB DEFAULT '[]'::jsonb,
    
    -- Threading
    thread_id UUID REFERENCES organization_messages(id) ON DELETE CASCADE,
    reply_count INTEGER DEFAULT 0,
    
    -- Editing
    is_edited BOOLEAN DEFAULT false,
    edit_history JSONB DEFAULT '[]'::jsonb,
    
    -- System messages
    system_data JSONB,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ====================================
-- ORGANIZATION SUBSCRIPTIONS TABLE
-- ====================================
CREATE TABLE organization_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    tier VARCHAR(20) DEFAULT 'free' CHECK (tier IN ('free', 'pro', 'enterprise')),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'cancelled', 'past_due', 'trialing')),
    
    -- Limits based on tier
    limits JSONB DEFAULT '{
        "max_members": 5,
        "max_projects": 3,
        "max_storage_gb": 1,
        "max_integrations": 0
    }'::jsonb,
    
    -- Billing information
    current_period_start TIMESTAMP WITH TIME ZONE,
    current_period_end TIMESTAMP WITH TIME ZONE,
    next_billing_date TIMESTAMP WITH TIME ZONE,
    amount DECIMAL(10,2) DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'USD',
    
    -- Payment information
    payment_method VARCHAR(255),
    billing_email VARCHAR(255),
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(organization_id)
);

-- ====================================
-- ORGANIZATION ANALYTICS TABLE
-- ====================================
CREATE TABLE organization_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    
    -- Activity metrics
    active_users INTEGER DEFAULT 0,
    messages_sent INTEGER DEFAULT 0,
    tasks_created INTEGER DEFAULT 0,
    tasks_completed INTEGER DEFAULT 0,
    projects_created INTEGER DEFAULT 0,
    
    -- Performance metrics
    avg_task_completion_time INTEGER DEFAULT 0, -- in hours
    on_time_delivery_rate DECIMAL(5,2) DEFAULT 0,
    team_productivity_score DECIMAL(5,2) DEFAULT 0,
    
    -- Communication metrics
    channels_active INTEGER DEFAULT 0,
    avg_response_time_minutes INTEGER DEFAULT 0,
    collaboration_score DECIMAL(5,2) DEFAULT 0,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(organization_id, date)
);

-- ====================================
-- INDEXES FOR PERFORMANCE
-- ====================================

-- Organizations
CREATE INDEX idx_organizations_slug ON organizations(slug);
CREATE INDEX idx_organizations_domain ON organizations(domain);
CREATE INDEX idx_organizations_created_by ON organizations(created_by);

-- Organization Members
CREATE INDEX idx_org_members_org_id ON organization_members(organization_id);
CREATE INDEX idx_org_members_user_id ON organization_members(user_id);
CREATE INDEX idx_org_members_status ON organization_members(status);
CREATE INDEX idx_org_members_role ON organization_members(role);

-- Organization Invitations
CREATE INDEX idx_org_invitations_org_id ON organization_invitations(organization_id);
CREATE INDEX idx_org_invitations_email ON organization_invitations(email);
CREATE INDEX idx_org_invitations_token ON organization_invitations(token);
CREATE INDEX idx_org_invitations_status ON organization_invitations(status);

-- Organization Projects
CREATE INDEX idx_org_projects_org_id ON organization_projects(organization_id);
CREATE INDEX idx_org_projects_status ON organization_projects(status);
CREATE INDEX idx_org_projects_manager ON organization_projects(project_manager_id);
CREATE INDEX idx_org_projects_due_date ON organization_projects(due_date);

-- Organization Tasks
CREATE INDEX idx_org_tasks_org_id ON organization_tasks(organization_id);
CREATE INDEX idx_org_tasks_project_id ON organization_tasks(project_id);
CREATE INDEX idx_org_tasks_assignee ON organization_tasks(assignee_id);
CREATE INDEX idx_org_tasks_status ON organization_tasks(status);
CREATE INDEX idx_org_tasks_due_date ON organization_tasks(due_date);

-- Task Time Entries
CREATE INDEX idx_time_entries_task_id ON task_time_entries(task_id);
CREATE INDEX idx_time_entries_user_id ON task_time_entries(user_id);
CREATE INDEX idx_time_entries_date ON task_time_entries(date);

-- Organization Channels
CREATE INDEX idx_org_channels_org_id ON organization_channels(organization_id);
CREATE INDEX idx_org_channels_type ON organization_channels(type);
CREATE INDEX idx_org_channels_project_id ON organization_channels(project_id);

-- Organization Messages
CREATE INDEX idx_org_messages_channel_id ON organization_messages(channel_id);
CREATE INDEX idx_org_messages_sender_id ON organization_messages(sender_id);
CREATE INDEX idx_org_messages_created_at ON organization_messages(created_at);
CREATE INDEX idx_org_messages_thread_id ON organization_messages(thread_id);

-- Organization Analytics
CREATE INDEX idx_org_analytics_org_id ON organization_analytics(organization_id);
CREATE INDEX idx_org_analytics_date ON organization_analytics(date);

-- ====================================
-- TRIGGERS FOR UPDATED_AT
-- ====================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to relevant tables
CREATE TRIGGER update_organizations_updated_at BEFORE UPDATE ON organizations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_org_projects_updated_at BEFORE UPDATE ON organization_projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_org_tasks_updated_at BEFORE UPDATE ON organization_tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_org_channels_updated_at BEFORE UPDATE ON organization_channels FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_org_messages_updated_at BEFORE UPDATE ON organization_messages FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_org_subscriptions_updated_at BEFORE UPDATE ON organization_subscriptions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ====================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- ====================================

-- Enable RLS on all tables
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_time_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_channels ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_analytics ENABLE ROW LEVEL SECURITY;

-- Organizations - Members can view their organizations
CREATE POLICY "Members can view their organizations" ON organizations
    FOR SELECT USING (
        id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid() AND status = 'active'
        )
    );

-- Organizations - Owners and admins can update
CREATE POLICY "Owners and admins can update organizations" ON organizations
    FOR UPDATE USING (
        id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid() 
            AND role IN ('owner', 'admin') 
            AND status = 'active'
        )
    );

-- Organizations - Only owners can delete
CREATE POLICY "Only owners can delete organizations" ON organizations
    FOR DELETE USING (
        id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid() 
            AND role = 'owner' 
            AND status = 'active'
        )
    );

-- Organization Members - Members can view other members in their organization
CREATE POLICY "Members can view organization members" ON organization_members
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid() AND status = 'active'
        )
    );

-- Organization Members - Admins and owners can manage members
CREATE POLICY "Admins can manage organization members" ON organization_members
    FOR ALL USING (
        organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid() 
            AND role IN ('owner', 'admin') 
            AND status = 'active'
        )
    );

-- Organization Projects - Members can view projects based on visibility
CREATE POLICY "Members can view organization projects" ON organization_projects
    FOR SELECT USING (
        (visibility = 'organization' AND organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid() AND status = 'active'
        ))
        OR (visibility = 'private' AND (
            created_by = auth.uid() 
            OR project_manager_id = auth.uid()
            OR auth.uid() = ANY(team_members)
        ))
        OR visibility = 'public'
    );

-- Organization Tasks - Members can view tasks in their projects or assigned to them
CREATE POLICY "Members can view organization tasks" ON organization_tasks
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid() AND status = 'active'
        )
        AND (
            assignee_id = auth.uid()
            OR reporter_id = auth.uid()
            OR project_id IN (
                SELECT id FROM organization_projects 
                WHERE auth.uid() = ANY(team_members) 
                OR project_manager_id = auth.uid()
                OR created_by = auth.uid()
            )
        )
    );

-- Organization Channels - Members can view channels they belong to
CREATE POLICY "Members can view organization channels" ON organization_channels
    FOR SELECT USING (
        organization_id IN (
            SELECT organization_id FROM organization_members 
            WHERE user_id = auth.uid() AND status = 'active'
        )
        AND (
            auth.uid() = ANY(members)
            OR type = 'public'
        )
    );

-- Organization Messages - Members can view messages in channels they have access to
CREATE POLICY "Members can view organization messages" ON organization_messages
    FOR SELECT USING (
        channel_id IN (
            SELECT id FROM organization_channels 
            WHERE organization_id IN (
                SELECT organization_id FROM organization_members 
                WHERE user_id = auth.uid() AND status = 'active'
            )
            AND (
                auth.uid() = ANY(members)
                OR type = 'public'
            )
        )
    );

-- ====================================
-- FUNCTIONS FOR STATS UPDATES
-- ====================================

-- Function to update organization stats when members change
CREATE OR REPLACE FUNCTION update_organization_member_stats()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        UPDATE organizations 
        SET stats = jsonb_set(
            stats, 
            '{total_members}', 
            (
                SELECT COUNT(*)::text::jsonb 
                FROM organization_members 
                WHERE organization_id = NEW.organization_id 
                AND status = 'active'
            )
        )
        WHERE id = NEW.organization_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE organizations 
        SET stats = jsonb_set(
            stats, 
            '{total_members}', 
            (
                SELECT COUNT(*)::text::jsonb 
                FROM organization_members 
                WHERE organization_id = OLD.organization_id 
                AND status = 'active'
            )
        )
        WHERE id = OLD.organization_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to update project stats when tasks change
CREATE OR REPLACE FUNCTION update_project_task_stats()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        UPDATE organization_projects 
        SET 
            tasks_total = (
                SELECT COUNT(*) 
                FROM organization_tasks 
                WHERE project_id = NEW.project_id
            ),
            tasks_completed = (
                SELECT COUNT(*) 
                FROM organization_tasks 
                WHERE project_id = NEW.project_id 
                AND status = 'done'
            )
        WHERE id = NEW.project_id;
        
        -- Update progress percentage
        UPDATE organization_projects 
        SET progress_percentage = CASE 
            WHEN tasks_total = 0 THEN 0
            ELSE (tasks_completed * 100 / tasks_total)
        END
        WHERE id = NEW.project_id;
        
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE organization_projects 
        SET 
            tasks_total = (
                SELECT COUNT(*) 
                FROM organization_tasks 
                WHERE project_id = OLD.project_id
            ),
            tasks_completed = (
                SELECT COUNT(*) 
                FROM organization_tasks 
                WHERE project_id = OLD.project_id 
                AND status = 'done'
            )
        WHERE id = OLD.project_id;
        
        -- Update progress percentage
        UPDATE organization_projects 
        SET progress_percentage = CASE 
            WHEN tasks_total = 0 THEN 0
            ELSE (tasks_completed * 100 / tasks_total)
        END
        WHERE id = OLD.project_id;
        
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to update channel message count
CREATE OR REPLACE FUNCTION update_channel_message_stats()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE organization_channels 
        SET 
            message_count = message_count + 1,
            last_activity_at = NEW.created_at
        WHERE id = NEW.channel_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE organization_channels 
        SET message_count = message_count - 1
        WHERE id = OLD.channel_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- ====================================
-- APPLY TRIGGERS
-- ====================================

CREATE TRIGGER update_org_member_stats 
    AFTER INSERT OR UPDATE OR DELETE ON organization_members 
    FOR EACH ROW EXECUTE FUNCTION update_organization_member_stats();

CREATE TRIGGER update_project_task_stats 
    AFTER INSERT OR UPDATE OR DELETE ON organization_tasks 
    FOR EACH ROW EXECUTE FUNCTION update_project_task_stats();

CREATE TRIGGER update_channel_message_stats 
    AFTER INSERT OR DELETE ON organization_messages 
    FOR EACH ROW EXECUTE FUNCTION update_channel_message_stats();

-- ====================================
-- SEED DATA (Optional)
-- ====================================

-- Default subscription limits for different tiers
INSERT INTO organization_subscriptions (organization_id, tier, limits) VALUES 
    -- This will be handled by application logic when organizations are created
    -- Just showing the structure here
ON CONFLICT DO NOTHING;

-- ====================================
-- COMMENTS FOR DOCUMENTATION
-- ====================================

COMMENT ON TABLE organizations IS 'Main organizations table storing company/team information';
COMMENT ON TABLE organization_members IS 'Members of organizations with roles and permissions';
COMMENT ON TABLE organization_invitations IS 'Pending invitations to join organizations';
COMMENT ON TABLE organization_projects IS 'Projects within organizations';
COMMENT ON TABLE organization_tasks IS 'Tasks within organization projects';
COMMENT ON TABLE task_time_entries IS 'Time tracking entries for tasks';
COMMENT ON TABLE organization_channels IS 'Chat channels for team communication';
COMMENT ON TABLE organization_messages IS 'Messages within organization channels';
COMMENT ON TABLE organization_subscriptions IS 'Subscription and billing information';
COMMENT ON TABLE organization_analytics IS 'Daily analytics data for organizations';

-- ====================================
-- GRANT PERMISSIONS
-- ====================================

-- Grant appropriate permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated; 