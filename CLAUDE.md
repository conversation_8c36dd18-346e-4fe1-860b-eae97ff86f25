# HielTech Project Memory

## Project Overview
HielTech is a comprehensive Next.js application built with TypeScript, featuring a modern web platform with multiple functionalities including:

- **Business Portfolio**: Showcasing projects and services
- **Blog Management**: Full-featured CMS with rich text editing
- **HielLinks Service**: Link-in-bio functionality similar to Linktree
- **Team Management**: Applications, chat system, and member management
- **Admin Dashboard**: Complete administrative interface
- **Project Management**: Portfolio management with detailed project pages

## Architecture & Stack

### Frontend
- **Framework**: Next.js 15.3.3 with App Router
- **Language**: TypeScript
- **Styling**: TailwindCSS
- **UI Components**: Custom components with Framer Motion animations
- **Rich Text Editor**: TipTap for blog content editing
- **Icons**: React Icons

### Backend & Database
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth with PKCE flow
- **Storage**: Supabase Storage for media files
- **Real-time**: Supabase Realtime for chat functionality

### Key Dependencies
- `@supabase/supabase-js`: Database and auth client
- `@tiptap/*`: Rich text editor components
- `framer-motion`: Animations
- `next-themes`: Theme management
- `highlight.js` & `lowlight`: Code syntax highlighting

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── (pages)/           # Route groups
│   │   ├── admin/         # Admin dashboard
│   │   ├── blog/          # Blog system
│   │   ├── hielLinks/     # Link-in-bio service
│   │   ├── profile/       # User profiles
│   │   ├── projects/      # Portfolio projects
│   │   └── team/          # Team applications
│   ├── globals.css        # Global styles
│   └── layout.tsx         # Root layout
├── components/            # React components
│   ├── admin/            # Admin-specific components
│   ├── auth/             # Authentication components
│   ├── blog/             # Blog-related components
│   ├── chat/             # Chat system components
│   ├── hiellinks/        # HielLinks service components
│   ├── layout/           # Layout components
│   └── ui/               # Reusable UI components
├── lib/                  # Utility libraries
│   ├── auth/             # Authentication context
│   ├── security/         # Security utilities
│   ├── services/         # Business logic services
│   └── supabase.ts       # Supabase client & database helpers
└── middleware.ts         # Next.js middleware
```

## Key Features

### 1. Supabase Integration
- **Location**: `src/lib/supabase.ts`
- Comprehensive database helper functions
- TypeScript interfaces for all database entities
- Error handling and retry logic
- Session management with timeout handling

### 2. Admin Dashboard
- **Location**: `src/app/(pages)/admin/`
- Blog management with rich text editor
- Team application review and management
- Project portfolio management
- HielLinks profile management
- Chat system for team communication

### 3. HielLinks Service
- **Location**: `src/components/hiellinks/`
- Link-in-bio functionality
- QR code generation
- Analytics tracking
- Custom theming and branding

### 4. Chat System
- **Location**: `src/components/chat/`
- Real-time messaging using Supabase Realtime
- Application-based chat rooms
- Security and moderation features
- File sharing capabilities

### 5. Blog Management
- **Location**: `src/components/blog/`
- Rich text editor with TipTap
- Category and tag management
- Multiple blog post templates
- SEO optimization features
- Social sharing functionality

## Database Schema

### Core Tables
- `profiles`: User profiles with role-based access
- `tasks`: Task management system
- `inquiries`: Contact form submissions
- `team_applications`: Job application management
- `team_members`: Active team member profiles

### Blog System
- `blog_posts`: Blog content with metadata
- `blog_categories`: Content categorization
- `blog_tags`: Content tagging system
- `blog_media`: Media asset management
- `blog_post_tags`: Many-to-many relationships

### HielLinks Service
- `hiel_profiles`: Link-in-bio profiles
- `hiel_links`: Individual links within profiles
- `hiel_analytics`: Usage tracking and analytics
- `hiel_settings`: User subscription and limits

### Chat System
- `chat_rooms`: Chat room management
- `chat_messages`: Message storage
- `chat_room_participants`: Room membership
- `chat_permissions`: Access control

### Projects
- `projects`: Portfolio project management

## Authentication & Security

### Authentication Flow
- Supabase Auth with PKCE (Proof Key for Code Exchange)
- Email confirmation required
- Session persistence with local storage
- Automatic token refresh
- Admin role detection (`<EMAIL>`)

### Security Features
- **Location**: `src/lib/security/chatSecurity.ts`
- Rate limiting for chat messages
- Content moderation and sanitization
- Permission validation
- Action logging

## Development Commands
- `npm run dev`: Start development server
- `npm run build`: Build for production
- `npm run start`: Start production server
- `npm run lint`: Run ESLint

## Environment Variables (Required)
- `NEXT_PUBLIC_SUPABASE_URL`: Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Supabase anonymous key

## Key Configuration Files
- `next.config.ts`: Next.js configuration
- `tailwind.config.js`: TailwindCSS configuration
- `tsconfig.json`: TypeScript configuration
- `eslint.config.mjs`: ESLint configuration

## Admin Access
- Admin email: `<EMAIL>`
- Admin users have access to full dashboard functionality
- Role-based access control throughout the application

## Recent Implementations
Based on recent commits and documentation files:
1. Enhanced session management and timeout handling
2. Comprehensive admin dashboard with project management
3. Team application workflow with chat integration
4. Enhanced HielLinks features with analytics
5. Email confirmation system setup
6. Comprehensive analysis and testing systems

## Development Notes
- Uses App Router (not Pages Router)
- TypeScript strict mode enabled
- Comprehensive error handling throughout
- Responsive design with mobile-first approach
- Dark/light theme support
- SEO optimized with proper meta tags

## Testing
- Jest configuration present
- Test files in `src/__tests__/`
- E2E testing for team application workflow
- Chat integration and security testing

This project represents a full-stack application with enterprise-level features including real-time communication, content management, user authentication, and comprehensive admin functionality.