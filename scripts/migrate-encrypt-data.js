#!/usr/bin/env node
/**
 * Data Migration Script for Encryption
 * This script migrates existing unencrypted data to encrypted format
 * 
 * Usage: node scripts/migrate-encrypt-data.js
 */

const { createClient } = require('@supabase/supabase-js');
const { config } = require('dotenv');
const { webcrypto } = require('crypto');

// Polyfill for Node.js crypto
if (!global.crypto) {
  global.crypto = webcrypto;
}

// Load environment variables
config({ path: '.env.local' });

// Create Supabase client with service role key for database access
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Simple encryption service implementation for migration
class MigrationEncryptionService {
  static keyCache = new Map();

  static async getEncryptionKey(keyName) {
    if (this.keyCache.has(keyName)) {
      return this.keyCache.get(keyName);
    }

    try {
      const { data, error } = await supabase.rpc('get_encryption_key', {
        key_name: keyName
      });

      if (error) {
        throw new Error(`Failed to retrieve encryption key: ${error.message}`);
      }

      if (!data) {
        throw new Error(`Encryption key '${keyName}' not found`);
      }

      // Convert base64 key to CryptoKey
      const keyBuffer = Buffer.from(data, 'base64');
      const key = await crypto.subtle.importKey(
        'raw',
        keyBuffer,
        { name: 'AES-GCM' },
        false,
        ['encrypt', 'decrypt']
      );

      this.keyCache.set(keyName, key);
      return key;
    } catch (error) {
      console.error('Error retrieving encryption key:', error);
      throw new Error('Failed to retrieve encryption key');
    }
  }

  static async encrypt(data, keyName) {
    try {
      if (!data) return data;

      const key = await this.getEncryptionKey(keyName);
      const encoder = new TextEncoder();
      const dataBuffer = encoder.encode(data);

      // Generate random IV
      const iv = crypto.getRandomValues(new Uint8Array(12));

      // Encrypt the data
      const encrypted = await crypto.subtle.encrypt(
        {
          name: 'AES-GCM',
          iv: iv,
        },
        key,
        dataBuffer
      );

      // Combine IV and encrypted data
      const combined = new Uint8Array(iv.length + encrypted.byteLength);
      combined.set(iv);
      combined.set(new Uint8Array(encrypted), iv.length);

      // Return as base64 string
      return Buffer.from(combined).toString('base64');
    } catch (error) {
      console.error('Encryption error:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  static async encryptFields(data, fieldsToEncrypt, keyName) {
    const encrypted_fields = {};

    for (const field of fieldsToEncrypt) {
      const value = data[field];
      if (value && typeof value === 'string') {
        encrypted_fields[field] = await this.encrypt(value, keyName);
      }
    }

    return { encrypted_fields };
  }
}

// Migration functions
async function migrateProfiles() {
  console.log('🔐 Starting profile encryption migration...');
  
  const batchSize = 10;
  let processed = 0;
  let errors = 0;

  while (true) {
    // Get unencrypted profiles
    const { data: profiles, error } = await supabase
      .from('profiles')
      .select('*')
      .or('encrypted_fields.is.null,encrypted_fields.eq.{}')
      .limit(batchSize);

    if (error) {
      console.error('Error fetching profiles:', error);
      break;
    }

    if (!profiles || profiles.length === 0) {
      break;
    }

    console.log(`Processing batch of ${profiles.length} profiles...`);

    for (const profile of profiles) {
      try {
        const fieldsToEncrypt = ['email', 'display_name', 'bio', 'company', 'position'];
        const encryptedData = await MigrationEncryptionService.encryptFields(
          profile,
          fieldsToEncrypt,
          'profile_encryption_key'
        );

        const { error: updateError } = await supabase
          .from('profiles')
          .update({
            encrypted_fields: encryptedData.encrypted_fields,
            encryption_version: 1
          })
          .eq('id', profile.id);

        if (updateError) {
          console.error(`Failed to update profile ${profile.id}:`, updateError);
          errors++;
        } else {
          processed++;
          console.log(`✅ Encrypted profile ${profile.id}`);
        }
      } catch (error) {
        console.error(`Failed to encrypt profile ${profile.id}:`, error);
        errors++;
      }
    }

    // Small delay to avoid overwhelming the database
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  console.log(`📊 Profile migration complete: ${processed} processed, ${errors} errors`);
}

async function migrateChatMessages() {
  console.log('💬 Starting chat message encryption migration...');
  
  const batchSize = 20;
  let processed = 0;
  let errors = 0;

  while (true) {
    // Get unencrypted messages
    const { data: messages, error } = await supabase
      .from('chat_messages')
      .select('*')
      .or('is_encrypted.is.null,is_encrypted.eq.false')
      .limit(batchSize);

    if (error) {
      console.error('Error fetching messages:', error);
      break;
    }

    if (!messages || messages.length === 0) {
      break;
    }

    console.log(`Processing batch of ${messages.length} messages...`);

    for (const message of messages) {
      try {
        if (message.message) {
          const encryptedMessage = await MigrationEncryptionService.encrypt(
            message.message,
            'chat_encryption_key'
          );

          const { error: updateError } = await supabase
            .from('chat_messages')
            .update({
              message: encryptedMessage,
              is_encrypted: true,
              encryption_key_id: 'chat_encryption_key'
            })
            .eq('id', message.id);

          if (updateError) {
            console.error(`Failed to update message ${message.id}:`, updateError);
            errors++;
          } else {
            processed++;
            console.log(`✅ Encrypted message ${message.id}`);
          }
        }
      } catch (error) {
        console.error(`Failed to encrypt message ${message.id}:`, error);
        errors++;
      }
    }

    await new Promise(resolve => setTimeout(resolve, 100));
  }

  console.log(`📊 Chat message migration complete: ${processed} processed, ${errors} errors`);
}

async function migrateInquiries() {
  console.log('📝 Starting inquiry encryption migration...');
  
  const batchSize = 10;
  let processed = 0;
  let errors = 0;

  while (true) {
    const { data: inquiries, error } = await supabase
      .from('inquiries')
      .select('*')
      .or('encrypted_fields.is.null,encrypted_fields.eq.{}')
      .limit(batchSize);

    if (error) {
      console.error('Error fetching inquiries:', error);
      break;
    }

    if (!inquiries || inquiries.length === 0) {
      break;
    }

    console.log(`Processing batch of ${inquiries.length} inquiries...`);

    for (const inquiry of inquiries) {
      try {
        const fieldsToEncrypt = ['email', 'phone', 'message', 'company'];
        const encryptedData = await MigrationEncryptionService.encryptFields(
          inquiry,
          fieldsToEncrypt,
          'profile_encryption_key'
        );

        const { error: updateError } = await supabase
          .from('inquiries')
          .update({
            encrypted_fields: encryptedData.encrypted_fields,
            encryption_version: 1
          })
          .eq('id', inquiry.id);

        if (updateError) {
          console.error(`Failed to update inquiry ${inquiry.id}:`, updateError);
          errors++;
        } else {
          processed++;
          console.log(`✅ Encrypted inquiry ${inquiry.id}`);
        }
      } catch (error) {
        console.error(`Failed to encrypt inquiry ${inquiry.id}:`, error);
        errors++;
      }
    }

    await new Promise(resolve => setTimeout(resolve, 100));
  }

  console.log(`📊 Inquiry migration complete: ${processed} processed, ${errors} errors`);
}

async function migrateTeamApplications() {
  console.log('👥 Starting team application encryption migration...');
  
  const batchSize = 10;
  let processed = 0;
  let errors = 0;

  while (true) {
    const { data: applications, error } = await supabase
      .from('team_applications')
      .select('*')
      .or('encrypted_fields.is.null,encrypted_fields.eq.{}')
      .limit(batchSize);

    if (error) {
      console.error('Error fetching team applications:', error);
      break;
    }

    if (!applications || applications.length === 0) {
      break;
    }

    console.log(`Processing batch of ${applications.length} applications...`);

    for (const application of applications) {
      try {
        const fieldsToEncrypt = ['email', 'phone', 'motivation', 'additional_info'];
        const encryptedData = await MigrationEncryptionService.encryptFields(
          application,
          fieldsToEncrypt,
          'profile_encryption_key'
        );

        const { error: updateError } = await supabase
          .from('team_applications')
          .update({
            encrypted_fields: encryptedData.encrypted_fields,
            encryption_version: 1
          })
          .eq('id', application.id);

        if (updateError) {
          console.error(`Failed to update application ${application.id}:`, updateError);
          errors++;
        } else {
          processed++;
          console.log(`✅ Encrypted application ${application.id}`);
        }
      } catch (error) {
        console.error(`Failed to encrypt application ${application.id}:`, error);
        errors++;
      }
    }

    await new Promise(resolve => setTimeout(resolve, 100));
  }

  console.log(`📊 Team application migration complete: ${processed} processed, ${errors} errors`);
}

async function migrateHielProfiles() {
  console.log('🔗 Starting HielLinks profile encryption migration...');
  
  const batchSize = 10;
  let processed = 0;
  let errors = 0;

  while (true) {
    const { data: profiles, error } = await supabase
      .from('hiel_profiles')
      .select('*')
      .or('encrypted_fields.is.null,encrypted_fields.eq.{}')
      .limit(batchSize);

    if (error) {
      console.error('Error fetching hiel profiles:', error);
      break;
    }

    if (!profiles || profiles.length === 0) {
      break;
    }

    console.log(`Processing batch of ${profiles.length} hiel profiles...`);

    for (const profile of profiles) {
      try {
        const fieldsToEncrypt = ['business_name', 'description', 'phone', 'email'];
        const encryptedData = await MigrationEncryptionService.encryptFields(
          profile,
          fieldsToEncrypt,
          'profile_encryption_key'
        );

        const { error: updateError } = await supabase
          .from('hiel_profiles')
          .update({
            encrypted_fields: encryptedData.encrypted_fields,
            encryption_version: 1
          })
          .eq('id', profile.id);

        if (updateError) {
          console.error(`Failed to update hiel profile ${profile.id}:`, updateError);
          errors++;
        } else {
          processed++;
          console.log(`✅ Encrypted hiel profile ${profile.id}`);
        }
      } catch (error) {
        console.error(`Failed to encrypt hiel profile ${profile.id}:`, error);
        errors++;
      }
    }

    await new Promise(resolve => setTimeout(resolve, 100));
  }

  console.log(`📊 HielLinks profile migration complete: ${processed} processed, ${errors} errors`);
}

async function hashAnalyticsIPs() {
  console.log('📊 Starting analytics IP hashing migration...');
  
  const batchSize = 50;
  let processed = 0;
  let errors = 0;

  while (true) {
    const { data: analytics, error } = await supabase
      .from('hiel_analytics')
      .select('*')
      .or('is_ip_encrypted.is.null,is_ip_encrypted.eq.false')
      .limit(batchSize);

    if (error) {
      console.error('Error fetching analytics:', error);
      break;
    }

    if (!analytics || analytics.length === 0) {
      break;
    }

    console.log(`Processing batch of ${analytics.length} analytics records...`);

    for (const record of analytics) {
      try {
        if (record.visitor_ip) {
          // Hash the IP address for privacy
          const encoder = new TextEncoder();
          const dataBuffer = encoder.encode(record.visitor_ip);
          const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
          const hashArray = new Uint8Array(hashBuffer);
          const hashedIP = Buffer.from(hashArray).toString('base64');

          const { error: updateError } = await supabase
            .from('hiel_analytics')
            .update({
              visitor_ip_hash: hashedIP,
              is_ip_encrypted: true
            })
            .eq('id', record.id);

          if (updateError) {
            console.error(`Failed to update analytics ${record.id}:`, updateError);
            errors++;
          } else {
            processed++;
            console.log(`✅ Hashed IP for analytics ${record.id}`);
          }
        }
      } catch (error) {
        console.error(`Failed to hash IP for analytics ${record.id}:`, error);
        errors++;
      }
    }

    await new Promise(resolve => setTimeout(resolve, 50));
  }

  console.log(`📊 Analytics IP hashing complete: ${processed} processed, ${errors} errors`);
}

// Main execution
async function main() {
  console.log('🚀 Starting comprehensive data encryption migration...');
  console.log('This will encrypt all existing sensitive data in the database.\n');

  try {
    // Run all migrations
    await migrateProfiles();
    await migrateChatMessages();
    await migrateInquiries();
    await migrateTeamApplications();
    await migrateHielProfiles();
    await hashAnalyticsIPs();

    console.log('\n🎉 Data encryption migration completed successfully!');
    console.log('All sensitive data has been encrypted with AES-256-GCM encryption.');
    console.log('\nNext steps:');
    console.log('1. Test the application to ensure encrypted data is properly decrypted');
    console.log('2. Monitor performance and error logs');
    console.log('3. Consider removing plaintext columns after validation period');

  } catch (error) {
    console.error('\n❌ Migration failed:', error);
    process.exit(1);
  }
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  console.error('Uncaught exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (error) => {
  console.error('Unhandled rejection:', error);
  process.exit(1);
});

// Run the migration
if (require.main === module) {
  main();
} 