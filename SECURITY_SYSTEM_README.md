# 🔐 Advanced Security System

A comprehensive security system with custom challenges and IP rate limiting for your Next.js application.

## 🚀 Features

### 🧩 Custom Security Challenges
- **Interactive Puzzle Challenges**: Drag-and-drop, slider, and pattern-matching puzzles
- **Pixel-Perfect Verification**: Users must move elements to specific positions
- **Multiple Challenge Types**: Randomized challenge selection for enhanced security
- **Configurable Difficulty**: Adjustable tolerance and attempt limits
- **Expiring Challenges**: Time-limited challenges to prevent abuse

### 🛡️ IP Rate Limiting
- **Endpoint-Specific Limits**: Different rate limits for login, signup, and API endpoints
- **Automatic IP Blocking**: Temporary blocks for rate limit violations
- **Configurable Windows**: Customizable time windows and block durations
- **Whitelist/Blacklist**: IP address management for trusted and blocked IPs

### 📊 Security Monitoring
- **Real-time Logging**: Comprehensive security event tracking
- **Admin Dashboard**: Visual monitoring and configuration interface
- **Security Analytics**: Success rates, blocked IPs, and violation statistics
- **Event Classification**: Severity-based event categorization

### ⚙️ Advanced Configuration
- **Dynamic Settings**: Runtime configuration without code changes
- **Admin Controls**: Granular security setting management
- **Export/Import**: Configuration backup and restoration
- **Validation**: Built-in configuration validation

## 📁 File Structure

```
src/
├── lib/security/
│   ├── challengeSystem.ts      # Core challenge logic
│   ├── rateLimiter.ts          # Rate limiting system
│   ├── securityConfig.ts       # Configuration management
│   └── chatSecurity.ts         # Existing chat security (enhanced)
├── components/
│   ├── auth/
│   │   ├── SecurityChallenge.tsx   # Interactive challenge UI
│   │   └── LoginForm.tsx           # Enhanced with security
│   └── admin/
│       └── SecurityDashboard.tsx   # Admin security interface
├── app/
│   ├── api/
│   │   ├── challenge/route.ts      # Challenge API endpoints
│   │   └── security/status/route.ts # Security status API
│   └── (pages)/admin/security/page.tsx # Admin security page
├── middleware.ts               # Enhanced with rate limiting
└── database/
    └── migrations/
        └── security_tables.sql # Database schema
```

## 🗄️ Database Schema

### Security Challenges Table
```sql
security_challenges (
  id UUID PRIMARY KEY,
  user_ip INET NOT NULL,
  challenge_type VARCHAR(50),
  challenge_data JSONB,
  solution_data JSONB,
  is_solved BOOLEAN DEFAULT FALSE,
  attempts INTEGER DEFAULT 0,
  max_attempts INTEGER DEFAULT 3,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
```

### Rate Limits Table
```sql
rate_limits (
  id UUID PRIMARY KEY,
  user_ip INET NOT NULL,
  endpoint VARCHAR(255),
  request_count INTEGER DEFAULT 1,
  window_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  window_duration_minutes INTEGER DEFAULT 60,
  max_requests INTEGER DEFAULT 10,
  is_blocked BOOLEAN DEFAULT FALSE,
  blocked_until TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
```

### Security Logs Table
```sql
security_logs (
  id UUID PRIMARY KEY,
  user_ip INET NOT NULL,
  user_id UUID REFERENCES auth.users(id),
  event_type VARCHAR(100),
  event_data JSONB,
  severity VARCHAR(20) DEFAULT 'low',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
```

## 🔧 Configuration

### Default Rate Limits
- **Login**: 5 requests per 15 minutes, 30-minute block
- **Signup**: 3 requests per 60 minutes, 60-minute block
- **API**: 10 requests per 10 minutes, 20-minute block

### Challenge Settings
- **Expiry**: 10 minutes
- **Max Attempts**: 3
- **Tolerance**: 15 pixels
- **Types**: Drag-drop, slider, pattern-match

## 🚀 Usage

### 1. Database Setup
Run the SQL migration in your Supabase dashboard:
```sql
-- Copy and paste the contents of security_tables.sql
```

### 2. Environment Variables
No additional environment variables required - uses existing Supabase configuration.

### 3. Admin Access
Navigate to `/admin/security` (admin access required) to:
- Monitor security statistics
- Configure rate limits
- Manage IP whitelist/blacklist
- View security logs

### 4. User Experience
Users will encounter:
1. **Security Challenge**: Interactive puzzle before login
2. **Rate Limiting**: Automatic protection against abuse
3. **Smooth UX**: Seamless integration with existing auth flow

## 🔒 Security Features

### Challenge System
- **Randomized Puzzles**: Different challenge types prevent automation
- **Pixel Precision**: Exact positioning required for verification
- **Time Limits**: Challenges expire to prevent prolonged attacks
- **Attempt Tracking**: Limited attempts with progressive difficulty

### Rate Limiting
- **IP-Based Protection**: Tracks requests per IP address
- **Sliding Windows**: Time-based request counting
- **Automatic Recovery**: Blocks expire automatically
- **Granular Control**: Different limits per endpoint

### Monitoring
- **Event Logging**: All security events tracked
- **Real-time Alerts**: Immediate notification of violations
- **Analytics Dashboard**: Visual security metrics
- **Audit Trail**: Comprehensive security history

## 🛠️ API Endpoints

### Challenge Management
- `GET /api/challenge` - Generate new challenge
- `POST /api/challenge` - Verify challenge solution
- `DELETE /api/challenge` - Cleanup expired challenges

### Security Status
- `GET /api/security/status` - Get current security status

## 🎨 Customization

### Challenge Types
Extend `challengeSystem.ts` to add new puzzle types:
```typescript
private static generatePuzzleData(type: string) {
  switch (type) {
    case 'your_custom_type':
      return { /* custom logic */ };
  }
}
```

### Rate Limit Rules
Modify `rateLimiter.ts` for custom rate limiting:
```typescript
private static readonly DEFAULT_CONFIGS = {
  '/your-endpoint': {
    maxRequests: 10,
    windowMinutes: 5,
    blockDurationMinutes: 15
  }
};
```

## 🔍 Monitoring

### Admin Dashboard
Access comprehensive security monitoring at `/admin/security`:
- Real-time security statistics
- Configuration management
- IP address management
- Security event logs

### Security Metrics
- Challenge success/failure rates
- Rate limit violations
- Blocked IP addresses
- Recent security events

## 🚨 Troubleshooting

### Common Issues

1. **Challenges Not Loading**
   - Check API route accessibility
   - Verify database connection
   - Check browser console for errors

2. **Rate Limiting Too Strict**
   - Adjust limits in SecurityConfigManager
   - Check IP whitelist settings
   - Review middleware configuration

3. **Database Errors**
   - Ensure tables are created
   - Check RLS policies
   - Verify Supabase connection

### Debug Mode
Enable detailed logging by setting `NODE_ENV=development`.

## 🔄 Updates and Maintenance

### Regular Tasks
- Monitor security logs for patterns
- Update rate limits based on usage
- Clean up expired challenges
- Review IP whitelist/blacklist

### Performance Optimization
- Index optimization for large datasets
- Cleanup old security logs
- Monitor database performance

## 🤝 Contributing

When extending the security system:
1. Follow existing patterns
2. Add comprehensive logging
3. Include configuration options
4. Update documentation
5. Test thoroughly

## 📝 License

This security system is part of your Next.js application and follows the same licensing terms.

---

**🔐 Your application is now protected with enterprise-grade security features!**
