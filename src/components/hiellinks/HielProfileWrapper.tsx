'use client';

import { useEffect, useState, useCallback, useMemo, useRef } from 'react';
import { db, HielProfile } from '@/lib/supabase';
import HielPublicProfile from './HielPublicProfile';

interface HielProfileWrapperProps {
  profile: HielProfile;
}

// Create a cache for analytics tracking to prevent duplicate calls
const analyticsCache = new Map<string, number>();
const ANALYTICS_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export default function HielProfileWrapper({ profile }: HielProfileWrapperProps) {
  const [isTracking, setIsTracking] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const hasTrackedView = useRef(false);
  const isWindowVisible = useRef(typeof window !== 'undefined' ? !document.hidden : true);

  // Memoize profile data to prevent unnecessary re-renders
  const memoizedProfile = useMemo(() => profile, [profile]);

  // Handle window visibility changes to prevent unwanted analytics
  useEffect(() => {
    const handleVisibilityChange = () => {
      isWindowVisible.current = !document.hidden;
      
      // Don't track analytics when window becomes visible again
      if (isWindowVisible.current) {
        console.log('Window became visible - not triggering analytics');
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, []);

  // Optimized analytics tracking with caching and retry logic
  const trackView = useCallback(async (retryAttempt = 0) => {
    // Prevent duplicate tracking calls
    if (hasTrackedView.current || isTracking) {
      console.log('View already tracked or tracking in progress');
      return;
    }

    const cacheKey = `view_${profile.id}`;
    const now = Date.now();
    const lastTracked = analyticsCache.get(cacheKey);

    // Check if we've already tracked this view recently
    if (lastTracked && (now - lastTracked) < ANALYTICS_CACHE_DURATION) {
      console.log('View already tracked recently, skipping');
      hasTrackedView.current = true;
      return;
    }

    // Only track if window is visible
    if (!isWindowVisible.current) {
      console.log('Window not visible, skipping analytics');
      return;
    }

    setIsTracking(true);
    setError(null);

    try {
      // Get user's location and device info for enhanced analytics
      const userAgent = navigator.userAgent;
      const referrer = document.referrer || undefined;
      
      // Batch the analytics calls for better performance
      const analyticsPromise = db.trackHielAnalytics({
        profile_id: profile.id,
        event_type: 'profile_view',
        visitor_ip: undefined, // Will be handled by server if needed
        user_agent: userAgent,
        referrer: referrer,
        country: undefined, // Could be populated with IP geolocation
        device_type: getDeviceType(userAgent),
        browser: getBrowserName(userAgent),
      });

      const incrementPromise = db.incrementProfileView(profile.id);

      // Execute both operations in parallel
      await Promise.all([analyticsPromise, incrementPromise]);

      // Cache the successful tracking
      analyticsCache.set(cacheKey, now);
      hasTrackedView.current = true;
      console.log('Profile view tracked successfully');

    } catch (error) {
      console.error('Error tracking profile view:', error);
      setError('Failed to track analytics');

      // Implement exponential backoff retry
      if (retryAttempt < 3) {
        const delay = Math.pow(2, retryAttempt) * 1000; // 1s, 2s, 4s
        setTimeout(() => {
          trackView(retryAttempt + 1);
        }, delay);
      }
    } finally {
      setIsTracking(false);
    }
  }, [profile.id, isTracking]);



  // Track view on mount with performance optimizations - only once
  useEffect(() => {
    // Use requestIdleCallback for non-critical analytics
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => trackView(), { timeout: 2000 });
    } else {
      // Fallback for browsers without requestIdleCallback
      setTimeout(() => trackView(), 100);
    }
    
    // Cleanup function to mark component as unmounted
    return () => {
      hasTrackedView.current = false;
    };
  }, [trackView]);

  // Cleanup analytics cache periodically
  useEffect(() => {
    const cleanup = setInterval(() => {
      const now = Date.now();
      for (const [key, timestamp] of analyticsCache.entries()) {
        if (now - timestamp > ANALYTICS_CACHE_DURATION) {
          analyticsCache.delete(key);
        }
      }
    }, ANALYTICS_CACHE_DURATION);

    return () => clearInterval(cleanup);
  }, []);

  return (
    <div className="hiel-profile-wrapper">
      {error && (
        <div className="fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg text-sm z-50 animate-fade-in">
          <div className="flex items-center space-x-2">
            <span>⚠️</span>
            <span>{error}</span>
            <button 
              onClick={() => setError(null)}
              className="ml-2 text-white hover:text-gray-200"
            >
              ×
            </button>
          </div>
        </div>
      )}
      
      <HielPublicProfile 
        profile={memoizedProfile} 
      />
      
      {/* Loading indicator for analytics */}
      {isTracking && (
        <div className="fixed bottom-4 right-4 bg-blue-500 text-white px-3 py-2 rounded-lg text-xs z-40 animate-pulse">
          📊 Tracking...
        </div>
      )}
    </div>
  );
}

// Utility functions for enhanced analytics
function getDeviceType(userAgent: string): string {
  if (/tablet|ipad|playbook|silk/i.test(userAgent)) {
    return 'tablet';
  }
  if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) {
    return 'mobile';
  }
  return 'desktop';
}

function getBrowserName(userAgent: string): string {
  if (userAgent.includes('Chrome')) return 'Chrome';
  if (userAgent.includes('Firefox')) return 'Firefox';
  if (userAgent.includes('Safari')) return 'Safari';
  if (userAgent.includes('Edge')) return 'Edge';
  if (userAgent.includes('Opera')) return 'Opera';
  return 'Unknown';
} 