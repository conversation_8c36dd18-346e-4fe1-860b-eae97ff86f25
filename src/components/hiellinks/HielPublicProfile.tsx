'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { HielProfile, HielLink, db } from '@/lib/supabase';
import { getPlatformIcon } from './PlatformIcons';

interface HielPublicProfileProps {
  profile: HielProfile;
}

export default function HielPublicProfile({ profile }: HielPublicProfileProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [showMap, setShowMap] = useState(false);

  useEffect(() => {
    // Track profile view
    if (profile.id) {
      db.incrementProfileView(profile.id);
    }
    setIsLoading(false);
  }, [profile.id]);

  const handleLinkClick = async (link: HielLink) => {
    // Track link click
    if (link.id) {
      await db.incrementLinkClick(link.id);
    }
    
    // Open link
    window.open(link.url, '_blank', 'noopener,noreferrer');
  };

  const toggleMap = () => {
    setShowMap(!showMap);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-100 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  const activeLinks = profile.links?.filter(link => link.is_active) || [];

  return (
    <div 
      className="min-h-screen relative"
      style={{
        backgroundColor: profile.theme_color || '#3B82F6',
        background: profile.background_image_url 
          ? `linear-gradient(rgba(0,0,0,0.6), rgba(0,0,0,0.6)), url(${profile.background_image_url})`
          : profile.theme_color || '#3B82F6',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundAttachment: 'fixed'
      }}
    >
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-md mx-auto">
          {/* Profile Header */}
          <div className="text-center mb-8">
            {/* Logo */}
            {profile.logo_url && (
              <div className="mb-6">
                <div className="w-24 h-24 mx-auto relative">
                  <Image
                    src={profile.logo_url}
                    alt={`${profile.business_name} logo`}
                    fill
                    className="rounded-full object-cover border-4 border-white shadow-lg"
                  />
                </div>
              </div>
            )}

            {/* Text Content with Background Overlay */}
            <div className="bg-black bg-opacity-40 backdrop-blur-sm rounded-2xl p-6 mx-4 shadow-xl border border-white border-opacity-20">
              {/* Business Name */}
              <h1 
                className="text-3xl font-bold mb-3 drop-shadow-lg"
                style={{ color: profile.text_color || '#FFFFFF' }}
              >
                {profile.business_name}
              </h1>

              {/* Description */}
              {profile.description && (
                <p 
                  className="text-lg opacity-95 mb-4 leading-relaxed drop-shadow-md"
                  style={{ color: profile.text_color || '#FFFFFF' }}
                >
                  {profile.description}
                </p>
              )}

              {/* Location - Now clickable to show map */}
              {profile.location && (
                <button
                  onClick={toggleMap}
                  className="inline-flex items-center justify-center bg-white bg-opacity-20 hover:bg-opacity-30 transition-all duration-300 cursor-pointer group px-4 py-2 rounded-full backdrop-blur-sm border border-white border-opacity-30"
                  style={{ color: profile.text_color || '#FFFFFF' }}
                >
                  <svg className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                  </svg>
                  <span className="text-sm font-medium drop-shadow-sm">{profile.location}</span>
                  <svg 
                    className={`w-4 h-4 ml-2 transition-transform duration-300 ${showMap ? 'rotate-180' : ''}`} 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
              )}
            </div>
          </div>

          {/* Embedded Map - Collapsible */}
          {profile.location && showMap && (
            <div className="mb-8 rounded-lg overflow-hidden shadow-lg transition-all duration-500 ease-in-out">
              <div className="bg-white bg-opacity-90 p-2">
                <iframe
                  src={`https://maps.google.com/maps?q=${encodeURIComponent(profile.location || '')}&output=embed&z=15`}
                  width="100%"
                  height="250"
                  style={{ border: 0 }}
                  allowFullScreen
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  className="w-full h-full rounded-md"
                  title={`Map showing location: ${profile.location || 'Business location'}`}
                />
              </div>
              <div className="bg-white bg-opacity-90 px-3 py-2 text-center">
                <p className="text-sm text-gray-700">
                  📍 {profile.location}
                </p>
                <button
                  onClick={() => profile.location && window.open(`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(profile.location)}`, '_blank')}
                  className="text-xs text-blue-600 hover:text-blue-800 underline mt-1"
                >
                  Open in Google Maps
                </button>
              </div>
            </div>
          )}

          {/* Links */}
          <div className="space-y-4">
            {activeLinks.map((link, index) => (
              <button
                key={link.id || index}
                onClick={() => handleLinkClick(link)}
                className="w-full p-4 bg-white bg-opacity-90 hover:bg-opacity-100 backdrop-blur-sm rounded-lg shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl group"
              >
                <div className="flex items-center space-x-4">
                  {/* Platform Icon */}
                  <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center">
                    {getPlatformIcon(link.platform || 'other', 'w-6 h-6')}
                  </div>
                  
                  {/* Link Content */}
                  <div className="flex-1 text-left">
                    <div className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                      {link.title}
                    </div>
                    {link.description && (
                      <div className="text-sm text-gray-600 mt-1">
                        {link.description}
                      </div>
                    )}
                  </div>
                  
                  {/* External Link Icon */}
                  <div className="flex-shrink-0">
                    <svg 
                      className="w-5 h-5 text-gray-400 group-hover:text-blue-600 transition-colors" 
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path 
                        strokeLinecap="round" 
                        strokeLinejoin="round" 
                        strokeWidth={2} 
                        d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" 
                      />
                    </svg>
                  </div>
                </div>
              </button>
            ))}

            {/* Contact Links (if any) */}
            {(profile.email || profile.phone || profile.website) && (
              <>
                <div className="my-6">
                  <div 
                    className="h-px w-full opacity-30"
                    style={{ backgroundColor: profile.text_color || '#FFFFFF' }}
                  ></div>
                </div>

                {/* Email */}
                {profile.email && (
                  <button
                    onClick={() => window.open(`mailto:${profile.email}`, '_self')}
                    className="w-full p-4 bg-white bg-opacity-90 hover:bg-opacity-100 backdrop-blur-sm rounded-lg shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl group"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center">
                        {getPlatformIcon('email', 'w-6 h-6')}
                      </div>
                      <div className="flex-1 text-left">
                        <div className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                          Email
                        </div>
                        <div className="text-sm text-gray-600">
                          {profile.email}
                        </div>
                      </div>
                      <div className="flex-shrink-0">
                        <svg className="w-5 h-5 text-gray-400 group-hover:text-blue-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                      </div>
                    </div>
                  </button>
                )}

                {/* Phone */}
                {profile.phone && (
                  <button
                    onClick={() => window.open(`tel:${profile.phone}`, '_self')}
                    className="w-full p-4 bg-white bg-opacity-90 hover:bg-opacity-100 backdrop-blur-sm rounded-lg shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl group"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center">
                        {getPlatformIcon('phone', 'w-6 h-6')}
                      </div>
                      <div className="flex-1 text-left">
                        <div className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                          Phone
                        </div>
                        <div className="text-sm text-gray-600">
                          {profile.phone}
                        </div>
                      </div>
                      <div className="flex-shrink-0">
                        <svg className="w-5 h-5 text-gray-400 group-hover:text-blue-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                      </div>
                    </div>
                  </button>
                )}

                {/* Website */}
                {profile.website && (
                  <button
                    onClick={() => profile.website && window.open(profile.website, '_blank', 'noopener,noreferrer')}
                    className="w-full p-4 bg-white bg-opacity-90 hover:bg-opacity-100 backdrop-blur-sm rounded-lg shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl group"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center">
                        {getPlatformIcon('website', 'w-6 h-6')}
                      </div>
                      <div className="flex-1 text-left">
                        <div className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                          Website
                        </div>
                        <div className="text-sm text-gray-600 truncate">
                          {profile.website?.replace(/^https?:\/\//, '') || ''}
                        </div>
                      </div>
                      <div className="flex-shrink-0">
                        <svg className="w-5 h-5 text-gray-400 group-hover:text-blue-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                      </div>
                    </div>
                  </button>
                )}
              </>
            )}

            {/* Empty State */}
            {activeLinks.length === 0 && !profile.email && !profile.phone && !profile.website && (
              <div className="text-center py-12">
                <div 
                  className="text-lg opacity-75"
                  style={{ color: profile.text_color || '#FFFFFF' }}
                >
                  No links available yet
                </div>
              </div>
            )}
          </div>

          {/* Powered by */}
          <div className="text-center mt-12">
            <p 
              className="text-sm opacity-60"
              style={{ color: profile.text_color || '#FFFFFF' }}
            >
              Powered by{' '}
              <a 
                href="https://hieltech.vercel.app" 
                target="_blank" 
                rel="noopener noreferrer"
                className="underline hover:opacity-80"
              >
                HielTech
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
