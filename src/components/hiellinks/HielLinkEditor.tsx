'use client';

import { useState, useEffect } from 'react';
import { HielLink } from '@/lib/supabase';
import { 
  getPlatformConfig, 
  getPlatformIcon, 
  detectPlatformFromUrl,
  PLATFORM_OPTIONS,
  LINK_TYPE_OPTIONS 
} from './PlatformIcons';

interface HielLinkEditorProps {
  link: Partial<HielLink>;
  onChange: (linkData: Partial<HielLink>) => void;
  onRemove: () => void;
}

export default function HielLinkEditor({ link, onChange, onRemove }: HielLinkEditorProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [detectedPlatform, setDetectedPlatform] = useState<string>('');

  // Auto-detect platform when URL changes
  useEffect(() => {
    if (link.url) {
      const detected = detectPlatformFromUrl(link.url);
      setDetectedPlatform(detected);
      
      // Auto-set platform if not manually selected
      if (!link.platform || link.platform === 'other') {
        onChange({ ...link, platform: detected as typeof link.platform });
      }
    }
  }, [link.url, link.platform, link, onChange]);

  // Get the current platform config for styling and placeholders
  const currentPlatform = link.platform || detectedPlatform || 'other';
  const platformConfig = getPlatformConfig(currentPlatform);

  const handleFieldChange = (field: keyof HielLink, value: string) => {
    onChange({ ...link, [field]: value });
  };

  const handleTypeChange = (type: string) => {
    onChange({ ...link, type: type as 'website' | 'social' | 'contact' | 'custom' });
  };

  const handlePlatformChange = (platform: string) => {
    const config = getPlatformConfig(platform);
    onChange({ 
      ...link, 
      platform: platform as typeof link.platform,
      // Auto-fill URL placeholder if URL is empty
      url: link.url || (config.placeholder ? config.placeholder : '')
    });
  };

  return (
    <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-800">
      {/* Header with icon, title, and controls */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3 flex-1">
          {/* Platform Icon */}
          <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center">
            {getPlatformIcon(currentPlatform, 'w-6 h-6')}
          </div>
          
          {/* Title Input */}
          <input
            type="text"
            value={link.title || ''}
            onChange={(e) => handleFieldChange('title', e.target.value)}
            placeholder={`${platformConfig.label} title`}
            className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
          />
          
          {/* Quick Toggle */}
          <button
            type="button"
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 p-1"
            title={isExpanded ? 'Collapse' : 'Expand'}
          >
            <svg 
              className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          
          {/* Remove Button */}
          <button
            type="button"
            onClick={onRemove}
            className="text-red-500 hover:text-red-700 p-1"
            title="Remove link"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      {/* URL Input - Always visible */}
      <div className="mb-3">
        <input
          type="url"
          value={link.url || ''}
          onChange={(e) => handleFieldChange('url', e.target.value)}
          placeholder={platformConfig.placeholder}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
        />
        {detectedPlatform && detectedPlatform !== currentPlatform && (
          <p className="mt-1 text-xs text-blue-600 dark:text-blue-400">
            💡 Detected: {getPlatformConfig(detectedPlatform).label}
          </p>
        )}
      </div>

      {/* Advanced Options - Collapsible */}
      {isExpanded && (
        <div className="space-y-3 pt-3 border-t border-gray-200 dark:border-gray-700">
          {/* Platform and Type Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Platform
              </label>
              <select
                value={link.platform || 'other'}
                onChange={(e) => handlePlatformChange(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
              >
                {PLATFORM_OPTIONS.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                Type
              </label>
              <select
                value={link.type || 'custom'}
                onChange={(e) => handleTypeChange(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm"
              >
                {LINK_TYPE_OPTIONS.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Description */}
          <div>
            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
              Description (Optional)
            </label>
            <textarea
              value={link.description || ''}
              onChange={(e) => handleFieldChange('description', e.target.value)}
              placeholder="Brief description of this link..."
              rows={2}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white text-sm resize-none"
            />
          </div>

          {/* Active Toggle */}
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id={`active-${link.id || 'new'}`}
              checked={link.is_active !== false}
              onChange={(e) => handleFieldChange('is_active', e.target.checked ? 'true' : 'false')}
              className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            />
            <label 
              htmlFor={`active-${link.id || 'new'}`}
              className="text-sm text-gray-700 dark:text-gray-300"
            >
              Active (visible on profile)
            </label>
          </div>
        </div>
      )}
    </div>
  );
}
