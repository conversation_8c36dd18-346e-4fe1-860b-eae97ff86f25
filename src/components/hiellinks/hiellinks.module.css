/* Custom animations for HielLinks profile */

.floatingBubbles {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1;
}

.bubble {
  position: absolute;
  border-radius: 50%;
  opacity: 0.1;
  animation: float 20s infinite ease-in-out;
}

.bubble:nth-child(1) {
  width: 40px;
  height: 40px;
  left: 10%;
  top: 60%;
  animation-delay: 0s;
  animation-duration: 15s;
}

.bubble:nth-child(2) {
  width: 20px;
  height: 20px;
  left: 20%;
  top: 80%;
  animation-delay: 2s;
  animation-duration: 18s;
}

.bubble:nth-child(3) {
  width: 60px;
  height: 60px;
  left: 35%;
  top: 40%;
  animation-delay: 4s;
  animation-duration: 22s;
}

.bubble:nth-child(4) {
  width: 80px;
  height: 80px;
  right: 15%;
  top: 20%;
  animation-delay: 6s;
  animation-duration: 25s;
}

.bubble:nth-child(5) {
  width: 30px;
  height: 30px;
  right: 25%;
  bottom: 30%;
  animation-delay: 8s;
  animation-duration: 20s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.1;
  }
  25% {
    transform: translateY(-20px) rotate(90deg);
    opacity: 0.2;
  }
  50% {
    transform: translateY(-40px) rotate(180deg);
    opacity: 0.1;
  }
  75% {
    transform: translateY(-20px) rotate(270deg);
    opacity: 0.2;
  }
}

.cardGlow {
  position: relative;
  overflow: hidden;
}

.cardGlow::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    from 0deg,
    transparent,
    rgba(var(--theme-color-rgb), 0.3),
    transparent,
    rgba(var(--theme-color-rgb), 0.3),
    transparent
  );
  animation: rotate 4s linear infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.cardGlow:hover::before {
  opacity: 1;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

.sparkle {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  animation: sparkle 2s infinite ease-in-out;
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

.linkHoverEffect {
  position: relative;
  overflow: hidden;
}

.linkHoverEffect::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(var(--theme-color-rgb), 0.2),
    transparent
  );
  transition: left 0.5s ease;
  z-index: 0;
}

.linkHoverEffect:hover::before {
  left: 100%;
}

.pulseGlow {
  animation: pulseGlow 3s infinite ease-in-out;
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(var(--theme-color-rgb), 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(var(--theme-color-rgb), 0.6);
  }
}

/* Glassmorphism effects */
.glass {
  backdrop-filter: blur(16px) saturate(180%);
  background-color: rgba(17, 25, 40, 0.75);
  border: 1px solid rgba(255, 255, 255, 0.125);
}

.glassLight {
  backdrop-filter: blur(16px) saturate(180%);
  background-color: rgba(255, 255, 255, 0.85);
  border: 1px solid rgba(209, 213, 219, 0.3);
}

/* Custom scrollbar for modal */
.customScrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(var(--theme-color-rgb), 0.5) transparent;
}

.customScrollbar::-webkit-scrollbar {
  width: 4px;
}

.customScrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.customScrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(var(--theme-color-rgb), 0.5);
  border-radius: 2px;
}

.customScrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(var(--theme-color-rgb), 0.8);
} 