import React from 'react';

export type TextOverlayStyle = 'glassmorphism' | 'solid' | 'gradient' | 'minimal' | 'card';

interface TextOverlayProps {
  children: React.ReactNode;
  style?: TextOverlayStyle;
  className?: string;
}

export const TextOverlay: React.FC<TextOverlayProps> = ({ 
  children, 
  style = 'glassmorphism', 
  className = '' 
}) => {
  const baseStyles = "rounded-2xl p-6 mx-4 shadow-xl transition-all duration-300";
  
  const styleVariants = {
    // Current glassmorphism style
    glassmorphism: `${baseStyles} bg-black bg-opacity-40 backdrop-blur-sm border border-white border-opacity-20`,
    
    // Solid dark background
    solid: `${baseStyles} bg-gray-900 bg-opacity-80`,
    
    // Gradient overlay
    gradient: `${baseStyles} bg-gradient-to-b from-black/50 via-black/40 to-black/60 backdrop-blur-sm`,
    
    // Minimal with just text shadow
    minimal: "p-4",
    
    // Card-like appearance
    card: `${baseStyles} bg-white bg-opacity-95 text-gray-900 shadow-2xl border border-gray-200`,
  };

  return (
    <div className={`${styleVariants[style]} ${className}`}>
      {children}
    </div>
  );
};

// Text style helpers for different overlay types
export const getTextStyles = (overlayStyle: TextOverlayStyle) => {
  const baseTextStyles = {
    title: "text-3xl font-bold mb-3",
    description: "text-lg mb-4 leading-relaxed",
    location: "text-sm font-medium"
  };

  switch (overlayStyle) {
    case 'card':
      return {
        title: `${baseTextStyles.title} text-gray-900`,
        description: `${baseTextStyles.description} text-gray-700 opacity-90`,
        location: `${baseTextStyles.location} text-gray-600`
      };
    case 'minimal':
      return {
        title: `${baseTextStyles.title} drop-shadow-[2px_2px_4px_rgba(0,0,0,0.8)]`,
        description: `${baseTextStyles.description} drop-shadow-[1px_1px_3px_rgba(0,0,0,0.7)]`,
        location: `${baseTextStyles.location} drop-shadow-[1px_1px_2px_rgba(0,0,0,0.6)]`
      };
    default:
      return {
        title: `${baseTextStyles.title} drop-shadow-lg`,
        description: `${baseTextStyles.description} opacity-95 drop-shadow-md`,
        location: `${baseTextStyles.location} drop-shadow-sm`
      };
  }
};

// Location button styles for different overlay types
export const getLocationButtonStyles = (overlayStyle: TextOverlayStyle) => {
  const baseButtonStyles = "inline-flex items-center justify-center transition-all duration-300 cursor-pointer group px-4 py-2 rounded-full";
  
  switch (overlayStyle) {
    case 'card':
      return `${baseButtonStyles} bg-blue-500 hover:bg-blue-600 text-white shadow-md`;
    case 'minimal':
      return `${baseButtonStyles} bg-black bg-opacity-50 hover:bg-opacity-70 backdrop-blur-sm border border-white border-opacity-50 drop-shadow-lg`;
    default:
      return `${baseButtonStyles} bg-white bg-opacity-20 hover:bg-opacity-30 backdrop-blur-sm border border-white border-opacity-30`;
  }
}; 