import { IconType } from 'react-icons';
import {
  FaInstagram,
  FaFacebook,
  FaLinkedin,
  FaYoutube,
  FaTiktok,
  FaWhatsapp,
  FaTelegram,
  FaGithub,
  FaGlobe,
  FaEnvelope,
  FaPhone,
  FaLink,
  FaExternalLinkAlt,
  FaStore,
  FaDiscord,
  FaSnapchat,
  FaPinterest,
  FaReddit,
  FaSpotify,
  FaTwitch,
  FaApple,
  FaGooglePlay,
} from 'react-icons/fa';
import {
  SiX,
  SiThreads,
  SiLinktree,
  SiSignal,
} from 'react-icons/si';

export type PlatformType = 'instagram' | 'facebook' | 'twitter' | 'linkedin' | 'youtube' | 'tiktok' | 'whatsapp' | 'telegram' | 'github' | 'other';
export type LinkType = 'website' | 'social' | 'contact' | 'custom';

export interface PlatformConfig {
  icon: IconType;
  color: string;
  label: string;
  placeholder: string;
  baseUrl?: string;
}

export const PLATFORM_CONFIGS: Record<string, PlatformConfig> = {
  // Social Media Platforms
  instagram: {
    icon: FaInstagram,
    color: '#E4405F',
    label: 'Instagram',
    placeholder: 'https://instagram.com/username',
    baseUrl: 'https://instagram.com/',
  },
  facebook: {
    icon: FaFacebook,
    color: '#1877F2',
    label: 'Facebook',
    placeholder: 'https://facebook.com/username',
    baseUrl: 'https://facebook.com/',
  },
  twitter: {
    icon: SiX,
    color: '#000000',
    label: 'X (Twitter)',
    placeholder: 'https://x.com/username',
    baseUrl: 'https://x.com/',
  },
  linkedin: {
    icon: FaLinkedin,
    color: '#0A66C2',
    label: 'LinkedIn',
    placeholder: 'https://linkedin.com/in/username',
    baseUrl: 'https://linkedin.com/in/',
  },
  youtube: {
    icon: FaYoutube,
    color: '#FF0000',
    label: 'YouTube',
    placeholder: 'https://youtube.com/@username',
    baseUrl: 'https://youtube.com/',
  },
  tiktok: {
    icon: FaTiktok,
    color: '#000000',
    label: 'TikTok',
    placeholder: 'https://tiktok.com/@username',
    baseUrl: 'https://tiktok.com/@',
  },
  threads: {
    icon: SiThreads,
    color: '#000000',
    label: 'Threads',
    placeholder: 'https://threads.net/@username',
    baseUrl: 'https://threads.net/@',
  },
  snapchat: {
    icon: FaSnapchat,
    color: '#FFFC00',
    label: 'Snapchat',
    placeholder: 'https://snapchat.com/add/username',
    baseUrl: 'https://snapchat.com/add/',
  },
  pinterest: {
    icon: FaPinterest,
    color: '#BD081C',
    label: 'Pinterest',
    placeholder: 'https://pinterest.com/username',
    baseUrl: 'https://pinterest.com/',
  },
  reddit: {
    icon: FaReddit,
    color: '#FF4500',
    label: 'Reddit',
    placeholder: 'https://reddit.com/u/username',
    baseUrl: 'https://reddit.com/u/',
  },
  discord: {
    icon: FaDiscord,
    color: '#5865F2',
    label: 'Discord',
    placeholder: 'https://discord.gg/invite',
    baseUrl: 'https://discord.gg/',
  },
  twitch: {
    icon: FaTwitch,
    color: '#9146FF',
    label: 'Twitch',
    placeholder: 'https://twitch.tv/username',
    baseUrl: 'https://twitch.tv/',
  },
  spotify: {
    icon: FaSpotify,
    color: '#1DB954',
    label: 'Spotify',
    placeholder: 'https://open.spotify.com/user/username',
    baseUrl: 'https://open.spotify.com/',
  },

  // Communication Platforms
  whatsapp: {
    icon: FaWhatsapp,
    color: '#25D366',
    label: 'WhatsApp',
    placeholder: 'https://wa.me/1234567890',
    baseUrl: 'https://wa.me/',
  },
  telegram: {
    icon: FaTelegram,
    color: '#0088CC',
    label: 'Telegram',
    placeholder: 'https://t.me/username',
    baseUrl: 'https://t.me/',
  },
  signal: {
    icon: SiSignal,
    color: '#3A76F0',
    label: 'Signal',
    placeholder: 'https://signal.me/#p/+1234567890',
    baseUrl: 'https://signal.me/',
  },

  // Professional & Development
  github: {
    icon: FaGithub,
    color: '#181717',
    label: 'GitHub',
    placeholder: 'https://github.com/username',
    baseUrl: 'https://github.com/',
  },

  // Link in Bio Services
  linktree: {
    icon: SiLinktree,
    color: '#39E09B',
    label: 'Linktree',
    placeholder: 'https://linktr.ee/username',
    baseUrl: 'https://linktr.ee/',
  },
  beacons: {
    icon: FaLink,
    color: '#FF6B35',
    label: 'Beacons',
    placeholder: 'https://beacons.ai/username',
    baseUrl: 'https://beacons.ai/',
  },

  // App Stores
  appstore: {
    icon: FaApple,
    color: '#000000',
    label: 'App Store',
    placeholder: 'https://apps.apple.com/app/...',
    baseUrl: 'https://apps.apple.com/',
  },
  playstore: {
    icon: FaGooglePlay,
    color: '#34A853',
    label: 'Google Play',
    placeholder: 'https://play.google.com/store/apps/details?id=...',
    baseUrl: 'https://play.google.com/',
  },

  // Contact & Website
  website: {
    icon: FaGlobe,
    color: '#6366F1',
    label: 'Website',
    placeholder: 'https://your-website.com',
  },
  email: {
    icon: FaEnvelope,
    color: '#EF4444',
    label: 'Email',
    placeholder: 'mailto:<EMAIL>',
    baseUrl: 'mailto:',
  },
  phone: {
    icon: FaPhone,
    color: '#10B981',
    label: 'Phone',
    placeholder: 'tel:+1234567890',
    baseUrl: 'tel:',
  },
  
  // Generic/Other
  other: {
    icon: FaLink,
    color: '#6B7280',
    label: 'Other',
    placeholder: 'https://example.com',
  },
  custom: {
    icon: FaExternalLinkAlt,
    color: '#8B5CF6',
    label: 'Custom Link',
    placeholder: 'https://example.com',
  },
  store: {
    icon: FaStore,
    color: '#F59E0B',
    label: 'Online Store',
    placeholder: 'https://your-store.com',
  },
};

// Helper function to get platform config
export const getPlatformConfig = (platform: string): PlatformConfig => {
  return PLATFORM_CONFIGS[platform] || PLATFORM_CONFIGS.other;
};

// Helper function to get icon component
export const getPlatformIcon = (platform: string, className?: string) => {
  const config = getPlatformConfig(platform);
  const IconComponent = config.icon;
  return <IconComponent className={className} style={{ color: config.color }} />;
};

// Helper function to detect platform from URL
export const detectPlatformFromUrl = (url: string): string => {
  if (!url) return 'other';
  
  const lowerUrl = url.toLowerCase();
  
  // Social Media
  if (lowerUrl.includes('instagram.com')) return 'instagram';
  if (lowerUrl.includes('facebook.com') || lowerUrl.includes('fb.com')) return 'facebook';
  if (lowerUrl.includes('twitter.com') || lowerUrl.includes('x.com')) return 'twitter';
  if (lowerUrl.includes('linkedin.com')) return 'linkedin';
  if (lowerUrl.includes('youtube.com') || lowerUrl.includes('youtu.be')) return 'youtube';
  if (lowerUrl.includes('tiktok.com')) return 'tiktok';
  if (lowerUrl.includes('threads.net')) return 'threads';
  if (lowerUrl.includes('snapchat.com')) return 'snapchat';
  if (lowerUrl.includes('pinterest.com')) return 'pinterest';
  if (lowerUrl.includes('reddit.com')) return 'reddit';
  if (lowerUrl.includes('discord.gg') || lowerUrl.includes('discord.com')) return 'discord';
  if (lowerUrl.includes('twitch.tv')) return 'twitch';
  if (lowerUrl.includes('spotify.com')) return 'spotify';
  
  // Communication
  if (lowerUrl.includes('wa.me') || lowerUrl.includes('whatsapp.com')) return 'whatsapp';
  if (lowerUrl.includes('t.me') || lowerUrl.includes('telegram.org')) return 'telegram';
  if (lowerUrl.includes('signal.me')) return 'signal';
  
  // Development
  if (lowerUrl.includes('github.com')) return 'github';
  
  // Link in Bio
  if (lowerUrl.includes('linktr.ee')) return 'linktree';
  if (lowerUrl.includes('beacons.ai')) return 'beacons';
  
  // App Stores
  if (lowerUrl.includes('apps.apple.com') || lowerUrl.includes('itunes.apple.com')) return 'appstore';
  if (lowerUrl.includes('play.google.com')) return 'playstore';
  
  // Contact
  if (lowerUrl.startsWith('mailto:')) return 'email';
  if (lowerUrl.startsWith('tel:')) return 'phone';
  
  return 'website';
};

// Available platform options for dropdown
export const PLATFORM_OPTIONS = [
  { value: 'instagram', label: 'Instagram' },
  { value: 'facebook', label: 'Facebook' },
  { value: 'twitter', label: 'X (Twitter)' },
  { value: 'linkedin', label: 'LinkedIn' },
  { value: 'youtube', label: 'YouTube' },
  { value: 'tiktok', label: 'TikTok' },
  { value: 'threads', label: 'Threads' },
  { value: 'snapchat', label: 'Snapchat' },
  { value: 'pinterest', label: 'Pinterest' },
  { value: 'reddit', label: 'Reddit' },
  { value: 'discord', label: 'Discord' },
  { value: 'twitch', label: 'Twitch' },
  { value: 'spotify', label: 'Spotify' },
  { value: 'whatsapp', label: 'WhatsApp' },
  { value: 'telegram', label: 'Telegram' },
  { value: 'signal', label: 'Signal' },
  { value: 'github', label: 'GitHub' },
  { value: 'linktree', label: 'Linktree' },
  { value: 'beacons', label: 'Beacons' },
  { value: 'appstore', label: 'App Store' },
  { value: 'playstore', label: 'Google Play' },
  { value: 'website', label: 'Website' },
  { value: 'email', label: 'Email' },
  { value: 'phone', label: 'Phone' },
  { value: 'store', label: 'Online Store' },
  { value: 'custom', label: 'Custom Link' },
  { value: 'other', label: 'Other' },
];

// Link type options
export const LINK_TYPE_OPTIONS = [
  { value: 'social', label: 'Social Media' },
  { value: 'contact', label: 'Contact' },
  { value: 'website', label: 'Website' },
  { value: 'custom', label: 'Custom' },
]; 