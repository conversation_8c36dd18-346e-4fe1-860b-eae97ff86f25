'use client';

import { useState } from 'react';
import Link from 'next/link';

interface Feature {
  id: string;
  icon: string;
  title: string;
  description: string;
  benefits: string[];
  status: 'available' | 'coming-soon' | 'beta';
  color: string;
  demoUrl?: string;
  metrics?: {
    users?: string;
    improvement?: string;
    rating?: string;
  };
}

interface FeatureShowcaseProps {
  variant?: 'compact' | 'detailed' | 'comparison';
  showMetrics?: boolean;
  animated?: boolean;
}

export default function FeatureShowcase({ 
  variant = 'detailed', 
  showMetrics = true, 
  animated = true 
}: FeatureShowcaseProps) {
  const [activeFeature, setActiveFeature] = useState(0);

  const features: Feature[] = [
    {
      id: 'hiellinks',
      icon: '🔗',
      title: 'HielLinks',
      description: 'Beautiful link-in-bio pages with analytics for users',
      benefits: [
        'Custom themes & branding',
        'Real-time click tracking',
        'QR code generation',
        'Location mapping',
        'SEO optimized',
        'Mobile-first design'
      ],
      status: 'available',
      color: 'blue',
      demoUrl: '/profile/hiellinks',
      metrics: {
        users: '500+',
        improvement: 'Live',
        rating: 'Beta'
      }
    },
    {
      id: 'user-analytics',
      icon: '📊',
      title: 'User Analytics',
      description: 'Comprehensive analytics dashboard for HielLinks profiles',
      benefits: [
        'Real-time visitor tracking',
        'Click analytics & heatmaps',
        'Performance insights',
        'Geographic distribution',
        'Device & browser stats',
        'Traffic source analysis'
      ],
      status: 'beta',
      color: 'green',
      demoUrl: '/profile/hiellinks',
      metrics: {
        users: 'Q2 2025',
        improvement: 'Enhanced',
        rating: 'New!'
      }
    },
    {
      id: 'admin-dashboard',
      icon: '⚙️',
      title: 'Admin Dashboard',
      description: 'Website and user management (Admin Only)',
      benefits: [
        'Blog content management',
        'User administration',
        'Team management',
        'Platform monitoring',
        'System configuration',
        'Analytics & reporting'
      ],
      status: 'available',
      color: 'orange',
      demoUrl: '/admin',
      metrics: {
        users: 'Admin',
        improvement: 'Internal',
        rating: 'Tool'
      }
    },
    {
      id: 'community-platform',
      icon: '💬',
      title: 'Community Platform',
      description: 'User forums and discussion threads for community engagement',
      benefits: [
        'Discussion forums & threads',
        'User-generated content',
        'Community moderation',
        'Integration with profiles',
        'Reputation system',
        'Advanced search & filtering'
      ],
      status: 'coming-soon',
      color: 'purple',
      metrics: {
        users: 'Q3 2025',
        improvement: 'Community',
        rating: 'Planned'
      }
    },
    {
      id: 'social-media-planner',
      icon: '📱',
      title: 'Social Media Planner',
      description: 'Content scheduling and social media management',
      benefits: [
        'Multi-platform scheduling',
        'Content calendar interface',
        'Performance tracking',
        'Content templates',
        'Team collaboration',
        'Analytics integration'
      ],
      status: 'coming-soon',
      color: 'pink',
      metrics: {
        users: 'Q4 2025',
        improvement: 'Social',
        rating: 'Planned'
      }
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400';
      case 'beta': return 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400';
      case 'coming-soon': return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400';
      default: return 'bg-gray-100 text-gray-700 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'available': return 'Available';
      case 'beta': return 'Beta';
      case 'coming-soon': return 'Coming Soon';
      default: return 'Unknown';
    }
  };

  if (variant === 'compact') {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 ${animated ? 'animate-fade-in' : ''}`}>
        {features.map((feature, index) => (
          <div
            key={feature.id}
            className={`group bg-white dark:bg-gray-800 p-4 rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-700 hover-lift ${
              animated ? `animate-slide-up animate-delay-${index * 100}` : ''
            }`}
          >
            <div className={`w-12 h-12 bg-gradient-to-br from-${feature.color}-500 to-${feature.color}-600 rounded-lg flex items-center justify-center text-white text-xl mb-3 group-hover:scale-110 transition-transform duration-300`}>
              {feature.icon}
            </div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">{feature.title}</h3>
            <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">{feature.description}</p>
            <span className={`text-xs px-2 py-1 rounded-full font-medium ${getStatusColor(feature.status)}`}>
              {getStatusText(feature.status)}
            </span>
          </div>
        ))}
      </div>
    );
  }

  if (variant === 'comparison') {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-3xl p-8 shadow-xl border border-gray-200 dark:border-gray-700 ${animated ? 'animate-zoom-in' : ''}`}>
        <div className="text-center mb-8">
          <h3 className="text-3xl font-bold mb-4 text-gray-900 dark:text-white">
            Platform Comparison
          </h3>
          <p className="text-gray-600 dark:text-gray-300">
            See how HielTech compares to traditional solutions
          </p>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200 dark:border-gray-700">
                <th className="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">Feature</th>
                <th className="text-center py-4 px-6 font-semibold text-blue-600 dark:text-blue-400">HielTech</th>
                <th className="text-center py-4 px-6 font-semibold text-gray-600 dark:text-gray-400">Others</th>
              </tr>
            </thead>
            <tbody className="text-sm">
              {[
                { feature: "All-in-one Platform", hieltech: "✅ Everything Included", traditional: "❌ Multiple Tools" },
                { feature: "Real-time Analytics", hieltech: "✅ Advanced Insights", traditional: "❌ Basic Stats" },
                { feature: "AI-Powered Features", hieltech: "✅ Smart Automation", traditional: "❌ Manual Work" },
                { feature: "Custom Branding", hieltech: "✅ Full Customization", traditional: "💰 Premium Only" },
                { feature: "Team Collaboration", hieltech: "✅ Built-in Chat", traditional: "💰 Extra Cost" },
                { feature: "Project Management", hieltech: "✅ Complete Suite", traditional: "💰 Separate Tools" },
                { feature: "Monthly Cost", hieltech: "🆓 Free Beta", traditional: "💰 $50-200+" },
              ].map((row, index) => (
                <tr key={index} className={`border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700/50 ${animated ? `animate-slide-in-left animate-delay-${index * 100}` : ''}`}>
                  <td className="py-4 px-6 font-medium text-gray-900 dark:text-white">{row.feature}</td>
                  <td className="py-4 px-6 text-center text-blue-600 dark:text-blue-400">{row.hieltech}</td>
                  <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-400">{row.traditional}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  }

  // Detailed variant (default)
  return (
    <div className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${animated ? 'animate-fade-in' : ''}`}>
      {/* Feature Navigation */}
      <div className="space-y-4">
        {features.map((feature, index) => (
          <div
            key={feature.id}
            className={`cursor-pointer p-6 rounded-2xl border-2 transition-all duration-300 hover-lift ${
              activeFeature === index
                ? `border-${feature.color}-500 bg-${feature.color}-50 dark:bg-${feature.color}-900/20 shadow-lg transform scale-105`
                : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 bg-white dark:bg-gray-800'
            } ${animated ? `animate-slide-in-left animate-delay-${index * 100}` : ''}`}
            onClick={() => setActiveFeature(index)}
          >
            <div className="flex items-start space-x-4">
              <div className={`w-12 h-12 bg-gradient-to-br from-${feature.color}-500 to-${feature.color}-600 rounded-xl flex items-center justify-center text-white text-xl flex-shrink-0 ${animated ? 'animate-float' : ''}`}>
                {feature.icon}
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                    {feature.title}
                  </h3>
                  <span className={`text-xs px-2 py-1 rounded-full font-medium ${getStatusColor(feature.status)}`}>
                    {getStatusText(feature.status)}
                  </span>
                </div>
                <p className="text-gray-600 dark:text-gray-300 mb-3">
                  {feature.description}
                </p>
                {showMetrics && feature.metrics && (
                  <div className="flex space-x-4 text-sm">
                    <div className="text-gray-500 dark:text-gray-400">
                      <span className="font-medium text-gray-900 dark:text-white">{feature.metrics.users}</span> users
                    </div>
                    <div className="text-gray-500 dark:text-gray-400">
                      <span className="font-medium text-green-600">{feature.metrics.improvement}</span> improvement
                    </div>
                    <div className="text-gray-500 dark:text-gray-400">
                      <span className="font-medium text-yellow-600">{feature.metrics.rating}</span> rating
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Feature Details */}
      <div className={`bg-white dark:bg-gray-800 rounded-3xl p-8 shadow-xl border border-gray-200 dark:border-gray-700 sticky top-8 ${animated ? 'animate-zoom-in animate-delay-500' : ''}`}>
        <div className="mb-6">
          <div className={`w-16 h-16 bg-gradient-to-br from-${features[activeFeature].color}-500 to-${features[activeFeature].color}-600 rounded-2xl flex items-center justify-center text-white text-2xl mb-4 ${animated ? 'animate-bounce-in' : ''}`}>
            {features[activeFeature].icon}
          </div>
          <h3 className="text-3xl font-bold mb-4 text-gray-900 dark:text-white">
            {features[activeFeature].title}
          </h3>
          <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed mb-6">
            {features[activeFeature].description}
          </p>
        </div>

        <div className="mb-8">
          <h4 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
            Key Benefits
          </h4>
          <ul className="space-y-3">
            {features[activeFeature].benefits.map((benefit, index) => (
              <li key={index} className={`flex items-center ${animated ? `animate-slide-in-right animate-delay-${(index + 1) * 100}` : ''}`}>
                <span className={`w-2 h-2 bg-${features[activeFeature].color}-500 rounded-full mr-3 flex-shrink-0 ${animated ? 'animate-sparkle' : ''}`}></span>
                <span className="text-gray-600 dark:text-gray-300">{benefit}</span>
              </li>
            ))}
          </ul>
        </div>

        {showMetrics && features[activeFeature].metrics && (
          <div className={`mb-8 grid grid-cols-3 gap-4 ${animated ? 'animate-slide-up animate-delay-700' : ''}`}>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {features[activeFeature].metrics!.users}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-300">Active Users</div>
            </div>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {features[activeFeature].metrics!.improvement}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-300">Improvement</div>
            </div>
            <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">
                {features[activeFeature].metrics!.rating}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-300">User Rating</div>
            </div>
          </div>
        )}

        {features[activeFeature].status === 'available' && features[activeFeature].demoUrl && (
          <Link
            href={features[activeFeature].demoUrl!}
            className={`w-full bg-gradient-to-r from-${features[activeFeature].color}-500 to-${features[activeFeature].color}-600 text-white font-medium py-3 px-6 rounded-lg hover:shadow-lg transform hover:scale-105 transition-all duration-300 text-center block btn-interactive ${animated ? 'hover-glow' : ''}`}
          >
            Try {features[activeFeature].title}
          </Link>
        )}

        {features[activeFeature].status === 'coming-soon' && (
          <button
            disabled
            className="w-full bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 font-medium py-3 px-6 rounded-lg cursor-not-allowed text-center block"
          >
            Coming Soon
          </button>
        )}
      </div>
    </div>
  );
} 