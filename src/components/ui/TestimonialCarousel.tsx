'use client';

import { useState, useEffect } from 'react';

interface Testimonial {
  id: string;
  quote: string;
  author: string;
  position: string;
  company: string;
  avatar: string;
  rating: number;
  feature: string;
  improvement: string;
}

export default function TestimonialCarousel() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlay, setIsAutoPlay] = useState(true);

  const testimonials: Testimonial[] = [
    {
      id: '1',
      quote: "HielTech has completely revolutionized our workflow. What used to take us hours now takes minutes. The HielLinks feature alone has increased our conversion rate by 300%!",
      author: "<PERSON>",
      position: "Marketing Director",
      company: "TechStartup Inc.",
      avatar: "SJ",
      rating: 5,
      feature: "HielLinks",
      improvement: "300% conversion increase"
    },
    {
      id: '2',
      quote: "The project management tools are incredibly intuitive. Our team productivity has improved significantly since we started using HielTech. The Kanban boards are perfect for our agile workflow.",
      author: "<PERSON>",
      position: "Project Manager",
      company: "Digital Agency",
      avatar: "AB",
      rating: 5,
      feature: "Project Management",
      improvement: "250% productivity boost"
    },
    {
      id: '3',
      quote: "As a content creator, HielLinks has made it so easy to share all my platforms in one beautiful page. The analytics are amazing and help me understand my audience better.",
      author: "Maria <PERSON>",
      position: "Content Creator",
      company: "@MariaCreates",
      avatar: "MG",
      rating: 5,
      feature: "Analytics Dashboard",
      improvement: "500% engagement increase"
    },
    {
      id: '4',
      quote: "We were using 6 different tools before HielTech. Now everything is in one place, and we're saving over $200 per month. The team loves the integrated chat system.",
      author: "David Chen",
      position: "CEO",
      company: "InnovateLab",
      avatar: "DC",
      rating: 5,
      feature: "All-in-One Platform",
      improvement: "$200/month savings"
    },
    {
      id: '5',
      quote: "The customer support is exceptional. They helped us migrate our entire workflow in just 2 days. The ROI has been incredible - we've saved 40+ hours per week.",
      author: "Lisa Thompson",
      position: "Operations Manager",
      company: "GrowthCorp",
      avatar: "LT",
      rating: 5,
      feature: "Customer Support",
      improvement: "40 hours/week saved"
    },
    {
      id: '6',
      quote: "HielTech's analytics gave us insights we never had before. We discovered our peak engagement times and optimized our content strategy accordingly. Game changer!",
      author: "Roberto Silva",
      position: "Social Media Manager",
      company: "BrandBoost",
      avatar: "RS",
      rating: 5,
      feature: "Advanced Analytics",
      improvement: "400% reach increase"
    }
  ];

  useEffect(() => {
    if (!isAutoPlay) return;

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
      );
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlay, testimonials.length]);

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
    setIsAutoPlay(false);
    // Resume autoplay after 10 seconds
    setTimeout(() => setIsAutoPlay(true), 10000);
  };

  const nextSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
    );
    setIsAutoPlay(false);
    setTimeout(() => setIsAutoPlay(true), 10000);
  };

  const prevSlide = () => {
    setCurrentIndex((prevIndex) => 
      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1
    );
    setIsAutoPlay(false);
    setTimeout(() => setIsAutoPlay(true), 10000);
  };

  const renderStars = (rating: number) => {
    return Array(5).fill(0).map((_, i) => (
      <span key={i} className={`text-lg ${i < rating ? 'text-yellow-400' : 'text-gray-300'}`}>
        ★
      </span>
    ));
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-3xl p-8 shadow-xl border border-gray-200 dark:border-gray-700 relative overflow-hidden">
      {/* Background Decoration */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-10 left-10 w-20 h-20 bg-blue-500 rounded-full animate-float"></div>
        <div className="absolute bottom-10 right-10 w-16 h-16 bg-purple-500 rounded-full animate-float" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative z-10">
        {/* Header */}
        <div className="text-center mb-12">
          <h3 className="text-3xl font-bold mb-4 text-gray-900 dark:text-white">
            💬 What Our Users Say
          </h3>
          <p className="text-gray-600 dark:text-gray-300">
            Real feedback from thousands of businesses using HielTech daily
          </p>
        </div>

        {/* Testimonial Content */}
        <div className="relative">
          <div className="overflow-hidden">
            <div 
              className="flex transition-transform duration-500 ease-in-out"
              style={{ transform: `translateX(-${currentIndex * 100}%)` }}
            >
              {testimonials.map((testimonial) => (
                <div
                  key={testimonial.id}
                  className="w-full flex-shrink-0 px-4"
                >
                  <div className="max-w-4xl mx-auto text-center">
                    {/* Quote */}
                    <div className="mb-8">
                      <svg className="w-12 h-12 text-blue-600 dark:text-blue-400 mx-auto mb-6 opacity-20" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609L24 4.266c-4.266 1.171-6.584 4.266-6.584 8.171V21h-3.399zM0 21v-7.391c0-5.704 3.748-9.57 9-10.609L9.983 4.266C5.717 5.437 3.4 8.532 3.4 12.875V21H0z"/>
                      </svg>
                      <blockquote className="text-xl md:text-2xl text-gray-700 dark:text-gray-300 leading-relaxed italic font-medium">
                        &ldquo;{testimonial.quote}&rdquo;
                      </blockquote>
                    </div>

                    {/* Author Info */}
                    <div className="flex items-center justify-center space-x-6">
                      {/* Avatar */}
                      <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-xl font-bold animate-glow">
                        {testimonial.avatar}
                      </div>

                      {/* Details */}
                      <div className="text-left">
                        <div className="font-semibold text-lg text-gray-900 dark:text-white">
                          {testimonial.author}
                        </div>
                        <div className="text-gray-600 dark:text-gray-400">
                          {testimonial.position} at {testimonial.company}
                        </div>
                        <div className="flex items-center mt-1">
                          {renderStars(testimonial.rating)}
                        </div>
                      </div>
                    </div>

                    {/* Metrics */}
                    <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-4 max-w-md mx-auto">
                      <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                        <div className="text-sm font-medium text-blue-900 dark:text-blue-300">
                          Favorite Feature
                        </div>
                        <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                          {testimonial.feature}
                        </div>
                      </div>
                      <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                        <div className="text-sm font-medium text-green-900 dark:text-green-300">
                          Key Improvement
                        </div>
                        <div className="text-lg font-bold text-green-600 dark:text-green-400">
                          {testimonial.improvement}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Navigation Arrows */}
          <button
            onClick={prevSlide}
            className="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white dark:bg-gray-700 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:scale-110"
            aria-label="Previous testimonial"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <button
            onClick={nextSlide}
            className="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white dark:bg-gray-700 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:scale-110"
            aria-label="Next testimonial"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>

        {/* Indicators */}
        <div className="flex justify-center space-x-2 mt-8">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentIndex
                  ? 'bg-blue-600 dark:bg-blue-400 scale-125'
                  : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
              }`}
              aria-label={`Go to testimonial ${index + 1}`}
            />
          ))}
        </div>

        {/* Auto-play Indicator */}
        <div className="flex items-center justify-center mt-4 space-x-2">
          <button
            onClick={() => setIsAutoPlay(!isAutoPlay)}
            className={`text-sm px-3 py-1 rounded-full transition-all duration-300 ${
              isAutoPlay
                ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
            }`}
          >
            {isAutoPlay ? '⏸️ Auto-play On' : '▶️ Auto-play Off'}
          </button>
        </div>

        {/* Progress Bar */}
        {isAutoPlay && (
          <div className="mt-4 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
            <div 
              className="bg-blue-600 h-1 rounded-full transition-all duration-100 ease-linear"
              style={{
                width: `${((currentIndex + 1) / testimonials.length) * 100}%`
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
} 