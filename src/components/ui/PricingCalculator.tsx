'use client';

import { useState } from 'react';

interface PricingTier {
  name: string;
  price: number;
  features: string[];
  color: string;
  popular?: boolean;
}

interface ToolComparison {
  name: string;
  monthlyPrice: number;
  category: string;
}

export default function PricingCalculator() {
  const [selectedTier, setSelectedTier] = useState(1);
  const [teamSize, setTeamSize] = useState(5);
  const [selectedTools, setSelectedTools] = useState<string[]>([
    'linkTree', 'asana', 'slack', 'mailchimp'
  ]);
  const [yearlyDiscount, setYearlyDiscount] = useState(false);

  const tiers: PricingTier[] = [
    {
      name: 'Starter',
      price: 0,
      features: [
        '5 HielLinks profiles',
        'Basic analytics',
        'Up to 3 team members',
        'Standard support',
        'Basic project tools'
      ],
      color: 'green'
    },
    {
      name: 'Professional',
      price: 9,
      features: [
        'Unlimited HielLinks',
        'Advanced analytics',
        'Up to 10 team members',
        'Priority support',
        'Full project suite',
        'Social media planner',
        'Custom branding',
        'API access'
      ],
      color: 'blue',
      popular: true
    },
    {
      name: 'Enterprise',
      price: 19,
      features: [
        'Everything in Professional',
        'Unlimited team members',
        'White-label solutions',
        '24/7 priority support',
        'Custom integrations',
        'Advanced security',
        'Dedicated account manager',
        'SLA guarantee'
      ],
      color: 'purple'
    }
  ];

  const competitorTools: ToolComparison[] = [
    { name: 'Linktree Pro', monthlyPrice: 6, category: 'Link Management' },
    { name: 'Asana', monthlyPrice: 13.49, category: 'Project Management' },
    { name: 'Slack Pro', monthlyPrice: 8.75, category: 'Team Communication' },
    { name: 'Mailchimp', monthlyPrice: 13, category: 'Email Marketing' },
    { name: 'Buffer Pro', monthlyPrice: 6, category: 'Social Media' },
    { name: 'WordPress Pro', monthlyPrice: 25, category: 'Content Management' },
    { name: 'Zoom Pro', monthlyPrice: 16.99, category: 'Video Meetings' },
    { name: 'Canva Pro', monthlyPrice: 15, category: 'Design Tools' },
    { name: 'Google Analytics 360', monthlyPrice: 150, category: 'Analytics' },
    { name: 'Hubspot Starter', monthlyPrice: 20, category: 'CRM' }
  ];

  const calculateCompetitorCost = () => {
    return selectedTools.reduce((total, toolName) => {
      const tool = competitorTools.find(t => t.name.toLowerCase().includes(toolName.toLowerCase()));
      return total + (tool ? tool.monthlyPrice * teamSize : 0);
    }, 0);
  };

  const calculateHielTechCost = () => {
    const basePrice = tiers[selectedTier].price;
    const discount = yearlyDiscount ? 0.8 : 1; // 20% discount for yearly
    return basePrice * teamSize * discount;
  };

  const calculateSavings = () => {
    return calculateCompetitorCost() - calculateHielTechCost();
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(price);
  };

  const toggleTool = (toolKey: string) => {
    setSelectedTools(prev => 
      prev.includes(toolKey) 
        ? prev.filter(t => t !== toolKey)
        : [...prev, toolKey]
    );
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-3xl p-8 shadow-xl border border-gray-200 dark:border-gray-700">
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold mb-4 text-gray-900 dark:text-white">
          💰 Pricing Calculator
        </h3>
        <p className="text-gray-600 dark:text-gray-300">
          See how much you can save by switching to HielTech
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Configuration Panel */}
        <div className="space-y-6">
          {/* Team Size */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Team Size: <span className="text-2xl font-bold text-blue-600">{teamSize}</span> members
            </label>
            <input
              type="range"
              min="1"
              max="50"
              value={teamSize}
              onChange={(e) => setTeamSize(Number(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700 slider"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>1</span>
              <span>25</span>
              <span>50+</span>
            </div>
          </div>

          {/* Plan Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Select HielTech Plan
            </label>
            <div className="space-y-2">
              {tiers.map((tier, index) => (
                <div
                  key={tier.name}
                  className={`cursor-pointer p-4 rounded-lg border-2 transition-all duration-300 ${
                    selectedTier === index
                      ? `border-${tier.color}-500 bg-${tier.color}-50 dark:bg-${tier.color}-900/20`
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                  }`}
                  onClick={() => setSelectedTier(index)}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center space-x-2">
                        <h4 className="font-semibold text-gray-900 dark:text-white">{tier.name}</h4>
                        {tier.popular && (
                          <span className="bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 text-xs px-2 py-1 rounded-full font-medium">
                            Most Popular
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        {tier.features.slice(0, 2).join(', ')}...
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-gray-900 dark:text-white">
                        {tier.price === 0 ? 'FREE' : `$${tier.price}`}
                      </div>
                      {tier.price > 0 && (
                        <div className="text-sm text-gray-500">per user/month</div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Billing Toggle */}
          {selectedTier > 0 && (
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Annual Billing (20% off)
              </span>
              <button
                onClick={() => setYearlyDiscount(!yearlyDiscount)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  yearlyDiscount ? 'bg-blue-600' : 'bg-gray-300 dark:bg-gray-600'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    yearlyDiscount ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          )}

          {/* Competitor Tools */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Current Tools You&apos;re Using
            </label>
            <div className="grid grid-cols-2 gap-2">
              {[
                { key: 'linkTree', name: 'Linktree', price: '$6/mo' },
                { key: 'asana', name: 'Asana', price: '$13.49/mo' },
                { key: 'slack', name: 'Slack', price: '$8.75/mo' },
                { key: 'mailchimp', name: 'Mailchimp', price: '$13/mo' },
                { key: 'buffer', name: 'Buffer', price: '$6/mo' },
                { key: 'wordpress', name: 'WordPress', price: '$25/mo' },
                { key: 'zoom', name: 'Zoom', price: '$16.99/mo' },
                { key: 'canva', name: 'Canva', price: '$15/mo' }
              ].map((tool) => (
                <button
                  key={tool.key}
                  onClick={() => toggleTool(tool.key)}
                  className={`p-3 text-left rounded-lg border-2 transition-all duration-300 ${
                    selectedTools.includes(tool.key)
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                  }`}
                >
                  <div className="font-medium text-sm text-gray-900 dark:text-white">{tool.name}</div>
                  <div className="text-xs text-gray-600 dark:text-gray-300">{tool.price}</div>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Results Panel */}
        <div className="space-y-6">
          {/* Cost Comparison */}
          <div className="bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-6 rounded-2xl">
            <h4 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">
              💸 Monthly Cost Comparison
            </h4>
            
            <div className="space-y-4">
              {/* Current Tools Cost */}
              <div className="flex items-center justify-between p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                <div>
                  <div className="font-medium text-red-900 dark:text-red-300">Current Tools</div>
                  <div className="text-sm text-red-600 dark:text-red-400">
                    {selectedTools.length} tools × {teamSize} users
                  </div>
                </div>
                <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                  {formatPrice(calculateCompetitorCost())}
                </div>
              </div>

              {/* HielTech Cost */}
              <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                <div>
                  <div className="font-medium text-green-900 dark:text-green-300">HielTech {tiers[selectedTier].name}</div>
                  <div className="text-sm text-green-600 dark:text-green-400">
                    All features × {teamSize} users
                    {yearlyDiscount && selectedTier > 0 && <span> (20% annual discount)</span>}
                  </div>
                </div>
                <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                  {formatPrice(calculateHielTechCost())}
                </div>
              </div>

              {/* Savings */}
              {calculateSavings() > 0 && (
                <div className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                  <div>
                    <div className="font-medium text-blue-900 dark:text-blue-300">Monthly Savings</div>
                    <div className="text-sm text-blue-600 dark:text-blue-400">
                      Annual savings: {formatPrice(calculateSavings() * 12)}
                    </div>
                  </div>
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {formatPrice(calculateSavings())}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* ROI Metrics */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {calculateSavings() > 0 ? Math.round((calculateSavings() / calculateCompetitorCost()) * 100) : 0}%
              </div>
              <div className="text-sm text-purple-700 dark:text-purple-300">Cost Reduction</div>
            </div>
            <div className="text-center p-4 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg">
              <div className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">
                {selectedTools.length + 5}
              </div>
              <div className="text-sm text-indigo-700 dark:text-indigo-300">Tools Replaced</div>
            </div>
          </div>

          {/* Value Proposition */}
          <div className="p-6 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-2xl">
            <h4 className="text-xl font-bold mb-3">🎯 Your Value Summary</h4>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center">
                <span className="w-2 h-2 bg-white rounded-full mr-3"></span>
                Save {formatPrice(calculateSavings())} per month
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-white rounded-full mr-3"></span>
                Replace {selectedTools.length} separate tools
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-white rounded-full mr-3"></span>
                Reduce complexity by 80%
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-white rounded-full mr-3"></span>
                Increase team productivity by 3x
              </li>
            </ul>
          </div>

          {/* CTA */}
          <div className="text-center">
            <button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium py-4 px-8 rounded-lg hover:from-blue-700 hover:to-purple-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl btn-interactive">
              {calculateSavings() > 0 ? `Start Saving ${formatPrice(calculateSavings())}/month` : 'Get Started Free'}
            </button>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
              No credit card required • Free 30-day trial
            </p>
          </div>
        </div>
      </div>
    </div>
  );
} 