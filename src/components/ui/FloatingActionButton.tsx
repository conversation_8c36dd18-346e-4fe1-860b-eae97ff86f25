'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

export default function FloatingActionButton() {
  const [isOpen, setIsOpen] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
        setIsOpen(false);
      }
    };

    window.addEventListener('scroll', toggleVisibility);
    return () => window.removeEventListener('scroll', toggleVisibility);
  }, []);

  const actions = [
    {
      icon: '🚀',
      label: 'Start Free Trial',
      href: '/login',
      color: 'bg-blue-500 hover:bg-blue-600',
      description: 'Get started in 30 seconds'
    },
    {
      icon: '📞',
      label: 'Book Demo',
      href: '/contact',
      color: 'bg-green-500 hover:bg-green-600',
      description: 'Schedule a personal demo'
    },
    {
      icon: '💬',
      label: 'Live Chat',
      href: '#',
      color: 'bg-purple-500 hover:bg-purple-600',
      description: 'Get instant support',
      onClick: () => {
        // Integrate with your chat system
        console.log('Opening chat...');
      }
    },
    {
      icon: '📊',
      label: 'Calculate Savings',
      href: '#pricing-calculator',
      color: 'bg-orange-500 hover:bg-orange-600',
      description: 'See your potential savings'
    }
  ];

  const scrollToCalculator = () => {
    const calculator = document.getElementById('pricing-calculator');
    if (calculator) {
      calculator.scrollIntoView({ behavior: 'smooth' });
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-6 right-6 z-50">
      {/* Action Items */}
      {isOpen && (
        <div className="mb-4 space-y-3">
          {actions.map((action, index) => (
            <div
              key={action.label}
              className={`transform transition-all duration-300 ease-out animate-slide-up animate-delay-${index * 100}`}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="flex items-center space-x-3">
                {/* Tooltip */}
                <div className="bg-white dark:bg-gray-800 px-4 py-2 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 animate-slide-in-left">
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {action.label}
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-300">
                    {action.description}
                  </div>
                </div>

                {/* Action Button */}
                {action.href === '#pricing-calculator' ? (
                  <button
                    onClick={scrollToCalculator}
                    className={`w-12 h-12 rounded-full text-white shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-300 flex items-center justify-center ${action.color} hover-glow`}
                  >
                    <span className="text-lg">{action.icon}</span>
                  </button>
                ) : action.onClick ? (
                  <button
                    onClick={action.onClick}
                    className={`w-12 h-12 rounded-full text-white shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-300 flex items-center justify-center ${action.color} hover-glow`}
                  >
                    <span className="text-lg">{action.icon}</span>
                  </button>
                ) : (
                  <Link
                    href={action.href}
                    className={`w-12 h-12 rounded-full text-white shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-300 flex items-center justify-center ${action.color} hover-glow`}
                  >
                    <span className="text-lg">{action.icon}</span>
                  </Link>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Main FAB */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transform transition-all duration-300 flex items-center justify-center hover-glow ${
          isOpen ? 'rotate-45 scale-110' : 'hover:scale-110'
        }`}
        aria-label="Quick Actions"
      >
        {isOpen ? (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        ) : (
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
        )}
      </button>

      {/* Pulse Animation */}
      {!isOpen && (
        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 animate-ping opacity-20"></div>
      )}
    </div>
  );
} 