'use client';

import { useState } from 'react';

interface FeatureItem {
  id: string;
  title: string;
  description: string;
  icon: string;
  status: 'completed' | 'in-progress' | 'planned' | 'future';
  quarter: string;
  category: 'core' | 'advanced' | 'enterprise' | 'ai';
  impact: 'high' | 'medium' | 'low';
}

export default function FeatureMap() {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedQuarter, setSelectedQuarter] = useState<string>('all');

  const features: FeatureItem[] = [
    {
      id: 'hiellinks',
      title: 'HielLinks Platform',
      description: 'Beautiful link-in-bio pages with analytics for users',
      icon: '🔗',
      status: 'completed',
      quarter: 'Q1 2025',
      category: 'core',
      impact: 'high'
    },
    {
      id: 'admin-dashboard',
      title: 'Admin Dashboard',
      description: 'Website management and user administration',
      icon: '⚙️',
      status: 'completed',
      quarter: 'Q1 2025',
      category: 'core',
      impact: 'high'
    },
    {
      id: 'blog-management',
      title: 'Blog Management (Admin)',
      description: 'Content creation and blog management for website',
      icon: '📝',
      status: 'completed',
      quarter: 'Q1 2025',
      category: 'core',
      impact: 'medium'
    },
    {
      id: 'team-management',
      title: 'Team Management (Admin)',
      description: 'Internal team and application management',
      icon: '👥',
      status: 'completed',
      quarter: 'Q1 2025',
      category: 'core',
      impact: 'medium'
    },
    {
      id: 'user-analytics',
      title: 'User Analytics Dashboard',
      description: 'Real-time analytics for HielLinks profiles',
      icon: '📊',
      status: 'in-progress',
      quarter: 'Q2 2025',
      category: 'core',
      impact: 'high'
    },
    {
      id: 'community-platform',
      title: 'Community Platform',
      description: 'User forums, threads, and community features',
      icon: '💬',
      status: 'planned',
      quarter: 'Q3 2025',
      category: 'core',
      impact: 'high'
    },
    {
      id: 'social-media-planner',
      title: 'Social Media Planner',
      description: 'Content scheduling and social media management',
      icon: '📱',
      status: 'planned',
      quarter: 'Q4 2025',
      category: 'advanced',
      impact: 'high'
    },
    {
      id: 'api-platform',
      title: 'Developer API',
      description: 'Public API for third-party integrations',
      icon: '🔌',
      status: 'planned',
      quarter: 'Q1 2026',
      category: 'advanced',
      impact: 'medium'
    },
    {
      id: 'mobile-apps',
      title: 'Mobile Applications',
      description: 'Native iOS and Android apps for users',
      icon: '📱',
      status: 'planned',
      quarter: 'Q2 2026',
      category: 'core',
      impact: 'high'
    },
    {
      id: 'ai-features',
      title: 'AI Content Tools',
      description: 'AI-powered content generation and optimization',
      icon: '🤖',
      status: 'future',
      quarter: 'Q3 2026',
      category: 'ai',
      impact: 'medium'
    },
    {
      id: 'white-label',
      title: 'White-label Solutions',
      description: 'Custom branding for enterprise clients',
      icon: '🏷️',
      status: 'future',
      quarter: 'Q4 2026',
      category: 'enterprise',
      impact: 'medium'
    },
    {
      id: 'advanced-integrations',
      title: 'Advanced Integrations',
      description: 'Deep integrations with major platforms',
      icon: '🔗',
      status: 'future',
      quarter: 'Q1 2027',
      category: 'enterprise',
      impact: 'low'
    }
  ];

  const categories = [
    { id: 'all', name: 'All Features', color: 'gray' },
    { id: 'core', name: 'Core Platform', color: 'blue' },
    { id: 'advanced', name: 'Advanced Tools', color: 'purple' },
    { id: 'ai', name: 'AI Features', color: 'green' },
    { id: 'enterprise', name: 'Enterprise', color: 'orange' }
  ];

  const quarters = [
    'all', 'Q1 2025', 'Q2 2025', 'Q3 2025', 'Q4 2025', 'Q1 2026', 'Q2 2026', 'Q3 2026', 'Q4 2026', 'Q1 2027'
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500';
      case 'in-progress': return 'bg-blue-500';
      case 'planned': return 'bg-yellow-500';
      case 'future': return 'bg-gray-400';
      default: return 'bg-gray-300';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return 'Live';
      case 'in-progress': return 'In Development';
      case 'planned': return 'Planned';
      case 'future': return 'Future';
      default: return 'Unknown';
    }
  };

  const getImpactIcon = (impact: string) => {
    switch (impact) {
      case 'high': return '🚀';
      case 'medium': return '⭐';
      case 'low': return '💡';
      default: return '📋';
    }
  };

  const filteredFeatures = features.filter(feature => {
    const categoryMatch = selectedCategory === 'all' || feature.category === selectedCategory;
    const quarterMatch = selectedQuarter === 'all' || feature.quarter === selectedQuarter;
    return categoryMatch && quarterMatch;
  });

  const groupedByQuarter = filteredFeatures.reduce((acc, feature) => {
    if (!acc[feature.quarter]) {
      acc[feature.quarter] = [];
    }
    acc[feature.quarter].push(feature);
    return acc;
  }, {} as Record<string, FeatureItem[]>);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-3xl p-8 shadow-xl border border-gray-200 dark:border-gray-700">
      <div className="text-center mb-8">
        <h3 className="text-3xl font-bold mb-4 text-gray-900 dark:text-white">
          🗺️ Feature Roadmap
        </h3>
        <p className="text-gray-600 dark:text-gray-300">
          Explore our journey from MVP to the most comprehensive business platform
        </p>
      </div>

      {/* Filters */}
      <div className="mb-8 space-y-4">
        {/* Category Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Filter by Category
          </label>
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                  selectedCategory === category.id
                    ? `bg-${category.color}-500 text-white shadow-lg`
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* Quarter Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Filter by Timeline
          </label>
          <select
            value={selectedQuarter}
            onChange={(e) => setSelectedQuarter(e.target.value)}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
          >
            {quarters.map((quarter) => (
              <option key={quarter} value={quarter}>
                {quarter === 'all' ? 'All Quarters' : quarter}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        {[
          { label: 'Total Features', value: features.length, color: 'blue' },
          { label: 'Completed', value: features.filter(f => f.status === 'completed').length, color: 'green' },
          { label: 'In Progress', value: features.filter(f => f.status === 'in-progress').length, color: 'yellow' },
          { label: 'Planned', value: features.filter(f => f.status === 'planned').length, color: 'purple' }
        ].map((stat, index) => (
          <div key={index} className={`text-center p-4 bg-${stat.color}-50 dark:bg-${stat.color}-900/20 rounded-lg`}>
            <div className={`text-2xl font-bold text-${stat.color}-600 dark:text-${stat.color}-400`}>
              {stat.value}
            </div>
            <div className={`text-sm text-${stat.color}-700 dark:text-${stat.color}-300`}>
              {stat.label}
            </div>
          </div>
        ))}
      </div>

      {/* Timeline View */}
      <div className="space-y-8">
        {Object.entries(groupedByQuarter)
          .sort(([a], [b]) => a.localeCompare(b))
          .map(([quarter, quarterFeatures]) => (
            <div key={quarter} className="relative">
              {/* Quarter Header */}
              <div className="flex items-center mb-6">
                <div className="bg-blue-500 w-4 h-4 rounded-full mr-4 flex-shrink-0 animate-pulse"></div>
                <h4 className="text-xl font-bold text-gray-900 dark:text-white">{quarter}</h4>
                <div className="flex-1 h-px bg-gray-300 dark:bg-gray-600 ml-4"></div>
              </div>

              {/* Features Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 ml-8">
                {quarterFeatures.map((feature, index) => (
                  <div
                    key={feature.id}
                    className={`group p-6 rounded-xl border-2 transition-all duration-300 hover:shadow-lg hover:-translate-y-1 animate-slide-up animate-delay-${index * 100}`}
                    style={{
                      borderColor: feature.status === 'completed' ? '#10b981' : 
                                  feature.status === 'in-progress' ? '#3b82f6' :
                                  feature.status === 'planned' ? '#f59e0b' : '#6b7280'
                    }}
                  >
                    {/* Status Indicator */}
                    <div className="flex items-center justify-between mb-4">
                      <div className={`w-3 h-3 rounded-full ${getStatusColor(feature.status)} animate-pulse`}></div>
                      <span className="text-xs font-medium px-2 py-1 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
                        {getStatusText(feature.status)}
                      </span>
                    </div>

                    {/* Feature Info */}
                    <div className="flex items-start space-x-3 mb-4">
                      <div className="text-2xl flex-shrink-0">{feature.icon}</div>
                      <div className="flex-1">
                        <h5 className="font-semibold text-gray-900 dark:text-white mb-1">
                          {feature.title}
                        </h5>
                        <p className="text-sm text-gray-600 dark:text-gray-300">
                          {feature.description}
                        </p>
                      </div>
                    </div>

                    {/* Metadata */}
                    <div className="flex items-center justify-between text-xs">
                      <div className="flex items-center space-x-1">
                        <span>{getImpactIcon(feature.impact)}</span>
                        <span className="text-gray-500 dark:text-gray-400 capitalize">
                          {feature.impact} Impact
                        </span>
                      </div>
                      <span className={`px-2 py-1 rounded-full font-medium ${
                        feature.category === 'core' ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400' :
                        feature.category === 'advanced' ? 'bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-400' :
                        feature.category === 'ai' ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400' :
                        'bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-400'
                      }`}>
                        {feature.category}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
      </div>

      {/* Legend */}
      <div className="mt-8 p-6 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <h4 className="font-semibold text-gray-900 dark:text-white mb-4">Legend</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[
            { status: 'completed', label: 'Live & Available', color: 'bg-green-500' },
            { status: 'in-progress', label: 'In Development', color: 'bg-blue-500' },
            { status: 'planned', label: 'Planned', color: 'bg-yellow-500' },
            { status: 'future', label: 'Future Vision', color: 'bg-gray-400' }
          ].map((item) => (
            <div key={item.status} className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${item.color}`}></div>
              <span className="text-sm text-gray-700 dark:text-gray-300">{item.label}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
} 