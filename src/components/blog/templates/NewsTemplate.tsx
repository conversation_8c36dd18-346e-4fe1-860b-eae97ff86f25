import Link from 'next/link';
import Image from 'next/image';
import { BlogPost } from '@/lib/supabase';

interface NewsTemplateProps {
  post: BlogPost;
}

export default function NewsTemplate({ post }: NewsTemplateProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <article className="max-w-4xl mx-auto">
      {/* Header */}
      <header className="mb-8">
        {/* News Badge */}
        <div className="flex items-center mb-4">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
            📰 Breaking News
          </span>
          <span className="mx-3 text-gray-400">•</span>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {formatDate(post.published_at || post.created_at)}
          </span>
        </div>

        {/* Title */}
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6 leading-tight">
          {post.title}
        </h1>

        {/* Excerpt */}
        {post.excerpt && (
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed font-medium">
            {post.excerpt}
          </p>
        )}

        {/* Author and Meta */}
        <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 pb-6">
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-500 dark:text-gray-400 mr-3">
              {post.author?.display_name 
                ? post.author.display_name.split(' ').map(n => n[0]).join('')
                : 'A'
              }
            </div>
            <div>
              <p className="font-medium text-gray-900 dark:text-white">
                {post.author?.display_name || 'Admin'}
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {post.reading_time} min read • {post.view_count} views
              </p>
            </div>
          </div>
          
          {/* Share Buttons */}
          <div className="flex items-center space-x-2">
            <button 
              onClick={() => {
                const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(post.title)}&url=${encodeURIComponent(window.location.href)}`;
                window.open(url, '_blank');
              }}
              className="p-2 text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400"
            >
              🐦
            </button>
            <button 
              onClick={() => {
                const url = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.href)}`;
                window.open(url, '_blank');
              }}
              className="p-2 text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400"
            >
              💼
            </button>
          </div>
        </div>
      </header>

      {/* Featured Image */}
      {post.featured_image && (
        <div className="mb-8">
          <Image
            src={post.featured_image.file_path}
            alt={post.featured_image.alt_text || post.title}
            width={800}
            height={400}
            className="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg"
          />
          {post.featured_image.caption && (
            <p className="text-sm text-gray-500 dark:text-gray-400 text-center mt-2 italic">
              {post.featured_image.caption}
            </p>
          )}
        </div>
      )}

      {/* Content */}
      <div className="prose prose-lg max-w-none dark:prose-invert prose-blue mb-8">
        <div dangerouslySetInnerHTML={{ __html: post.content }} />
      </div>

      {/* Tags */}
      {post.tags && post.tags.length > 0 && (
        <div className="mb-8 pt-6 border-t border-gray-200 dark:border-gray-700">
          <div className="flex flex-wrap gap-2">
            {post.tags.map((tag) => (
              <Link
                key={tag.id}
                href={`/blog?tag=${tag.slug}`}
                className="px-3 py-1 rounded-full text-sm font-medium hover:opacity-80 transition-opacity"
                style={{ 
                  backgroundColor: tag.color + '20',
                  color: tag.color
                }}
              >
                #{tag.name}
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* Related Links */}
      {post.external_links && Object.keys(post.external_links).length > 0 && (
        <div className="mb-8 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            📎 Related Links
          </h3>
          <div className="space-y-2">
            {post.external_links.website && (
              <a
                href={post.external_links.website}
                target="_blank"
                rel="noopener noreferrer"
                className="block text-blue-600 dark:text-blue-400 hover:underline"
              >
                🌐 Official Website
              </a>
            )}
            {post.external_links.github && (
              <a
                href={post.external_links.github}
                target="_blank"
                rel="noopener noreferrer"
                className="block text-blue-600 dark:text-blue-400 hover:underline"
              >
                💻 GitHub Repository
              </a>
            )}
          </div>
        </div>
      )}

      {/* Back to News */}
      <div className="text-center pt-8 border-t border-gray-200 dark:border-gray-700">
        <Link
          href="/blog?category=industry-news"
          className="inline-flex items-center px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
        >
          ← More News
        </Link>
      </div>
    </article>
  );
}
