import Link from 'next/link';
import Image from 'next/image';
import { BlogPost } from '@/lib/supabase';

interface CaseStudyTemplateProps {
  post: BlogPost;
}

export default function CaseStudyTemplate({ post }: CaseStudyTemplateProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="max-w-6xl mx-auto">
      {/* Hero Section */}
      <header className="mb-12">
        {/* Case Study Badge */}
        <div className="flex items-center mb-6">
          <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400">
            📊 Case Study
          </span>
          <span className="mx-3 text-gray-400">•</span>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {post.reading_time} min read
          </span>
        </div>

        {/* Title */}
        <h1 className="text-5xl md:text-6xl font-bold text-gray-900 dark:text-white mb-8 leading-tight">
          {post.title}
        </h1>

        {/* Excerpt */}
        {post.excerpt && (
          <p className="text-2xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed max-w-4xl">
            {post.excerpt}
          </p>
        )}

        {/* Project Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              🎯 Challenge
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              Complex problem requiring innovative solution
            </p>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              💡 Solution
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              Strategic approach with modern technology
            </p>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              📈 Results
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              Measurable impact and success metrics
            </p>
          </div>
        </div>

        {/* Author and Date */}
        <div className="flex items-center">
          <div className="w-12 h-12 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-500 dark:text-gray-400 mr-4">
            {post.author?.display_name 
              ? post.author.display_name.split(' ').map(n => n[0]).join('')
              : 'A'
            }
          </div>
          <div>
            <p className="font-medium text-gray-900 dark:text-white">
              {post.author?.display_name || 'Admin'}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Published on {formatDate(post.published_at || post.created_at)}
            </p>
          </div>
        </div>
      </header>

      {/* Featured Image */}
      {post.featured_image && (
        <div className="mb-12">
          <Image
            src={post.featured_image.file_path}
            alt={post.featured_image.alt_text || post.title}
            width={800}
            height={500}
            className="w-full h-96 md:h-[500px] object-cover rounded-2xl shadow-2xl"
          />
          {post.featured_image.caption && (
            <p className="text-sm text-gray-500 dark:text-gray-400 text-center mt-4">
              {post.featured_image.caption}
            </p>
          )}
        </div>
      )}

      {/* Content */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
        {/* Main Content */}
        <article className="lg:col-span-3">
          <div className="prose prose-lg max-w-none dark:prose-invert prose-blue mb-12">
            <div dangerouslySetInnerHTML={{ __html: post.content }} />
          </div>

          {/* Key Metrics */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-2xl p-8 mb-12">
            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">
              📊 Key Results
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                  150%
                </div>
                <p className="text-gray-600 dark:text-gray-400">Performance Improvement</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">
                  85%
                </div>
                <p className="text-gray-600 dark:text-gray-400">User Satisfaction</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">
                  3x
                </div>
                <p className="text-gray-600 dark:text-gray-400">Faster Deployment</p>
              </div>
            </div>
          </div>

          {/* Technologies Used */}
          {post.tags && post.tags.length > 0 && (
            <div className="mb-12">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                🛠️ Technologies Used
              </h3>
              <div className="flex flex-wrap gap-3">
                {post.tags.map((tag) => (
                  <Link
                    key={tag.id}
                    href={`/blog?tag=${tag.slug}`}
                    className="px-4 py-2 rounded-lg text-sm font-medium hover:opacity-80 transition-opacity border"
                    style={{ 
                      backgroundColor: tag.color + '10',
                      borderColor: tag.color + '40',
                      color: tag.color
                    }}
                  >
                    {tag.name}
                  </Link>
                ))}
              </div>
            </div>
          )}

          {/* Project Links */}
          {post.external_links && Object.keys(post.external_links).length > 0 && (
            <div className="mb-12 p-8 bg-gray-50 dark:bg-gray-800 rounded-2xl">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                🔗 Project Resources
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {post.external_links.github && (
                  <a
                    href={post.external_links.github}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center p-4 bg-white dark:bg-gray-700 rounded-xl hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-600"
                  >
                    <span className="mr-4 text-2xl">💻</span>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">Source Code</p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">View on GitHub</p>
                    </div>
                  </a>
                )}
                {post.external_links.demo && (
                  <a
                    href={post.external_links.demo}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center p-4 bg-white dark:bg-gray-700 rounded-xl hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-600"
                  >
                    <span className="mr-4 text-2xl">🌐</span>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">Live Demo</p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Try it yourself</p>
                    </div>
                  </a>
                )}
                {post.external_links.playstore && (
                  <a
                    href={post.external_links.playstore}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center p-4 bg-white dark:bg-gray-700 rounded-xl hover:shadow-lg transition-all duration-300 border border-gray-200 dark:border-gray-600"
                  >
                    <span className="mr-4 text-2xl">📱</span>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">Mobile App</p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Download from Play Store</p>
                    </div>
                  </a>
                )}
              </div>
            </div>
          )}
        </article>

        {/* Sidebar */}
        <aside className="lg:col-span-1">
          <div className="sticky top-8 space-y-6">
            {/* Project Details */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                📋 Project Details
              </h3>
              <div className="space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Category</p>
                  <p className="text-gray-900 dark:text-white">
                    {post.category?.name || 'Case Study'}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Duration</p>
                  <p className="text-gray-900 dark:text-white">{post.reading_time} min read</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Views</p>
                  <p className="text-gray-900 dark:text-white">{post.view_count}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Published</p>
                  <p className="text-gray-900 dark:text-white">
                    {formatDate(post.published_at || post.created_at)}
                  </p>
                </div>
              </div>
            </div>

            {/* Share Case Study */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                📤 Share This Case Study
              </h3>
              <div className="space-y-3">
                <button 
                  onClick={() => {
                    const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(`Check out this case study: ${post.title}`)}&url=${encodeURIComponent(window.location.href)}`;
                    window.open(url, '_blank');
                  }}
                  className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                >
                  🐦 Share on Twitter
                </button>
                <button 
                  onClick={() => {
                    const url = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.href)}`;
                    window.open(url, '_blank');
                  }}
                  className="w-full flex items-center justify-center px-4 py-2 bg-blue-700 text-white rounded-lg hover:bg-blue-800 transition-colors text-sm"
                >
                  💼 Share on LinkedIn
                </button>
              </div>
            </div>

            {/* More Case Studies */}
            <div className="text-center">
              <Link
                href="/blog?category=case-studies"
                className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm"
              >
                ← More Case Studies
              </Link>
            </div>
          </div>
        </aside>
      </div>
    </div>
  );
}
