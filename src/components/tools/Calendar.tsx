'use client';

import { useEffect, useState, useCallback } from 'react';
import { useAuth } from '@/lib/auth/AuthContext';
import { taskService } from '@/lib/services/taskService';
import { CalendarEvent } from '@/lib/types/task';
import { motion } from 'framer-motion';
import TaskForm from './TaskForm';

export default function Calendar() {
  const { user } = useAuth();
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [showTaskForm, setShowTaskForm] = useState(false);
  const [editingTask, setEditingTask] = useState<CalendarEvent | null>(null);

  const loadEvents = useCallback(async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const calendarEvents = await taskService.getCalendarEvents(user.id);
      setEvents(calendarEvents);
    } catch (error) {
      console.error('Error loading calendar events:', error);
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  useEffect(() => {
    if (user) {
      loadEvents();
    }
  }, [user, loadEvents]);

  const handleTaskSubmit = () => {
    loadEvents();
    setShowTaskForm(false);
    setEditingTask(null);
  };

  const handleEditTask = (event: CalendarEvent) => {
    setEditingTask(event);
    setShowTaskForm(true);
  };

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDay = firstDay.getDay();
    
    const days = [];
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDay; i++) {
      days.push(null);
    }
    
    // Add days of the month
    for (let i = 1; i <= daysInMonth; i++) {
      days.push(new Date(year, month, i));
    }
    
    return days;
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const getEventsForDate = (date: Date) => {
    if (!date) return [];
    return events.filter(event => {
      if (!event.due_date) return false;
      const eventDate = new Date(event.due_date);
      return eventDate.getDate() === date.getDate() &&
             eventDate.getMonth() === date.getMonth() &&
             eventDate.getFullYear() === date.getFullYear();
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="h-full w-full relative"
    >
      {loading ? (
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigateMonth('prev')}
                className="p-2 hover:bg-white/5 dark:hover:bg-gray-700/50 rounded-full"
              >
                ←
              </button>
              <h2 className="text-xl font-semibold">
                {currentDate.toLocaleString('default', { month: 'long', year: 'numeric' })}
              </h2>
              <button
                onClick={() => navigateMonth('next')}
                className="p-2 hover:bg-white/5 dark:hover:bg-gray-700/50 rounded-full"
              >
                →
              </button>
            </div>
            <button
              onClick={() => setShowTaskForm(true)}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md"
            >
              Add Task
            </button>
          </div>

          <div className="grid grid-cols-7 gap-1 bg-white/5 dark:bg-gray-700/50 rounded-lg p-4">
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
              <div
                key={day}
                className="p-2 text-center font-semibold text-gray-600 dark:text-gray-300"
              >
                {day}
              </div>
            ))}
            {getDaysInMonth(currentDate).map((date, index) => (
              <div
                key={index}
                className={`min-h-[100px] p-2 border border-gray-200 dark:border-gray-600 ${date ? 'bg-white/5 dark:bg-gray-600/50' : ''}`}
              >
                {date && (
                  <>
                    <div className="font-medium text-gray-600 dark:text-gray-400">
                      {date.getDate()}
                    </div>
                    <div className="space-y-1 mt-1">
                      {getEventsForDate(date).map((event) => (
                        <div
                          key={event.id}
                          onClick={() => handleEditTask(event)}
                          className="text-xs p-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 rounded cursor-pointer hover:bg-blue-200 dark:hover:bg-blue-800/30"
                        >
                          {event.title}
                        </div>
                      ))}
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
      {showTaskForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-md">
            <TaskForm
              onSubmit={handleTaskSubmit}
              onCancel={() => {
                setShowTaskForm(false);
                setEditingTask(null);
              }}
              initialData={editingTask || undefined}
              mode={editingTask ? 'edit' : 'create'}
            />
          </div>
        </div>
      )}
    </motion.div>
  );
}