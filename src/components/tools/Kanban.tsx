'use client';

import { useEffect, useState, useCallback } from 'react';
import { useAuth } from '@/lib/auth/AuthContext';
import { taskService } from '@/lib/services/taskService';
import { KanbanColumn, Task } from '@/lib/types/task';
import { motion } from 'framer-motion';
import TaskForm from './TaskForm';

export default function Kanban() {
  const { user } = useAuth();
  const [columns, setColumns] = useState<KanbanColumn[]>([]);
  const [loading, setLoading] = useState(true);
  const [draggedTask, setDraggedTask] = useState<Task | null>(null);
  const [showTaskForm, setShowTaskForm] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);

  const loadKanbanData = useCallback(async () => {
    if (!user?.id) return;

    try {
      setLoading(true);
      const kanbanData = await taskService.getKanbanTasks(user.id);
      setColumns(kanbanData);
    } catch (error) {
      console.error('Error loading Kanban data:', error);
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  useEffect(() => {
    if (user) {
      loadKanbanData();
    }
  }, [user, loadKanbanData]);

  const handleDragStart = (task: Task) => {
    setDraggedTask(task);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = async (columnId: 'pending' | 'in-progress' | 'completed') => {
    if (!draggedTask || !user?.id) return;

    try {
      await taskService.updateTask(draggedTask.id, { status: columnId });
      await loadKanbanData(); // Refresh the board
    } catch (error) {
      console.error('Error updating task status:', error);
    }
    setDraggedTask(null);
  };

  const handleTaskSubmit = () => {
    loadKanbanData();
    setShowTaskForm(false);
    setEditingTask(null);
  };

  const handleEditTask = (task: Task) => {
    setEditingTask(task);
    setShowTaskForm(true);
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
      className="h-full w-full relative"
    >
      {loading ? (
        <div className="flex items-center justify-center h-full">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <div className="grid grid-cols-3 gap-4 h-full relative">
          <button
            onClick={() => setShowTaskForm(true)}
            className="absolute top-0 right-0 px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md"
          >
            Add Task
          </button>
          {columns.map((column) => (
            <div
              key={column.id}
              onDragOver={handleDragOver}
              onDrop={() => handleDrop(column.id as 'pending' | 'in-progress' | 'completed')}
              className="bg-white/5 dark:bg-gray-700/50 rounded-lg p-4"
            >
              <h3 className="text-lg font-semibold mb-4 text-gray-700 dark:text-gray-300">
                {column.title}
              </h3>
              <div className="space-y-2">
                {column.tasks.map((task) => (
                  <motion.div
                    key={task.id}
                    draggable
                    onDragStart={() => handleDragStart(task)}
                    className="p-3 bg-white/10 dark:bg-gray-600/50 rounded-lg cursor-move hover:shadow-lg transition-all duration-200"
                    whileHover={{ scale: 1.02 }}
                  >
                    <h4 className="font-medium text-gray-800 dark:text-gray-200">
                      {task.title}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {task.description}
                    </p>
                    <div className="flex items-center justify-between mt-2">
                      <span className={`text-xs px-2 py-1 rounded ${getPriorityColor(task.priority)}`}>
                        {task.priority}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        Due: {task.due_date ? new Date(task.due_date).toLocaleDateString() : 'No due date'}
                      </span>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditTask(task);
                        }}
                        className="text-xs text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"
                      >
                        Edit
                      </button>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}
      {showTaskForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-md">
            <TaskForm
              onSubmit={handleTaskSubmit}
              onCancel={() => {
                setShowTaskForm(false);
                setEditingTask(null);
              }}
              initialData={editingTask || undefined}
              mode={editingTask ? 'edit' : 'create'}
            />
          </div>
        </div>
      )}
    </motion.div>
  );
}

const getPriorityColor = (priority: string) => {
  switch (priority) {
    case 'high':
      return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200';
    case 'medium':
      return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200';
    default:
      return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200';
  }
};