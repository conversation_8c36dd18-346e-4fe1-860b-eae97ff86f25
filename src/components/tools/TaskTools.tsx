'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Calendar from './Calendar';
import Kanban from './Kanban';
import Gantt from './Gantt';
import ListView from './ListView';

type Tool = 'calendar' | 'kanban' | 'gantt' | 'list';

export default function TaskTools() {
  const [activeTool, setActiveTool] = useState<Tool>('calendar');

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="max-w-6xl mx-auto p-8 backdrop-blur-lg bg-white/10 dark:bg-gray-800/30 rounded-xl shadow-xl border border-white/20 dark:border-gray-700/30"
    >
      <div className="flex justify-between items-center mb-8">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent">
          Task Management Tools
        </h2>
        <div className="flex space-x-4">
          <button
            onClick={() => setActiveTool('calendar')}
            className={`px-4 py-2 rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg ${
              activeTool === 'calendar'
                ? 'bg-blue-600/80 text-white'
                : 'bg-white/5 dark:bg-gray-700/50 text-gray-700 dark:text-gray-300'
            }`}
          >
            Calendar
          </button>
          <button
            onClick={() => setActiveTool('list')}
            className={`px-4 py-2 rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg ${
              activeTool === 'list'
                ? 'bg-blue-600/80 text-white'
                : 'bg-white/5 dark:bg-gray-700/50 text-gray-700 dark:text-gray-300'
            }`}
          >
            List
          </button>
          <button
            onClick={() => setActiveTool('kanban')}
            className={`px-4 py-2 rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg ${
              activeTool === 'kanban'
                ? 'bg-blue-600/80 text-white'
                : 'bg-white/5 dark:bg-gray-700/50 text-gray-700 dark:text-gray-300'
            }`}
          >
            Kanban
          </button>
          <button
            onClick={() => setActiveTool('gantt')}
            className={`px-4 py-2 rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg ${
              activeTool === 'gantt'
                ? 'bg-blue-600/80 text-white'
                : 'bg-white/5 dark:bg-gray-700/50 text-gray-700 dark:text-gray-300'
            }`}
          >
            Gantt
          </button>
        </div>
      </div>

      <div className="min-h-[500px] bg-white/5 dark:bg-gray-700/50 rounded-lg p-6">
        {activeTool === 'calendar' && <Calendar />}
        {activeTool === 'kanban' && <Kanban />}
        {activeTool === 'gantt' && <Gantt />}
        {activeTool === 'list' && <ListView />}
      </div>
    </motion.div>
  );
}