'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/lib/auth/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import EmailConfirmation from './EmailConfirmation';
import AuthErrorDisplay from './AuthErrorDisplay';
import SecurityChallenge from './SecurityChallenge';
import styles from '@/components/animations/auth.module.css';

export default function LoginForm() {
  const searchParams = useSearchParams();
  const initialMode = searchParams?.get('mode') === 'signup' ? false : true;
  
  const [isLogin, setIsLogin] = useState(initialMode);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [showEmailConfirmation, setShowEmailConfirmation] = useState(false);
  const [signupEmail, setSignupEmail] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [confirmationSource, setConfirmationSource] = useState<'signup' | 'login'>('signup');
  const [validationErrors, setValidationErrors] = useState<{email?: string; password?: string}>({});
  const [showSecurityChallenge, setShowSecurityChallenge] = useState(false);
  const [challengeCompleted, setChallengeCompleted] = useState(false);
  const [securityStatus, setSecurityStatus] = useState<any>(null);
  const { signIn, signUp, signInWithGoogle } = useAuth();
  const router = useRouter();

  // Check security status on component mount
  useEffect(() => {
    checkSecurityStatus();
  }, []);

  const checkSecurityStatus = async () => {
    try {
      const response = await fetch('/api/security/status?endpoint=/login');
      const data = await response.json();

      if (data.success) {
        setSecurityStatus(data.security);
        setChallengeCompleted(data.security.hasValidChallenge);

        // If rate limited or blocked, show error
        if (data.security.rateLimit && !data.security.rateLimit.allowed) {
          if (data.security.rateLimit.isBlocked) {
            setError(`Access temporarily blocked. Please try again ${data.security.rateLimit.blockUntil ? 'after ' + new Date(data.security.rateLimit.blockUntil).toLocaleString() : 'later'}.`);
          } else {
            setError(`Too many attempts. Please wait before trying again.`);
          }
        }
      }
    } catch (error) {
      console.error('Error checking security status:', error);
      // Continue without security check if it fails
    }
  };

  // Real-time validation
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email) return 'Email is required';
    if (!emailRegex.test(email)) return 'Please enter a valid email address';
    return '';
  };

  const validatePassword = (password: string, isSignup = false) => {
    if (!password) return 'Password is required';
    if (isSignup && password.length < 6) return 'Password must be at least 6 characters long';
    return '';
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setEmail(value);
    setError('');
    setSuccessMessage('');
    
    // Clear email validation error when user starts typing
    if (validationErrors.email) {
      setValidationErrors(prev => ({ ...prev, email: undefined }));
    }
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPassword(value);
    setError('');
    setSuccessMessage('');
    
    // Clear password validation error when user starts typing
    if (validationErrors.password) {
      setValidationErrors(prev => ({ ...prev, password: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccessMessage('');
    setValidationErrors({});

    // Validation
    const emailError = validateEmail(email);
    const passwordError = validatePassword(password, !isLogin);

    if (emailError || passwordError) {
      setValidationErrors({
        email: emailError,
        password: passwordError
      });
      return;
    }

    // Check if security challenge is required for login
    if (isLogin && !challengeCompleted) {
      setShowSecurityChallenge(true);
      return;
    }

    setLoading(true);

    try {
      if (isLogin) {
        console.log('Attempting login with:', email);
        await signIn(email, password);
        setSuccessMessage('Login successful! Redirecting...');
        setTimeout(() => {
          router.push('/profile');
        }, 1000);
      } else {
        console.log('Attempting signup with:', email);
        await signUp(email, password);
        // If signup is successful, show email confirmation screen
        setSignupEmail(email);
        setConfirmationSource('signup');
        setSuccessMessage('Account created successfully! Please check your email to confirm your account.');
        setShowEmailConfirmation(true);
        setLoading(false); // Set loading to false here for signup
        return; // Return early to prevent setting loading to false again
      }
    } catch (err: unknown) {
      console.error(isLogin ? 'Login error:' : 'Registration error:', err);
      const error = err as Error;
      const errorMessage = error.message || 'An unknown error occurred';

      // Enhanced error handling with more specific messages
      if (errorMessage.includes('Invalid login credentials')) {
        setError('Incorrect email or password. Please check your credentials and try again.');
      } else if (errorMessage.includes('Email not confirmed')) {
        // For unconfirmed email during login, show confirmation screen
        setSignupEmail(email);
        setConfirmationSource('login');
        setShowEmailConfirmation(true);
        setLoading(false);
        return; // Return early to prevent setting loading to false again
      } else if (errorMessage.includes('User already registered')) {
        setError('An account with this email already exists. Please sign in instead.');
        // Auto-switch to login mode
        setTimeout(() => {
          setIsLogin(true);
          setError('Account found. Please sign in with your credentials.');
        }, 2000);
      } else if (errorMessage.includes('Password should be at least')) {
        setValidationErrors({ password: 'Password must be at least 6 characters long' });
      } else if (errorMessage.includes('Invalid email') || errorMessage.includes('invalid email')) {
        setValidationErrors({ email: 'Please enter a valid email address' });
      } else if (errorMessage.includes('too many requests') || errorMessage.includes('Too many')) {
        setError('Too many attempts. Please wait a few minutes before trying again.');
      } else if (errorMessage.includes('signup disabled')) {
        setError('New registrations are temporarily disabled. Please try again later.');
      } else if (errorMessage.includes('Database error')) {
        setError('Temporary server issue. Please try again in a moment.');
      } else if (errorMessage.includes('Network') || errorMessage.includes('fetch')) {
        setError('Connection issue. Please check your internet and try again.');
      } else if (errorMessage.includes('timeout')) {
        setError('Request timed out. Please try again.');
      } else {
        // Show a generic error for unexpected cases
        setError('Something went wrong. Please try again or contact support if the issue persists.');
        console.error('Unexpected auth error:', errorMessage);
      }
      
      // Set loading to false for all error cases
      setLoading(false);
    }
  };

  // TODO: Add Google Sign In handlers
  const handleGoogleSignIn = async () => {
    try {
      setError('');
      setSuccessMessage('');
      setLoading(true);
      
      console.log('Initiating Google Sign In...');
      await signInWithGoogle();
      
      // Success message will be handled by auth state change
      console.log('Google Sign In request sent successfully');
      
    } catch (err: unknown) {
      console.error('Google Sign In error:', err);
      const error = err as Error;
      const errorMessage = error.message || 'Failed to sign in with Google';
      
      // Provide specific error messages for common Google auth issues
      if (errorMessage.includes('popup closed') || errorMessage.includes('cancelled')) {
        setError('Google sign-in was cancelled. Please try again.');
      } else if (errorMessage.includes('blocked') || errorMessage.includes('popup')) {
        setError('Pop-up was blocked. Please allow pop-ups for this site and try again.');
      } else if (errorMessage.includes('network') || errorMessage.includes('offline')) {
        setError('Network error. Please check your internet connection and try again.');
      } else if (errorMessage.includes('Invalid Refresh Token')) {
        setError('Authentication session expired. Please try signing in again.');
      } else {
        setError(errorMessage);
      }
      
      setLoading(false);
    }
  };

  const handleBackToSignup = () => {
    setShowEmailConfirmation(false);
    setSignupEmail('');
    setError('');
    setSuccessMessage('');
    setConfirmationSource('signup');
    setLoading(false);
  };

  const handleToggleMode = () => {
    setIsLogin(!isLogin);
    setError('');
    setSuccessMessage('');
    setValidationErrors({});
    setEmail('');
    setPassword('');
    setShowSecurityChallenge(false);
    setChallengeCompleted(false);
  };

  const handleChallengeComplete = (challengeId: string) => {
    console.log('Security challenge completed:', challengeId);
    setChallengeCompleted(true);
    setShowSecurityChallenge(false);
    setSuccessMessage('Security verification completed! You can now sign in.');

    // Auto-submit the form after challenge completion
    setTimeout(() => {
      if (email && password) {
        handleSubmit(new Event('submit') as any);
      }
    }, 1000);
  };

  const handleChallengeError = (error: string) => {
    console.error('Security challenge error:', error);
    setError(`Security verification failed: ${error}`);
    setShowSecurityChallenge(false);
    setChallengeCompleted(false);
  };

  const handleBackFromChallenge = () => {
    setShowSecurityChallenge(false);
    setError('');
  };

  // Show email confirmation screen after successful signup OR for unconfirmed email during login
  if (showEmailConfirmation) {
    return <EmailConfirmation email={signupEmail} onBack={handleBackToSignup} source={confirmationSource} />;
  }

  // Show security challenge if required
  if (showSecurityChallenge) {
    return (
      <div className={`max-w-md mx-auto p-8 rounded-xl shadow-2xl border border-white/20 dark:border-gray-700/30 transition-all duration-300 hover:shadow-indigo-500/10 ${styles['glass-morphism']} bg-white/10 dark:bg-gray-800/30`}>
        <div className="text-center mb-6">
          <h2 className={`text-2xl font-bold bg-gradient-to-r from-indigo-500 to-purple-500 bg-clip-text text-transparent mb-2 ${styles['animate-fade-in']}`}>
            Security Verification
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Complete the security challenge to continue with your login
          </p>
        </div>

        <SecurityChallenge
          onComplete={handleChallengeComplete}
          onError={handleChallengeError}
          className="mb-6"
        />

        <button
          onClick={handleBackFromChallenge}
          className="w-full py-2 px-4 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
        >
          ← Back to Login
        </button>
      </div>
    );
  }

  return (
    <div className={`max-w-md mx-auto p-8 rounded-xl shadow-2xl border border-white/20 dark:border-gray-700/30 transition-all duration-300 hover:shadow-indigo-500/10 ${styles['glass-morphism']} bg-white/10 dark:bg-gray-800/30`}>
      <h2 className={`text-3xl font-bold text-center bg-gradient-to-r from-indigo-500 to-purple-500 bg-clip-text text-transparent mb-8 ${styles['animate-fade-in']}`}>
        {isLogin ? 'Welcome Back' : 'Create Account'}
      </h2>

      {/* Security Status Indicator */}
      {isLogin && (
        <div className="mb-6 p-3 rounded-lg border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Security Verification
            </span>
            <div className="flex items-center">
              {challengeCompleted ? (
                <>
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  <span className="text-xs text-green-600 dark:text-green-400">Verified</span>
                </>
              ) : (
                <>
                  <div className="w-2 h-2 bg-orange-500 rounded-full mr-2"></div>
                  <span className="text-xs text-orange-600 dark:text-orange-400">Required</span>
                </>
              )}
            </div>
          </div>
          {!challengeCompleted && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              You'll need to complete a security challenge before signing in
            </p>
          )}
        </div>
      )}
      
      {/* Success Message */}
      {successMessage && (
        <div className={`mb-6 p-4 bg-green-500/10 border border-green-500/20 text-green-500 rounded-lg flex items-center space-x-2 ${styles['animate-fade-in']}`}>
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
          <span className="text-sm">{successMessage}</span>
        </div>
      )}

      {/* Enhanced Error Display */}
      {error && (
        <AuthErrorDisplay
          error={error}
          onRetry={() => {
            setError('');
            setValidationErrors({});
          }}
          onSwitchMode={() => {
            setIsLogin(true);
            setError('');
            setValidationErrors({});
          }}
          onRequestConfirmation={() => {
            setSignupEmail(email);
            setConfirmationSource('login');
            setShowEmailConfirmation(true);
            setError('');
          }}
          showConfirmationOption={error.includes('Email not confirmed') || error.includes('confirm your email')}
        />
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Email Address
          </label>
          <input
            type="email"
            value={email}
            onChange={handleEmailChange}
            placeholder="Enter your email"
            className={`w-full px-4 py-3 bg-white/5 border rounded-lg focus:ring-2 focus:ring-indigo-500/50 focus:border-indigo-500/50 transition-all duration-200 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 ${
              validationErrors.email 
                ? 'border-red-500/50 bg-red-500/5 dark:bg-red-500/10' 
                : 'border-white/10 dark:border-gray-700/50 dark:bg-gray-800/50'
            }`}
            required
            disabled={loading}
            autoComplete={isLogin ? 'email' : 'email'}
          />
          {validationErrors.email && (
            <p className="mt-1 text-sm text-red-500 flex items-center">
              <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              {validationErrors.email}
            </p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Password
            {!isLogin && <span className="text-xs text-gray-500 ml-1">(minimum 6 characters)</span>}
          </label>
          <input
            type="password"
            value={password}
            onChange={handlePasswordChange}
            placeholder={isLogin ? "Enter your password" : "Create a password (min. 6 characters)"}
            className={`w-full px-4 py-3 bg-white/5 border rounded-lg focus:ring-2 focus:ring-indigo-500/50 focus:border-indigo-500/50 transition-all duration-200 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500 ${
              validationErrors.password 
                ? 'border-red-500/50 bg-red-500/5 dark:bg-red-500/10' 
                : 'border-white/10 dark:border-gray-700/50 dark:bg-gray-800/50'
            }`}
            required
            disabled={loading}
            autoComplete={isLogin ? 'current-password' : 'new-password'}
            minLength={isLogin ? undefined : 6}
          />
          {validationErrors.password && (
            <p className="mt-1 text-sm text-red-500 flex items-center">
              <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              {validationErrors.password}
            </p>
          )}
        </div>

        <button
          type="submit"
          disabled={loading || !email || !password}
          className="w-full py-3 px-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg font-medium hover:from-indigo-700 hover:to-purple-700 transform hover:scale-[1.02] transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:hover:scale-100 disabled:hover:from-indigo-600 disabled:hover:to-purple-600"
          style={{
            boxShadow: loading ? '0 0 15px rgba(99, 102, 241, 0.5)' : 'none'
          }}
        >
          <span className="flex items-center justify-center">
            {loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {isLogin ? 'Signing In...' : 'Creating Account...'}
              </>
            ) : (isLogin ? 'Sign In' : 'Create Account')}
          </span>
        </button>

        {/* Social Authentication Divider */}
        <div className="mt-6">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300 dark:border-gray-600" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">
                Or continue with
              </span>
            </div>
          </div>
        </div>

        {/* Google Sign In Button - TODO: Implement */}
        <button
          type="button"
          onClick={handleGoogleSignIn}
          disabled={loading}
          className="w-full mt-4 py-3 px-4 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
        >
          <svg className="w-5 h-5" viewBox="0 0 24 24">
            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
          </svg>
          <span>
            {isLogin ? 'Sign in with Google' : 'Sign up with Google'}
          </span>
        </button>
      </form>

      <div className="mt-6 text-center">
        <button
          onClick={handleToggleMode}
          disabled={loading}
          className="text-indigo-500 hover:text-indigo-600 dark:text-indigo-400 dark:hover:text-indigo-300 transition-colors duration-200 disabled:opacity-50 font-medium"
        >
          {isLogin ? 'Need an account? Sign up' : 'Already have an account? Sign in'}
        </button>
      </div>

      {/* Additional help text for signup */}
      {!isLogin && (
        <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <p className="text-xs text-blue-700 dark:text-blue-300">
            <strong>Note:</strong> After creating your account, you&apos;ll receive a confirmation email. 
            Please check your inbox and click the confirmation link to activate your account.
          </p>
        </div>
      )}

      {/* Password requirements hint for signup */}
      {!isLogin && password.length > 0 && password.length < 6 && (
        <div className="mt-2 p-2 bg-yellow-50 dark:bg-yellow-900/20 rounded border border-yellow-200 dark:border-yellow-800">
          <p className="text-xs text-yellow-700 dark:text-yellow-300">
            Password needs {6 - password.length} more character{6 - password.length !== 1 ? 's' : ''}
          </p>
        </div>
      )}

      {/* Help text for login issues */}
      {isLogin && (
        <div className="mt-4 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Having trouble signing in? Make sure your email is confirmed and check your password.
          </p>
        </div>
      )}
    </div>
  );
}