'use client';

import { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';

interface Position {
  x: number;
  y: number;
}

interface Challenge {
  id: string;
  type: 'drag_drop' | 'slider' | 'pattern_match';
  currentPosition: Position;
  tolerance: number;
  instructions: string;
  theme: 'light' | 'dark';
}

interface SecurityChallengeProps {
  onComplete: (challengeId: string) => void;
  onError: (error: string) => void;
  className?: string;
}

export default function SecurityChallenge({ onComplete, onError, className = '' }: SecurityChallengeProps) {
  const [challenge, setChallenge] = useState<Challenge | null>(null);
  const [currentPosition, setCurrentPosition] = useState<Position>({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [attempts, setAttempts] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const dragElementRef = useRef<HTMLDivElement>(null);

  // Load challenge on component mount
  useEffect(() => {
    loadChallenge();
  }, []);

  const loadChallenge = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/challenge');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to load challenge');
      }

      if (data.success && data.challenge) {
        setChallenge(data.challenge);
        setCurrentPosition(data.challenge.currentPosition);
      } else {
        throw new Error('Invalid challenge data');
      }
    } catch (error) {
      console.error('Error loading challenge:', error);
      onError(error instanceof Error ? error.message : 'Failed to load security challenge');
    } finally {
      setIsLoading(false);
    }
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!challenge) return;
    setIsDragging(true);
    e.preventDefault();
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !containerRef.current || !challenge) return;

    const rect = containerRef.current.getBoundingClientRect();
    const newX = e.clientX - rect.left - 25; // Adjust for element center
    const newY = e.clientY - rect.top - 25;

    // Constrain to container bounds
    const maxX = rect.width - 50;
    const maxY = rect.height - 50;

    setCurrentPosition({
      x: Math.max(0, Math.min(newX, maxX)),
      y: Math.max(0, Math.min(newY, maxY))
    });
  };

  const handleMouseUp = () => {
    if (!isDragging) return;
    setIsDragging(false);
    submitSolution();
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    if (!challenge) return;
    setIsDragging(true);
    e.preventDefault();
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging || !containerRef.current || !challenge) return;

    const touch = e.touches[0];
    const rect = containerRef.current.getBoundingClientRect();
    const newX = touch.clientX - rect.left - 25;
    const newY = touch.clientY - rect.top - 25;

    const maxX = rect.width - 50;
    const maxY = rect.height - 50;

    setCurrentPosition({
      x: Math.max(0, Math.min(newX, maxX)),
      y: Math.max(0, Math.min(newY, maxY))
    });
  };

  const handleTouchEnd = () => {
    if (!isDragging) return;
    setIsDragging(false);
    submitSolution();
  };

  const submitSolution = async () => {
    if (!challenge || isSubmitting) return;

    try {
      setIsSubmitting(true);
      const response = await fetch('/api/challenge', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          challengeId: challenge.id,
          position: currentPosition
        })
      });

      const data = await response.json();

      if (data.success) {
        onComplete(challenge.id);
      } else {
        setAttempts(prev => prev + 1);
        if (data.attemptsRemaining <= 0) {
          onError('Maximum attempts exceeded. Please refresh and try again.');
        } else {
          onError(`${data.message} (${data.attemptsRemaining} attempts remaining)`);
        }
      }
    } catch (error) {
      console.error('Error submitting solution:', error);
      onError('Failed to verify solution. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getChallengeStyles = () => {
    if (!challenge) return {};

    switch (challenge.type) {
      case 'drag_drop':
        return {
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          borderRadius: '50%'
        };
      case 'slider':
        return {
          background: 'linear-gradient(90deg, #4facfe 0%, #00f2fe 100%)',
          borderRadius: '8px'
        };
      case 'pattern_match':
        return {
          background: 'linear-gradient(45deg, #fa709a 0%, #fee140 100%)',
          borderRadius: '12px'
        };
      default:
        return {
          background: '#3b82f6',
          borderRadius: '50%'
        };
    }
  };

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600 dark:text-gray-300">Loading security challenge...</span>
      </div>
    );
  }

  if (!challenge) {
    return (
      <div className={`text-center p-8 ${className}`}>
        <p className="text-red-600 dark:text-red-400">Failed to load security challenge</p>
        <button 
          onClick={loadChallenge}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className={`w-full max-w-md mx-auto ${className}`}>
      <div className="text-center mb-4">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">
          Security Verification
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {challenge.instructions}
        </p>
        {attempts > 0 && (
          <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
            Attempts: {attempts}
          </p>
        )}
      </div>

      <div 
        ref={containerRef}
        className="relative w-full h-64 bg-gray-100 dark:bg-gray-800 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden cursor-crosshair"
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Target area hint */}
        <div className="absolute top-4 right-4 w-8 h-8 border-2 border-green-500 rounded-full opacity-30"></div>
        
        {/* Draggable element */}
        <motion.div
          ref={dragElementRef}
          className="absolute w-12 h-12 cursor-grab active:cursor-grabbing shadow-lg"
          style={{
            left: currentPosition.x,
            top: currentPosition.y,
            ...getChallengeStyles()
          }}
          animate={{
            scale: isDragging ? 1.1 : 1,
            boxShadow: isDragging 
              ? '0 10px 25px rgba(0,0,0,0.3)' 
              : '0 4px 15px rgba(0,0,0,0.2)'
          }}
          transition={{ type: 'spring', stiffness: 300, damping: 30 }}
          onMouseDown={handleMouseDown}
          onTouchStart={handleTouchStart}
        >
          <div className="w-full h-full flex items-center justify-center text-white font-bold text-sm">
            {challenge.type === 'drag_drop' && '●'}
            {challenge.type === 'slider' && '▶'}
            {challenge.type === 'pattern_match' && '◆'}
          </div>
        </motion.div>

        {/* Loading overlay */}
        {isSubmitting && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
          </div>
        )}
      </div>

      <div className="mt-4 text-center">
        <p className="text-xs text-gray-500 dark:text-gray-400">
          Drag the element to the target area to continue
        </p>
      </div>
    </div>
  );
}
