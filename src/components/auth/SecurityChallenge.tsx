'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface Position {
  x: number;
  y: number;
}

interface Challenge {
  id: string;
  type: 'drag_drop' | 'slider' | 'pattern_match';
  currentPosition: Position;
  tolerance: number;
  instructions: string;
  theme: 'light' | 'dark';
}

interface SecurityChallengeProps {
  onComplete: (challengeId: string) => void;
  onError: (error: string) => void;
  className?: string;
}

export default function SecurityChallenge({ onComplete, onError, className = '' }: SecurityChallengeProps) {
  const [challenge, setChallenge] = useState<Challenge | null>(null);
  const [currentPosition, setCurrentPosition] = useState<Position>({ x: 0, y: 0 });
  const [targetPosition, setTargetPosition] = useState<Position>({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [attempts, setAttempts] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [distance, setDistance] = useState(0);
  const [isNearTarget, setIsNearTarget] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const dragElementRef = useRef<HTMLDivElement>(null);

  // Load challenge on component mount
  useEffect(() => {
    loadChallenge();
  }, []);

  // Show hint after 5 seconds
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowHint(true);
    }, 5000);
    return () => clearTimeout(timer);
  }, []);

  // Calculate distance and proximity to target
  useEffect(() => {
    if (targetPosition.x === 0 && targetPosition.y === 0) return;

    const dist = Math.sqrt(
      Math.pow(currentPosition.x - targetPosition.x, 2) +
      Math.pow(currentPosition.y - targetPosition.y, 2)
    );
    setDistance(dist);
    setIsNearTarget(dist <= (challenge?.tolerance || 15) * 2); // Show "near" when within 2x tolerance
  }, [currentPosition, targetPosition, challenge?.tolerance]);

  const loadChallenge = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/challenge');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to load challenge');
      }

      if (data.success && data.challenge) {
        setChallenge(data.challenge);
        setCurrentPosition(data.challenge.currentPosition);
        setTargetPosition(data.challenge.targetPosition);
      } else {
        throw new Error('Invalid challenge data');
      }
    } catch (error) {
      console.error('Error loading challenge:', error);
      onError(error instanceof Error ? error.message : 'Failed to load security challenge');
    } finally {
      setIsLoading(false);
    }
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!challenge) return;
    setIsDragging(true);
    e.preventDefault();
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !containerRef.current || !challenge) return;

    const rect = containerRef.current.getBoundingClientRect();
    const newX = e.clientX - rect.left - 25; // Adjust for element center
    const newY = e.clientY - rect.top - 25;

    // Constrain to container bounds
    const maxX = rect.width - 50;
    const maxY = rect.height - 50;

    setCurrentPosition({
      x: Math.max(0, Math.min(newX, maxX)),
      y: Math.max(0, Math.min(newY, maxY))
    });
  };

  const handleMouseUp = () => {
    if (!isDragging) return;
    setIsDragging(false);
    submitSolution();
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    if (!challenge) return;
    setIsDragging(true);
    e.preventDefault();
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging || !containerRef.current || !challenge) return;

    const touch = e.touches[0];
    const rect = containerRef.current.getBoundingClientRect();
    const newX = touch.clientX - rect.left - 25;
    const newY = touch.clientY - rect.top - 25;

    const maxX = rect.width - 50;
    const maxY = rect.height - 50;

    setCurrentPosition({
      x: Math.max(0, Math.min(newX, maxX)),
      y: Math.max(0, Math.min(newY, maxY))
    });
  };

  const handleTouchEnd = () => {
    if (!isDragging) return;
    setIsDragging(false);
    submitSolution();
  };

  const submitSolution = async () => {
    if (!challenge || isSubmitting) return;

    try {
      setIsSubmitting(true);
      const response = await fetch('/api/challenge', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          challengeId: challenge.id,
          position: currentPosition
        })
      });

      const data = await response.json();

      if (data.success) {
        onComplete(challenge.id);
      } else {
        setAttempts(prev => prev + 1);
        if (data.attemptsRemaining <= 0) {
          onError('Maximum attempts exceeded. Please refresh and try again.');
        } else {
          onError(`${data.message} (${data.attemptsRemaining} attempts remaining)`);
        }
      }
    } catch (error) {
      console.error('Error submitting solution:', error);
      onError('Failed to verify solution. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getChallengeStyles = () => {
    if (!challenge) return {};

    const baseStyle = {
      borderRadius: challenge.type === 'slider' ? '12px' : '50%'
    };

    switch (challenge.type) {
      case 'drag_drop':
        return {
          ...baseStyle,
          background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
          borderRadius: '50%'
        };
      case 'slider':
        return {
          ...baseStyle,
          background: 'linear-gradient(90deg, #06b6d4 0%, #0891b2 100%)',
          borderRadius: '12px'
        };
      case 'pattern_match':
        return {
          ...baseStyle,
          background: 'linear-gradient(45deg, #8b5cf6 0%, #7c3aed 100%)',
          borderRadius: '12px',
          transform: 'rotate(45deg)'
        };
      default:
        return {
          ...baseStyle,
          background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
          borderRadius: '50%'
        };
    }
  };

  const getHintText = () => {
    if (!challenge) return '';

    switch (challenge.type) {
      case 'drag_drop':
        return 'Click and hold the blue circle, then drag it to the green target area';
      case 'slider':
        return 'Click and drag the blue rectangle all the way to the right side';
      case 'pattern_match':
        return 'Drag the purple diamond to match the target position exactly';
      default:
        return 'Click and drag the element to the target area';
    }
  };

  if (isLoading) {
    return (
      <div className={`w-full max-w-lg mx-auto ${className}`}>
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-8 shadow-lg">
          <div className="flex flex-col items-center justify-center">
            <div className="relative">
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-200 dark:border-blue-800"></div>
              <div className="animate-spin rounded-full h-12 w-12 border-4 border-transparent border-t-blue-600 absolute top-0 left-0"></div>
            </div>
            <div className="mt-4 text-center">
              <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">
                Preparing Security Challenge
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Please wait while we set up your verification...
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!challenge) {
    return (
      <div className={`w-full max-w-lg mx-auto ${className}`}>
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-red-200 dark:border-red-800 p-8 shadow-lg">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">
              Challenge Loading Failed
            </h3>
            <p className="text-red-600 dark:text-red-400 mb-4">
              We couldn't load the security challenge. Please try again.
            </p>
            <button
              onClick={loadChallenge}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`w-full max-w-lg mx-auto ${className}`}>
      {/* Header with clear instructions */}
      <div className="text-center mb-6">
        <div className="flex items-center justify-center mb-3">
          <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mr-3">
            <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
          <h3 className="text-xl font-bold text-gray-800 dark:text-gray-200">
            Security Verification
          </h3>
        </div>

        <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-4 mb-4">
          <p className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
            {challenge.instructions}
          </p>
          <div className="flex items-center justify-center space-x-4 text-xs text-blue-600 dark:text-blue-400">
            <span className="flex items-center">
              <div className="w-3 h-3 bg-blue-500 rounded-full mr-1"></div>
              Drag this
            </span>
            <span>→</span>
            <span className="flex items-center">
              <div className="w-3 h-3 bg-green-500 rounded-full mr-1"></div>
              To here
            </span>
          </div>
        </div>

        {/* Distance feedback */}
        {distance > 0 && !isSubmitting && (
          <div className="mb-3">
            {isNearTarget ? (
              <div className="flex items-center justify-center text-green-600 dark:text-green-400">
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4" />
                </svg>
                <span className="text-sm font-medium">Very close! Drop it here!</span>
              </div>
            ) : (
              <div className="text-orange-600 dark:text-orange-400">
                <span className="text-sm">Keep dragging to the target area</span>
              </div>
            )}
          </div>
        )}

        {attempts > 0 && (
          <div className="flex items-center justify-center text-orange-600 dark:text-orange-400 mb-2">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <span className="text-sm font-medium">Attempt {attempts} of 3</span>
          </div>
        )}
      </div>

      {/* Challenge Area */}
      <div
        ref={containerRef}
        className="relative w-full h-80 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 border-2 border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden shadow-inner"
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Grid pattern for better visual reference */}
        <div className="absolute inset-0 opacity-20">
          <div className="w-full h-full" style={{
            backgroundImage: `
              linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px)
            `,
            backgroundSize: '20px 20px'
          }}></div>
        </div>

        {/* Target area with pulsing animation */}
        <motion.div
          className="absolute border-4 border-green-500 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center"
          style={{
            left: targetPosition.x - 30,
            top: targetPosition.y - 30,
            width: 60,
            height: 60
          }}
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.6, 0.8, 0.6]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          <div className="text-green-600 dark:text-green-400 font-bold text-lg">🎯</div>
        </motion.div>

        {/* Path line showing drag direction */}
        {isDragging && (
          <svg className="absolute inset-0 w-full h-full pointer-events-none">
            <line
              x1={currentPosition.x + 25}
              y1={currentPosition.y + 25}
              x2={targetPosition.x}
              y2={targetPosition.y}
              stroke={isNearTarget ? "#10b981" : "#6b7280"}
              strokeWidth="2"
              strokeDasharray="5,5"
              opacity="0.6"
            />
          </svg>
        )}

        {/* Draggable element with enhanced design */}
        <motion.div
          ref={dragElementRef}
          className="absolute cursor-grab active:cursor-grabbing shadow-xl border-4 border-white dark:border-gray-200"
          style={{
            left: currentPosition.x,
            top: currentPosition.y,
            width: 50,
            height: 50,
            ...getChallengeStyles()
          }}
          animate={{
            scale: isDragging ? 1.2 : 1,
            boxShadow: isDragging
              ? '0 20px 40px rgba(0,0,0,0.3)'
              : '0 8px 25px rgba(0,0,0,0.15)',
            rotate: isDragging ? [0, 5, -5, 0] : 0
          }}
          transition={{
            type: 'spring',
            stiffness: 300,
            damping: 30,
            rotate: { duration: 0.3, repeat: isDragging ? Infinity : 0 }
          }}
          onMouseDown={handleMouseDown}
          onTouchStart={handleTouchStart}
        >
          <div className="w-full h-full flex items-center justify-center text-white font-bold text-xl">
            {challenge.type === 'drag_drop' && '●'}
            {challenge.type === 'slider' && '▶'}
            {challenge.type === 'pattern_match' && '◆'}
          </div>

          {/* Glow effect when near target */}
          {isNearTarget && (
            <motion.div
              className="absolute inset-0 rounded-full bg-green-400"
              animate={{
                opacity: [0, 0.3, 0],
                scale: [1, 1.5, 1]
              }}
              transition={{
                duration: 1,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          )}
        </motion.div>

        {/* Success animation overlay */}
        <AnimatePresence>
          {isSubmitting && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 bg-green-500/20 flex items-center justify-center backdrop-blur-sm"
            >
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="bg-white dark:bg-gray-800 rounded-full p-6 shadow-2xl"
              >
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Bottom instructions and hints */}
      <div className="mt-6 space-y-3">
        <div className="text-center">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {challenge.type === 'slider' ? 'Click and drag horizontally' : 'Click and drag to move'}
          </p>
        </div>

        {/* Hint system */}
        <AnimatePresence>
          {showHint && !isSubmitting && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3"
            >
              <div className="flex items-start">
                <svg className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
                <div>
                  <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    💡 Tip: {getHintText()}
                  </p>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Progress indicator */}
        <div className="flex items-center justify-center space-x-2">
          <div className="flex space-x-1">
            {[1, 2, 3].map((step) => (
              <div
                key={step}
                className={`w-2 h-2 rounded-full transition-colors ${
                  step <= attempts + 1
                    ? 'bg-blue-500'
                    : 'bg-gray-300 dark:bg-gray-600'
                }`}
              />
            ))}
          </div>
          <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
            Step {Math.min(attempts + 1, 3)} of 3
          </span>
        </div>
      </div>
    </div>
  );
}
