'use client';

import { useState } from 'react';
import { useAuth } from '@/lib/auth/AuthContext';
import { useRouter } from 'next/navigation';
import { db } from '@/lib/supabase';
import { motion } from 'framer-motion';
import { FaExclamationTriangle, FaDownload, FaTrash, FaInfoCircle } from 'react-icons/fa';
import Link from 'next/link';

interface AccountDeletionSectionProps {
  className?: string;
}

export default function AccountDeletionSection({ className = '' }: AccountDeletionSectionProps) {
  const { user, logout } = useAuth();
  const router = useRouter();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleteStep, setDeleteStep] = useState<'initial' | 'confirm' | 'final'>('initial');
  const [isDeleting, setIsDeleting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [confirmText, setConfirmText] = useState('');
  const [deleteReason, setDeleteReason] = useState('');

  const handleExportData = async () => {
    if (!user?.id) return;
    
    setIsExporting(true);
    try {
      const userData = await db.exportUserData(user.id);
      
      // Create and download JSON file
      const dataStr = JSON.stringify(userData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `hieltech-data-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(url);
      
      alert('Your data has been exported and downloaded successfully!');
    } catch (error) {
      console.error('Error exporting data:', error);
      alert('Failed to export data. Please try again or contact support.');
    } finally {
      setIsExporting(false);
    }
  };

  const handleDeleteAccount = async () => {
    if (!user?.id) return;
    
    setIsDeleting(true);
    try {
      // First, request the deletion (for audit trail)
      await db.requestAccountDeletion(user.id, deleteReason);
      
      // Then proceed with actual deletion
      const result = await db.deleteUserAccount(user.id);
      
      if (result.success) {
        alert('Your account has been successfully deleted. You will now be signed out.');
        await logout();
        router.push('/');
      } else {
        alert(`Account deletion failed: ${result.error}`);
      }
    } catch (error) {
      console.error('Error deleting account:', error);
      alert('Failed to delete account. Please try again or contact support.');
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
      setDeleteStep('initial');
      setConfirmText('');
      setDeleteReason('');
    }
  };

  const resetDeleteFlow = () => {
    setShowDeleteConfirm(false);
    setDeleteStep('initial');
    setConfirmText('');
    setDeleteReason('');
  };

  const canProceedToFinal = confirmText.toLowerCase() === 'delete my account';

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Data Export Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6"
      >
        <div className="flex items-start space-x-3">
          <FaInfoCircle className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
          <div className="flex-1">
            <h3 className="text-lg font-medium text-blue-900 dark:text-blue-300 mb-2">
              Export Your Data
            </h3>
            <p className="text-blue-800 dark:text-blue-200 text-sm mb-4">
              Download a complete copy of your data before deleting your account. This includes your profile, HielLinks, and other content.
            </p>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={handleExportData}
              disabled={isExporting}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isExporting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>Exporting...</span>
                </>
              ) : (
                <>
                  <FaDownload className="w-4 h-4" />
                  <span>Export My Data</span>
                </>
              )}
            </motion.button>
          </div>
        </div>
      </motion.div>

      {/* Privacy Links */}
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
        <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
          Privacy & Data Information
        </h4>
        <div className="flex flex-wrap gap-4 text-sm">
          <Link 
            href="/privacy" 
            className="text-blue-600 dark:text-blue-400 hover:underline"
          >
            Privacy Policy
          </Link>
          <Link 
            href="/data-deletion" 
            className="text-blue-600 dark:text-blue-400 hover:underline"
          >
            Data Deletion Guide
          </Link>
          <Link 
            href="/terms" 
            className="text-blue-600 dark:text-blue-400 hover:underline"
          >
            Terms of Service
          </Link>
        </div>
      </div>

      {/* Account Deletion Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6"
      >
        <div className="flex items-start space-x-3">
          <FaExclamationTriangle className="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5" />
          <div className="flex-1">
            <h3 className="text-lg font-medium text-red-900 dark:text-red-300 mb-2">
              Danger Zone
            </h3>
            <p className="text-red-800 dark:text-red-200 text-sm mb-4">
              Once you delete your account, there is no going back. This action cannot be undone.
            </p>

            {!showDeleteConfirm ? (
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => setShowDeleteConfirm(true)}
                className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
              >
                <FaTrash className="w-4 h-4" />
                <span>Delete My Account</span>
              </motion.button>
            ) : (
              <div className="space-y-4">
                {deleteStep === 'initial' && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    className="space-y-4"
                  >
                    <div className="bg-red-100 dark:bg-red-900/30 rounded-lg p-4">
                      <h4 className="font-medium text-red-900 dark:text-red-200 mb-2">
                        This will permanently delete:
                      </h4>
                      <ul className="text-sm text-red-800 dark:text-red-300 space-y-1">
                        <li>• Your profile and account information</li>
                        <li>• All HielLinks profiles and content</li>
                        <li>• Chat messages and conversations</li>
                        <li>• Team applications and submissions</li>
                        <li>• Social media drafts and scheduled posts</li>
                        <li>• Analytics data and insights</li>
                        <li>• Any uploaded files and media</li>
                      </ul>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-red-900 dark:text-red-200 mb-2">
                        Why are you deleting your account? (Optional)
                      </label>
                      <textarea
                        value={deleteReason}
                        onChange={(e) => setDeleteReason(e.target.value)}
                        placeholder="Help us improve by sharing your reason..."
                        className="w-full px-3 py-2 border border-red-300 dark:border-red-700 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                        rows={3}
                      />
                    </div>

                    <div className="flex space-x-3">
                      <button
                        onClick={() => setDeleteStep('confirm')}
                        className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                      >
                        Continue
                      </button>
                      <button
                        onClick={resetDeleteFlow}
                        className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                      >
                        Cancel
                      </button>
                    </div>
                  </motion.div>
                )}

                {deleteStep === 'confirm' && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    className="space-y-4"
                  >
                    <div className="bg-red-100 dark:bg-red-900/30 rounded-lg p-4">
                      <h4 className="font-medium text-red-900 dark:text-red-200 mb-2">
                        ⚠️ Final Warning
                      </h4>
                      <p className="text-sm text-red-800 dark:text-red-300">
                        This action will permanently delete your account and all associated data. 
                        This cannot be undone. Please make sure you have exported any data you wish to keep.
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-red-900 dark:text-red-200 mb-2">
                        Type &quot;DELETE MY ACCOUNT&quot; to confirm:
                      </label>
                      <input
                        type="text"
                        value={confirmText}
                        onChange={(e) => setConfirmText(e.target.value)}
                        placeholder="DELETE MY ACCOUNT"
                        className="w-full px-3 py-2 border border-red-300 dark:border-red-700 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                      />
                    </div>

                    <div className="flex space-x-3">
                      <button
                        onClick={() => setDeleteStep('final')}
                        disabled={!canProceedToFinal}
                        className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white rounded-lg transition-colors disabled:cursor-not-allowed"
                      >
                        I Understand, Delete My Account
                      </button>
                      <button
                        onClick={() => setDeleteStep('initial')}
                        className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                      >
                        Back
                      </button>
                    </div>
                  </motion.div>
                )}

                {deleteStep === 'final' && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    className="space-y-4"
                  >
                    <div className="bg-red-100 dark:bg-red-900/30 rounded-lg p-4 text-center">
                      <h4 className="font-medium text-red-900 dark:text-red-200 mb-2">
                        🗑️ Account Deletion
                      </h4>
                      <p className="text-sm text-red-800 dark:text-red-300 mb-4">
                        You are about to permanently delete your account. This action cannot be undone.
                      </p>
                      
                      <div className="flex justify-center space-x-3">
                        <button
                          onClick={handleDeleteAccount}
                          disabled={isDeleting}
                          className="flex items-center space-x-2 px-6 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white rounded-lg transition-colors disabled:cursor-not-allowed"
                        >
                          {isDeleting ? (
                            <>
                              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                              <span>Deleting...</span>
                            </>
                          ) : (
                            <>
                              <FaTrash className="w-4 h-4" />
                              <span>Delete Forever</span>
                            </>
                          )}
                        </button>
                        <button
                          onClick={resetDeleteFlow}
                          disabled={isDeleting}
                          className="px-6 py-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  </motion.div>
                )}
              </div>
            )}
          </div>
        </div>
      </motion.div>
    </div>
  );
} 