'use client';

import { useState, useRef, useEffect } from 'react';
import { useAuth } from '@/lib/auth/AuthContext';
import { useRouter } from 'next/navigation';
import { db } from '@/lib/supabase';
import { motion } from 'framer-motion';
import { FaUser, FaUpload, FaTrash, FaDownload, FaExclamationTriangle, FaInfoCircle } from 'react-icons/fa';
import Image from 'next/image';
import Link from 'next/link';
import TaskTools from '@/components/tools/TaskTools';
import ApplicantChat from '@/components/chat/ApplicantChat';
import SupportChat from '@/components/chat/SupportChat';
import { EncryptionService } from '@/lib/services/encryptionService';

interface ProfilePictureManagerProps {
  onUpdate?: () => void;
}

interface UserProfileData {
  display_name: string;
  bio: string;
  company: string;
  position: string;
  [key: string]: string;
}

interface QuickAction {
  title: string;
  description: string;
  icon: string;
  color: string;
  href?: string;
  action?: () => void;
  disabled?: boolean;
  comingSoon?: boolean;
  badge?: string | null;
}

function ProfilePictureManager({ onUpdate }: ProfilePictureManagerProps) {
  const { user, profile } = useAuth();
  const [uploading, setUploading] = useState(false);
  const [loadingGoogle, setLoadingGoogle] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !user) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      alert('File size must be less than 5MB');
      return;
    }

    setUploading(true);
    try {
      // Upload the file
      const avatarUrl = await db.uploadProfilePicture(file, user.id);
      if (!avatarUrl) {
        throw new Error('Failed to upload image');
      }

      // Update the profile
      await db.updateProfilePicture(user.id, avatarUrl);
      
      onUpdate?.();
    } catch {
      alert('Failed to upload profile picture');
    } finally {
      setUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleUseGooglePicture = async () => {
    if (!user) return;

    setLoadingGoogle(true);
    try {
      const googleAvatarUrl = await db.getGoogleProfilePicture(user.id);
      if (!googleAvatarUrl) {
        alert('No Google profile picture found. Please sign in with Google to use this feature.');
        return;
      }

      await db.updateProfilePicture(user.id, googleAvatarUrl);
      onUpdate?.();
    } catch {
      alert('Failed to use Google profile picture');
    } finally {
      setLoadingGoogle(false);
    }
  };

  const handleDeletePicture = async () => {
    if (!user || !confirm('Are you sure you want to remove your profile picture?')) return;

    try {
      await db.deleteProfilePicture(user.id);
      onUpdate?.();
    } catch {
      alert('Failed to delete profile picture');
    }
  };

  return (
    <div className="space-y-4">
      {/* Current Avatar Display */}
      <div className="flex items-center space-x-4">
        <div className="relative">
          <div className="w-20 h-20 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center overflow-hidden">
            {profile?.avatar_url ? (
              <Image
                src={profile.avatar_url}
                alt="Profile picture"
                width={80}
                height={80}
                className="w-full h-full object-cover"
              />
            ) : (
              <FaUser className="w-8 h-8 text-gray-400" />
            )}
          </div>
        </div>
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Profile Picture
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Upload a new picture or use your Google profile picture
          </p>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-3">
        {/* Upload Button */}
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={() => fileInputRef.current?.click()}
          disabled={uploading}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {uploading ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              <span>Uploading...</span>
            </>
          ) : (
            <>
              <FaUpload className="w-4 h-4" />
              <span>Upload New</span>
            </>
          )}
        </motion.button>

        {/* Google Picture Button */}
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={handleUseGooglePicture}
          disabled={loadingGoogle}
          className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {loadingGoogle ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              <span>Loading...</span>
            </>
          ) : (
            <>
              <FaDownload className="w-4 h-4" />
              <span>Use Google Picture</span>
            </>
          )}
        </motion.button>

        {/* Delete Button */}
        {profile?.avatar_url && (
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleDeletePicture}
            className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
          >
            <FaTrash className="w-4 h-4" />
            <span>Remove</span>
          </motion.button>
        )}
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/jpg,image/png,image/webp,image/gif"
        onChange={handleFileUpload}
        className="hidden"
      />

      {/* Upload Guidelines */}
      <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
        <p>• Supported formats: JPEG, PNG, WebP, GIF</p>
        <p>• Maximum file size: 5MB</p>
        <p>• For best results, use a square image (1:1 aspect ratio)</p>
      </div>
    </div>
  );
}

export default function UserProfile() {
  const { user, profile: userProfile, logout } = useAuth();
  const router = useRouter();
  const [profile, setProfile] = useState<UserProfileData>({
    display_name: '',
    bio: '',
    company: '',
    position: ''
  });
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [activeSection, setActiveSection] = useState<'overview' | 'chat' | 'tools'>('overview');
  
  // Account deletion states
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleteStep, setDeleteStep] = useState<'initial' | 'confirm' | 'final'>('initial');
  const [isDeleting, setIsDeleting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [confirmText, setConfirmText] = useState('');
  const [deleteReason, setDeleteReason] = useState('');


  useEffect(() => {
    if (user && userProfile) {
      setProfile({
        display_name: userProfile.display_name || '',
        bio: userProfile.bio || '',
        company: userProfile.company || '',
        position: userProfile.position || ''
      });
    }
  }, [user, userProfile]);

  const handleSave = async () => {
    if (!user?.id) return;

    setIsSaving(true);
    try {
      // Encrypt sensitive profile fields
      const encryptedFields = await EncryptionService.encryptFields(
        profile,
        ['display_name', 'bio', 'company', 'position'],
        'profile_encryption_key'
      );

      const profileData = {
        display_name: profile.display_name,
        bio: profile.bio,
        company: profile.company,
        position: profile.position,
        encrypted_fields: encryptedFields.encrypted_fields,
        encryption_version: 1
      };

      const result = await db.updateProfile(user.id, profileData);
      if (result) {
        setIsEditing(false);
      }
    } catch {
      alert('Failed to update profile');
    } finally {
      setIsSaving(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProfile(prev => ({ ...prev, [name]: value }));
  };

  const handleProfileUpdate = () => {
    // Force a re-render to show updated profile data
    window.location.reload();
  };

  // Account deletion handlers
  const handleExportData = async () => {
    if (!user?.id) return;
    
    setIsExporting(true);
    try {
      const userData = await db.exportUserData(user.id);
      
      // Create and download JSON file
      const dataStr = JSON.stringify(userData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `hieltech-data-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(url);
      
      alert('Your data has been exported and downloaded successfully!');
    } catch (error) {
      console.error('Error exporting data:', error);
      alert('Failed to export data. Please try again or contact support.');
    } finally {
      setIsExporting(false);
    }
  };

  const handleDeleteAccount = async () => {
    if (!user?.id) return;
    
    setIsDeleting(true);
    try {
      // First, request the deletion (for audit trail)
      await db.requestAccountDeletion(user.id, deleteReason);
      
      // Then proceed with actual deletion
      const result = await db.deleteUserAccount(user.id);
      
      if (result.success) {
        alert('Your account has been successfully deleted. You will now be signed out.');
        await logout();
        router.push('/');
      } else {
        alert(`Account deletion failed: ${result.error}`);
      }
    } catch (error) {
      console.error('Error deleting account:', error);
      alert('Failed to delete account. Please try again or contact support.');
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
      setDeleteStep('initial');
      setConfirmText('');
      setDeleteReason('');
    }
  };

  const resetDeleteFlow = () => {
    setShowDeleteConfirm(false);
    setDeleteStep('initial');
    setConfirmText('');
    setDeleteReason('');
  };

  const canProceedToFinal = confirmText.toLowerCase() === 'delete my account';

  if (!user || !userProfile) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            No Profile Found
          </h2>
          <p className="text-gray-600 dark:text-gray-300">
            Please sign in to view your profile.
          </p>
        </div>
      </div>
    );
  }

  // Quick actions for easy navigation
  const quickActions: QuickAction[] = [
    {
      title: 'HielLinks',
      description: 'Manage your business profiles',
      icon: '🔗',
      href: '/profile/hiellinks',
      color: 'bg-blue-500',
      badge: null
    },
    {
      title: 'Chat',
      description: 'Team communication',
      icon: '💬',
      action: () => setActiveSection('chat'),
      color: 'bg-green-500',
      badge: 'Live'
    },
    {
      title: 'Task Tools',
      description: 'Project management',
      icon: '⚡',
      action: () => setActiveSection('tools'),
      color: 'bg-purple-500'
    },
    {
      title: 'Social Planner',
      description: 'Social media management',
      icon: '📱',
      disabled: true,
      color: 'bg-orange-400',
      comingSoon: true
    },
    {
      title: 'Unified Dashboard',
      description: 'All-in-one business hub',
      icon: '🎯',
      disabled: true,
      color: 'bg-indigo-400',
      comingSoon: true
    },
    {
      title: 'Analytics Suite',
      description: 'Cross-platform insights',
      icon: '📊',
      disabled: true,
      color: 'bg-pink-400',
      comingSoon: true
    }
  ];

  return (
    <div className="space-y-8">
      {/* Main Dashboard Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        
        {/* Profile Information Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="lg:col-span-2 bg-white/10 dark:bg-gray-800/30 backdrop-blur-lg rounded-xl shadow-xl border border-white/20 dark:border-gray-700/30 p-6"
        >
          {/* Profile Header */}
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-center space-x-4">
              {/* Avatar */}
              <div className="w-16 h-16 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center overflow-hidden">
                {userProfile?.avatar_url ? (
                  <Image
                    src={userProfile.avatar_url}
                    alt="Profile picture"
                    width={64}
                    height={64}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xl font-bold">
                    {profile.display_name ? profile.display_name.charAt(0).toUpperCase() : user.email?.charAt(0).toUpperCase()}
                  </div>
                )}
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {profile.display_name || 'Set your name'}
                </h2>
                <p className="text-gray-600 dark:text-gray-400">{user.email}</p>
                {profile.position && profile.company && (
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {profile.position} at {profile.company}
                  </p>
                )}
              </div>
            </div>
            
            {!isEditing ? (
              <button
                onClick={() => setIsEditing(true)}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600/80 backdrop-blur-sm text-white rounded-lg hover:bg-blue-700/80 transition-all duration-300 hover:scale-105"
              >
                <span>✏️</span>
                <span>Edit</span>
              </button>
            ) : (
              <div className="flex space-x-2">
                <button
                  onClick={handleSave}
                  disabled={isSaving}
                  className="flex items-center space-x-2 px-4 py-2 bg-green-600/80 backdrop-blur-sm text-white rounded-lg hover:bg-green-700/80 transition-all duration-300 disabled:opacity-50"
                >
                  {isSaving ? '⏳' : '✅'}
                  <span>{isSaving ? 'Saving...' : 'Save'}</span>
                </button>
                <button
                  onClick={() => setIsEditing(false)}
                  className="flex items-center space-x-2 px-4 py-2 bg-gray-600/80 backdrop-blur-sm text-white rounded-lg hover:bg-gray-700/80 transition-all duration-300"
                >
                  <span>❌</span>
                  <span>Cancel</span>
                </button>
              </div>
            )}
          </div>

          {/* Profile Form */}
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Display Name
                </label>
                <input
                  type="text"
                  name="display_name"
                  value={profile.display_name}
                  onChange={handleChange}
                  disabled={!isEditing}
                  placeholder="Your full name"
                  className="w-full px-4 py-3 bg-white/5 dark:bg-gray-700/50 border border-white/10 dark:border-gray-600/50 rounded-lg focus:ring-2 focus:ring-blue-500/50 transition-all duration-300 outline-none disabled:opacity-50 text-gray-900 dark:text-white placeholder-gray-400"
                />
              </div>
            
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Position
                </label>
                <input
                  type="text"
                  name="position"
                  value={profile.position}
                  onChange={handleChange}
                  disabled={!isEditing}
                  placeholder="Your job title"
                  className="w-full px-4 py-3 bg-white/5 dark:bg-gray-700/50 border border-white/10 dark:border-gray-600/50 rounded-lg focus:ring-2 focus:ring-blue-500/50 transition-all duration-300 outline-none disabled:opacity-50 text-gray-900 dark:text-white placeholder-gray-400"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Company
                </label>
                <input
                  type="text"
                  name="company"
                  value={profile.company}
                  onChange={handleChange}
                  disabled={!isEditing}
                  placeholder="Your company"
                  className="w-full px-4 py-3 bg-white/5 dark:bg-gray-700/50 border border-white/10 dark:border-gray-600/50 rounded-lg focus:ring-2 focus:ring-blue-500/50 transition-all duration-300 outline-none disabled:opacity-50 text-gray-900 dark:text-white placeholder-gray-400"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Bio
                </label>
                <textarea
                  name="bio"
                  value={profile.bio}
                  onChange={handleChange}
                  disabled={!isEditing}
                  rows={3}
                  placeholder="Tell us about yourself..."
                  className="w-full px-4 py-3 bg-white/5 dark:bg-gray-700/50 border border-white/10 dark:border-gray-600/50 rounded-lg focus:ring-2 focus:ring-blue-500/50 transition-all duration-300 outline-none disabled:opacity-50 text-gray-900 dark:text-white placeholder-gray-400"
                />
              </div>
            </div>
          </div>
        </motion.div>

        {/* Quick Actions Sidebar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="space-y-4"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h3>
          
          {quickActions.map((action, index) => (
            <motion.div
              key={action.title}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              {action.href ? (
                <Link href={action.href}>
                  <div className={`p-4 rounded-lg ${action.color} bg-opacity-10 border border-current border-opacity-20 hover:bg-opacity-20 transition-all duration-300 cursor-pointer group relative`}>
                    {action.badge && (
                      <div className="absolute top-2 right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                        {action.badge}
                      </div>
                    )}
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{action.icon}</span>
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white group-hover:text-white transition-colors">
                          {action.title}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400 group-hover:text-gray-200 transition-colors">
                          {action.description}
                        </p>
                      </div>
                    </div>
                  </div>
                </Link>
              ) : action.action ? (
                <button
                  onClick={action.action}
                  className={`w-full p-4 rounded-lg ${action.color} bg-opacity-10 border border-current border-opacity-20 hover:bg-opacity-20 transition-all duration-300 group text-left relative`}
                >
                  {action.badge && (
                    <div className="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                      {action.badge}
                    </div>
                  )}
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{action.icon}</span>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white group-hover:text-white transition-colors">
                        {action.title}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 group-hover:text-gray-200 transition-colors">
                        {action.description}
                      </p>
                    </div>
                  </div>
                </button>
              ) : (
                <div className={`p-4 rounded-lg ${action.color} bg-opacity-10 border border-current border-opacity-20 ${action.comingSoon ? 'opacity-70' : 'opacity-50'} cursor-not-allowed relative`}>
                  {action.comingSoon && (
                    <div className="absolute top-2 right-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded-full">
                      Soon
                    </div>
                  )}
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{action.icon}</span>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {action.title}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {action.description}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </motion.div>
          ))}
        </motion.div>
      </div>

      {/* Profile Picture Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="bg-white/10 dark:bg-gray-800/30 backdrop-blur-lg rounded-xl shadow-xl border border-white/20 dark:border-gray-700/30 p-6"
      >
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Profile Picture
        </h2>
        <ProfilePictureManager onUpdate={handleProfileUpdate} />
      </motion.div>

      {/* Dynamic Content Sections */}
      {activeSection === 'chat' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="space-y-6"
        >
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Communication Hub</h3>
            <button
              onClick={() => setActiveSection('overview')}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              ✕ Close
            </button>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ApplicantChat />
            <SupportChat />
          </div>
        </motion.div>
      )}

      {activeSection === 'tools' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Task Management Tools</h3>
            <button
              onClick={() => setActiveSection('overview')}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              ✕ Close
            </button>
          </div>
          <TaskTools />
        </motion.div>
      )}

      {/* Account Management & Data */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="bg-white/10 dark:bg-gray-800/30 backdrop-blur-lg rounded-xl shadow-xl border border-white/20 dark:border-gray-700/30 p-6 space-y-6"
      >
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Account Management
        </h2>

        {/* Data Export Section */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <FaInfoCircle className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
            <div className="flex-1">
              <h3 className="text-lg font-medium text-blue-900 dark:text-blue-300 mb-2">
                Export Your Data
              </h3>
              <p className="text-blue-800 dark:text-blue-200 text-sm mb-4">
                Download a complete copy of your data including profile, HielLinks, chat history, and other content.
              </p>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={handleExportData}
                disabled={isExporting}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isExporting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span>Exporting...</span>
                  </>
                ) : (
                  <>
                    <FaDownload className="w-4 h-4" />
                    <span>Export My Data</span>
                  </>
                )}
              </motion.button>
            </div>
          </div>
        </div>

        {/* Privacy Links */}
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            Privacy & Data Information
          </h4>
          <div className="flex flex-wrap gap-4 text-sm">
            <Link 
              href="/privacy" 
              className="text-blue-600 dark:text-blue-400 hover:underline"
            >
              Privacy Policy
            </Link>
            <Link 
              href="/data-deletion" 
              className="text-blue-600 dark:text-blue-400 hover:underline"
            >
              Data Deletion Guide
            </Link>
            <Link 
              href="/terms" 
              className="text-blue-600 dark:text-blue-400 hover:underline"
            >
              Terms of Service
            </Link>
          </div>
        </div>

        {/* Account Actions */}
        <div className="flex flex-col sm:flex-row gap-4">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={logout}
            className="flex items-center justify-center space-x-2 px-4 py-2 bg-gray-600/80 backdrop-blur-sm hover:bg-gray-700/80 text-white rounded-lg transition-colors"
          >
            <span>🚪</span>
            <span>Sign Out</span>
          </motion.button>
        </div>

        {/* Account Deletion Section */}
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <FaExclamationTriangle className="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5" />
            <div className="flex-1">
              <h3 className="text-lg font-medium text-red-900 dark:text-red-300 mb-2">
                Danger Zone
              </h3>
              <p className="text-red-800 dark:text-red-200 text-sm mb-4">
                Once you delete your account, there is no going back. This action cannot be undone.
              </p>

              {!showDeleteConfirm ? (
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => setShowDeleteConfirm(true)}
                  className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                >
                  <FaTrash className="w-4 h-4" />
                  <span>Delete My Account</span>
                </motion.button>
              ) : (
                <div className="space-y-4">
                  {deleteStep === 'initial' && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      className="space-y-4"
                    >
                      <div className="bg-red-100 dark:bg-red-900/30 rounded-lg p-4">
                        <h4 className="font-medium text-red-900 dark:text-red-200 mb-2">
                          This will permanently delete:
                        </h4>
                        <ul className="text-sm text-red-800 dark:text-red-300 space-y-1">
                          <li>• Your profile and account information</li>
                          <li>• All HielLinks profiles and content</li>
                          <li>• Chat messages and conversations</li>
                          <li>• Team applications and submissions</li>
                          <li>• Social media drafts and scheduled posts</li>
                          <li>• Analytics data and insights</li>
                          <li>• Any uploaded files and media</li>
                        </ul>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-red-900 dark:text-red-200 mb-2">
                          Why are you deleting your account? (Optional)
                        </label>
                        <textarea
                          value={deleteReason}
                          onChange={(e) => setDeleteReason(e.target.value)}
                          placeholder="Help us improve by sharing your reason..."
                          className="w-full px-3 py-2 border border-red-300 dark:border-red-700 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                          rows={3}
                        />
                      </div>

                      <div className="flex space-x-3">
                        <button
                          onClick={() => setDeleteStep('confirm')}
                          className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                        >
                          Continue
                        </button>
                        <button
                          onClick={resetDeleteFlow}
                          className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                        >
                          Cancel
                        </button>
                      </div>
                    </motion.div>
                  )}

                  {deleteStep === 'confirm' && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      className="space-y-4"
                    >
                      <div className="bg-red-100 dark:bg-red-900/30 rounded-lg p-4">
                        <h4 className="font-medium text-red-900 dark:text-red-200 mb-2">
                          ⚠️ Final Warning
                        </h4>
                        <p className="text-sm text-red-800 dark:text-red-300">
                          This action will permanently delete your account and all associated data. 
                          This cannot be undone. Please make sure you have exported any data you wish to keep.
                        </p>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-red-900 dark:text-red-200 mb-2">
                          Type &quot;DELETE MY ACCOUNT&quot; to confirm:
                        </label>
                        <input
                          type="text"
                          value={confirmText}
                          onChange={(e) => setConfirmText(e.target.value)}
                          placeholder="DELETE MY ACCOUNT"
                          className="w-full px-3 py-2 border border-red-300 dark:border-red-700 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                        />
                      </div>

                      <div className="flex space-x-3">
                        <button
                          onClick={() => setDeleteStep('final')}
                          disabled={!canProceedToFinal}
                          className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white rounded-lg transition-colors disabled:cursor-not-allowed"
                        >
                          I Understand, Delete My Account
                        </button>
                        <button
                          onClick={() => setDeleteStep('initial')}
                          className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                        >
                          Back
                        </button>
                      </div>
                    </motion.div>
                  )}

                  {deleteStep === 'final' && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      className="space-y-4"
                    >
                      <div className="bg-red-100 dark:bg-red-900/30 rounded-lg p-4 text-center">
                        <h4 className="font-medium text-red-900 dark:text-red-200 mb-2">
                          🗑️ Account Deletion
                        </h4>
                        <p className="text-sm text-red-800 dark:text-red-300 mb-4">
                          You are about to permanently delete your account. This action cannot be undone.
                        </p>
                        
                        <div className="flex justify-center space-x-3">
                          <button
                            onClick={handleDeleteAccount}
                            disabled={isDeleting}
                            className="flex items-center space-x-2 px-6 py-2 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white rounded-lg transition-colors disabled:cursor-not-allowed"
                          >
                            {isDeleting ? (
                              <>
                                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                <span>Deleting...</span>
                              </>
                            ) : (
                              <>
                                <FaTrash className="w-4 h-4" />
                                <span>Delete Forever</span>
                              </>
                            )}
                          </button>
                          <button
                            onClick={resetDeleteFlow}
                            disabled={isDeleting}
                            className="px-6 py-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
}