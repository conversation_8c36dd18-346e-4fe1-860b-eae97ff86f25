'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { supabase } from '@/lib/supabase';
import styles from '@/components/animations/auth.module.css';

interface EmailConfirmationProps {
  email: string;
  onBack: () => void;
  source?: 'signup' | 'login'; // Add source to distinguish between signup and unconfirmed login
}

export default function EmailConfirmation({ email, onBack, source = 'signup' }: EmailConfirmationProps) {
  const [isResending, setIsResending] = useState(false);
  const [resendMessage, setResendMessage] = useState('');
  const [resendCount, setResendCount] = useState(0);
  const [countdown, setCountdown] = useState(60);
  const [canResend, setCanResend] = useState(false);

  useEffect(() => {
    // Start countdown timer
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          setCanResend(true);
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [resendCount]); // Restart countdown when resend count changes

  const handleResendEmail = async () => {
    if (resendCount >= 3) {
      setResendMessage('Maximum resend attempts reached. Please try signing up again or contact support.');
      return;
    }

    setIsResending(true);
    setResendMessage('');

    try {
      // Use the appropriate resend type based on source
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email,
        options: {
          emailRedirectTo: `${typeof window !== 'undefined' ? window.location.origin : 'https://hieltech-v-next.vercel.app'}/confirm-email`
        }
      });

      if (error) {
        throw error;
      }

      setResendCount(prev => prev + 1);
      setResendMessage('✅ Confirmation email sent successfully! Please check your inbox and spam folder.');
      setCanResend(false);
      setCountdown(60 + (resendCount * 30)); // Increase countdown time with each resend
      
    } catch (error: unknown) {
      console.error('Error resending email:', error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      if (errorMessage.includes('Email rate limit exceeded') || errorMessage.includes('rate limit')) {
        setResendMessage('⏱️ Please wait a moment before requesting another email. Check your spam folder.');
      } else if (errorMessage.includes('User not found') || errorMessage.includes('not found')) {
        setResendMessage('❌ Account not found. Please sign up again.');
      } else if (errorMessage.includes('already confirmed') || errorMessage.includes('already verified')) {
        setResendMessage('✅ Your email has already been confirmed! You can now sign in.');
      } else if (errorMessage.includes('signup disabled')) {
        setResendMessage('⚠️ New registrations are temporarily disabled. Please try again later.');
      } else {
        setResendMessage('❌ Unable to send email. Please try again or contact support.');
      }
    } finally {
      setIsResending(false);
    }
  };

  const handleCheckEmail = () => {
    // Open default email client
    try {
      window.open('mailto:', '_blank');
    } catch (error) {
      console.error('Could not open email client:', error);
      // Fallback: provide instructions
      setResendMessage('💡 Please open your email app manually to check for the confirmation email.');
    }
  };

  const handleTryLoginAgain = () => {
    // Clear any messages and go back to login form
    setResendMessage('');
    onBack();
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={`max-w-md mx-auto p-8 rounded-xl shadow-2xl border border-white/20 dark:border-gray-700/30 transition-all duration-300 hover:shadow-indigo-500/10 ${styles['glass-morphism']} bg-white/10 dark:bg-gray-800/30`}
    >
      {/* Email Icon */}
      <div className="text-center mb-6">
        <div className={`mx-auto w-16 h-16 rounded-full flex items-center justify-center mb-4 ${
          source === 'login' 
            ? 'bg-gradient-to-r from-yellow-500 to-orange-600' 
            : 'bg-gradient-to-r from-blue-500 to-purple-600'
        }`}>
          <svg
            className="w-8 h-8 text-white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            {source === 'login' ? (
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 14.5c-.77.833.192 2.5 1.732 2.5z"
              />
            ) : (
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
              />
            )}
          </svg>
        </div>
        <h2 className={`text-3xl font-bold mb-4 ${styles['animate-fade-in']} ${
          source === 'login'
            ? 'bg-gradient-to-r from-yellow-500 to-orange-500 bg-clip-text text-transparent'
            : 'bg-gradient-to-r from-indigo-500 to-purple-500 bg-clip-text text-transparent'
        }`}>
          {source === 'login' ? 'Email Not Confirmed' : 'Check Your Email'}
        </h2>
      </div>

      {/* Instructions */}
      <div className="text-center mb-6">
        {source === 'login' ? (
          <>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Your account exists but your email address is not confirmed yet.
            </p>
            <div className="p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800 mb-4">
              <p className="text-lg font-semibold text-orange-700 dark:text-orange-400 break-all">
                {email}
              </p>
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Please check your email and click the confirmation link, or request a new confirmation email below.
            </p>
          </>
        ) : (
          <>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              We&apos;ve sent a confirmation link to:
            </p>
            <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 mb-4">
              <p className="text-lg font-semibold text-blue-700 dark:text-blue-400 break-all">
                {email}
              </p>
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Click the link in the email to confirm your account and complete the registration process.
            </p>
          </>
        )}
      </div>

      {/* Action Buttons */}
      <div className="space-y-4">
        <button
          onClick={handleCheckEmail}
          className={`w-full py-3 px-4 text-white rounded-lg font-medium transform hover:scale-[1.02] transition-all duration-200 ${
            source === 'login'
              ? 'bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700'
              : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700'
          }`}
        >
          📧 Open Email App
        </button>

        {/* Resend Email */}
        <div className="text-center">
          {canResend && resendCount < 3 ? (
            <button
              onClick={handleResendEmail}
              disabled={isResending}
              className={`text-center transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed font-medium ${
                source === 'login'
                  ? 'text-orange-600 dark:text-orange-400 hover:text-orange-700 dark:hover:text-orange-300'
                  : 'text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300'
              }`}
            >
              {isResending ? '⏳ Sending...' : `📤 Resend confirmation email ${resendCount > 0 ? `(${resendCount}/3)` : ''}`}
            </button>
          ) : (
            <div className="text-center">
              {resendCount >= 3 ? (
                <p className="text-sm text-red-600 dark:text-red-400 font-medium">
                  Maximum resend attempts reached
                </p>
              ) : (
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  📅 Resend email in {countdown} seconds
                </p>
              )}
            </div>
          )}
        </div>

        {/* Resend Message */}
        {resendMessage && (
          <div className={`text-center text-sm p-3 rounded-lg border ${
            resendMessage.includes('❌') || resendMessage.includes('Maximum')
              ? 'text-red-600 bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800' 
              : resendMessage.includes('⏱️') || resendMessage.includes('⚠️')
              ? 'text-yellow-600 bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'
              : 'text-green-600 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
          }`}>
            {resendMessage}
          </div>
        )}

        {/* Additional action for login scenario */}
        {source === 'login' && (
          <button
            onClick={handleTryLoginAgain}
            className="w-full py-2 px-4 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
          >
            🔄 Try Logging In Again
          </button>
        )}

        {/* Back Button */}
        <button
          onClick={onBack}
          className="w-full py-2 px-4 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200 font-medium"
        >
          ← Back to {source === 'login' ? 'Sign In' : 'Sign Up'}
        </button>
      </div>

      {/* Help Text */}
      <div className={`mt-6 p-4 rounded-lg border ${
        source === 'login'
          ? 'bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800'
          : 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800'
      }`}>
        <h4 className={`text-sm font-medium mb-2 ${
          source === 'login'
            ? 'text-orange-800 dark:text-orange-200'
            : 'text-yellow-800 dark:text-yellow-200'
        }`}>
          {source === 'login' ? '🔐 Need to confirm your email?' : '📬 Didn&apos;t receive the email?'}
        </h4>
        <ul className={`text-xs space-y-1 ${
          source === 'login'
            ? 'text-orange-700 dark:text-orange-300'
            : 'text-yellow-700 dark:text-yellow-300'
        }`}>
          <li>• Check your spam/junk folder thoroughly</li>
          <li>• Make sure the email address is spelled correctly</li>
          <li>• Wait 2-3 minutes for the email to arrive</li>
          <li>• Use the resend button above if needed (max 3 times)</li>
          {source === 'login' && (
            <li>• Once confirmed, you can log in normally</li>
          )}
          <li>• Contact support if you continue having issues</li>
        </ul>
        
        {/* Email provider quick links */}
        <div className="mt-3 pt-3 border-t border-current/20">
          <p className="text-xs font-medium mb-2">Quick email access:</p>
          <div className="flex gap-2 text-xs">
            <a 
              href="https://gmail.com" 
              target="_blank" 
              rel="noopener noreferrer"
              className="hover:underline"
            >
              Gmail
            </a>
            <span>•</span>
            <a 
              href="https://outlook.com" 
              target="_blank" 
              rel="noopener noreferrer"
              className="hover:underline"
            >
              Outlook
            </a>
            <span>•</span>
            <a 
              href="https://mail.yahoo.com" 
              target="_blank" 
              rel="noopener noreferrer"
              className="hover:underline"
            >
              Yahoo
            </a>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
