import React from 'react';
import styles from '@/components/animations/auth.module.css';

interface AuthErrorDisplayProps {
  error: string;
  onRetry?: () => void;
  onSwitchMode?: () => void;
  onRequestConfirmation?: () => void;
  showConfirmationOption?: boolean;
}

export default function AuthErrorDisplay({
  error,
  onRetry,
  onSwitchMode,
  onRequestConfirmation,
  showConfirmationOption = false
}: AuthErrorDisplayProps) {
  const getErrorConfig = () => {
    if (error.includes('Email not confirmed') || error.includes('confirm your email')) {
      return {
        icon: (
          <svg className="w-5 h-5 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        ),
        bgColor: 'bg-yellow-500/10',
        borderColor: 'border-yellow-500/20',
        textColor: 'text-yellow-600 dark:text-yellow-400',
        title: 'Email Confirmation Required'
      };
    } else if (error.includes('Invalid') || error.includes('credentials') || error.includes('password')) {
      return {
        icon: (
          <svg className="w-5 h-5 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        ),
        bgColor: 'bg-red-500/10',
        borderColor: 'border-red-500/20',
        textColor: 'text-red-500',
        title: 'Authentication Error'
      };
    } else if (error.includes('already registered') || error.includes('already exists')) {
      return {
        icon: (
          <svg className="w-5 h-5 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
          </svg>
        ),
        bgColor: 'bg-blue-500/10',
        borderColor: 'border-blue-500/20',
        textColor: 'text-blue-600 dark:text-blue-400',
        title: 'Account Exists'
      };
    } else {
      return {
        icon: (
          <svg className="w-5 h-5 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        ),
        bgColor: 'bg-red-500/10',
        borderColor: 'border-red-500/20',
        textColor: 'text-red-500',
        title: 'Error'
      };
    }
  };

  const config = getErrorConfig();

  const getActionButtons = () => {
    if (error.includes('Email not confirmed') || showConfirmationOption) {
      return (
        <div className="mt-3 space-y-2">
          {onRequestConfirmation && (
            <button
              onClick={onRequestConfirmation}
              className="w-full py-2 px-3 bg-yellow-600 hover:bg-yellow-700 text-white text-sm rounded-md font-medium transition-colors duration-200"
            >
              📧 Resend Confirmation Email
            </button>
          )}
          {onRetry && (
            <button
              onClick={onRetry}
              className="w-full py-1 px-3 text-xs text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-200 transition-colors duration-200"
            >
              Try Again
            </button>
          )}
        </div>
      );
    }

    if (error.includes('already registered') || error.includes('already exists')) {
      return (
        <div className="mt-3 space-y-2">
          {onSwitchMode && (
            <button
              onClick={onSwitchMode}
              className="w-full py-2 px-3 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md font-medium transition-colors duration-200"
            >
              🔑 Switch to Sign In
            </button>
          )}
        </div>
      );
    }

    if (onRetry) {
      return (
        <div className="mt-3">
          <button
            onClick={onRetry}
            className="w-full py-2 px-3 bg-red-600 hover:bg-red-700 text-white text-sm rounded-md font-medium transition-colors duration-200"
          >
            🔄 Try Again
          </button>
        </div>
      );
    }

    return null;
  };

  return (
    <div className={`mb-6 p-4 ${config.bgColor} border ${config.borderColor} ${config.textColor} rounded-lg ${styles['animate-shake']}`}>
      <div className="flex items-start space-x-3">
        {config.icon}
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-semibold mb-1">{config.title}</h4>
          <p className="text-sm leading-relaxed">{error}</p>
          {getActionButtons()}
        </div>
      </div>
    </div>
  );
} 