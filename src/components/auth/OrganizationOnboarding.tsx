'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '@/lib/auth/AuthContext';
import { useRouter } from 'next/navigation';
import { Organization, OrganizationMember } from '@/types/organization';

type OnboardingStep = 'account_type' | 'org_details' | 'team_setup' | 'preferences' | 'complete';

interface OrganizationOnboardingProps {
  onComplete?: (organization: Organization) => void;
}

interface OnboardingData {
  // Organization Details
  orgName: string;
  orgSlug: string;
  orgDescription: string;
  orgIndustry: string;
  orgSize: Organization['size'];
  orgCountry: string;
  orgTimezone: string;
  orgWebsite: string;
  
  // Team Members
  teamMembers: {
    email: string;
    role: OrganizationMember['role'];
    department: string;
  }[];
  
  // Preferences
  projectVisibility: 'private' | 'organization' | 'public';
  enableTimeTracking: boolean;
  enablePublicChat: boolean;
  subscriptionTier: 'free' | 'pro' | 'enterprise';
}

const initialData: OnboardingData = {
  orgName: '',
  orgSlug: '',
  orgDescription: '',
  orgIndustry: '',
  orgSize: 'startup',
  orgCountry: '',
  orgTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  orgWebsite: '',
  teamMembers: [],
  projectVisibility: 'organization',
  enableTimeTracking: true,
  enablePublicChat: true,
  subscriptionTier: 'free'
};

const industries = [
  'Technology', 'Healthcare', 'Finance', 'Education', 'Retail',
  'Manufacturing', 'Construction', 'Real Estate', 'Marketing',
  'Consulting', 'Non-profit', 'Government', 'Other'
];

const countries = [
  'United States', 'Canada', 'United Kingdom', 'Germany', 'France',
  'Australia', 'Japan', 'India', 'Brazil', 'Mexico', 'Other'
];

export default function OrganizationOnboarding({ onComplete }: OrganizationOnboardingProps) {
  const { signUp, signInWithGoogle } = useAuth();
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState<OnboardingStep>('account_type');
  const [data, setData] = useState<OnboardingData>(initialData);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  
  // Account creation for org onboarding
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [accountType, setAccountType] = useState<'individual' | 'organization'>('individual');

  const updateData = (updates: Partial<OnboardingData>) => {
    setData(prev => ({ ...prev, ...updates }));
  };

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const handleOrgNameChange = (name: string) => {
    updateData({ 
      orgName: name, 
      orgSlug: generateSlug(name) 
    });
  };

  const addTeamMember = () => {
    updateData({
      teamMembers: [...data.teamMembers, { email: '', role: 'member', department: '' }]
    });
  };

  const updateTeamMember = (index: number, updates: Partial<typeof data.teamMembers[0]>) => {
    const newTeamMembers = [...data.teamMembers];
    newTeamMembers[index] = { ...newTeamMembers[index], ...updates };
    updateData({ teamMembers: newTeamMembers });
  };

  const removeTeamMember = (index: number) => {
    updateData({
      teamMembers: data.teamMembers.filter((_, i) => i !== index)
    });
  };

  const handleAccountCreation = async (type: 'email' | 'google') => {
    setLoading(true);
    setError('');
    
    try {
      if (type === 'email') {
        if (!email || !password || !name) {
          throw new Error('Please fill in all required fields');
        }
        await signUp(email, password);
      } else {
        await signInWithGoogle();
      }
      
      // After successful account creation, move to next step
      if (accountType === 'organization') {
        setCurrentStep('org_details');
      } else {
        // For individual accounts, complete onboarding
        router.push('/profile');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create account');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateOrganization = async () => {
    setLoading(true);
    setError('');
    
    try {
      // Here you would call your organization creation API
      // const organization = await createOrganization(data);
      // const invitations = await inviteTeamMembers(data.teamMembers);
      
      // Mock success for now
      setTimeout(() => {
        setCurrentStep('complete');
        setLoading(false);
        if (onComplete) {
          // Mock organization object
          const mockOrg: Organization = {
            id: 'mock-id',
            name: data.orgName,
            slug: data.orgSlug,
            size: data.orgSize,
            subscription_tier: data.subscriptionTier,
            settings: {
              default_project_visibility: data.projectVisibility,
              enable_time_tracking: data.enableTimeTracking,
              enable_budget_tracking: false,
              default_task_priority: 'medium',
              enable_public_chat: data.enablePublicChat,
              enable_guest_access: false,
              chat_retention_days: 365,
              require_2fa: false,
              allowed_domains: [],
              session_timeout_minutes: 480,
              features: {
                advanced_analytics: false,
                custom_branding: false,
                api_access: false,
                integrations: false,
                custom_fields: false,
                advanced_permissions: false
              },
              brand_colors: {
                primary: '#3B82F6',
                secondary: '#8B5CF6',
                accent: '#10B981'
              }
            },
            stats: {
              total_members: 1,
              active_projects: 0,
              completed_projects: 0,
              total_tasks: 0,
              completed_tasks: 0,
              storage_used_mb: 0,
              storage_limit_mb: 1024
            },
            is_active: true,
            created_by: 'mock-user-id',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };
          onComplete(mockOrg);
        }
      }, 2000);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create organization');
      setLoading(false);
    }
  };

  const nextStep = () => {
    const steps: OnboardingStep[] = ['account_type', 'org_details', 'team_setup', 'preferences', 'complete'];
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1]);
    }
  };

  const prevStep = () => {
    const steps: OnboardingStep[] = ['account_type', 'org_details', 'team_setup', 'preferences', 'complete'];
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1]);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 'account_type':
        return (
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            className="space-y-6"
          >
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Choose Your Account Type
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Select how you&apos;d like to use our platform
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Individual Account */}
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => setAccountType('individual')}
                className={`p-6 rounded-xl border-2 cursor-pointer transition-all ${
                  accountType === 'individual'
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-4 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                    <span className="text-2xl">👤</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    Individual
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    Perfect for freelancers, personal projects, and individual use
                  </p>
                  <ul className="mt-4 text-left text-sm text-gray-500 dark:text-gray-400 space-y-1">
                    <li>• Personal task management</li>
                    <li>• Basic project tools</li>
                    <li>• HielLinks profile</li>
                    <li>• Individual chat support</li>
                  </ul>
                </div>
              </motion.div>

              {/* Organization Account */}
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => setAccountType('organization')}
                className={`p-6 rounded-xl border-2 cursor-pointer transition-all ${
                  accountType === 'organization'
                    ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
              >
                <div className="text-center">
                  <div className="w-16 h-16 mx-auto mb-4 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                    <span className="text-2xl">🏢</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    Organization
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    For teams, companies, and collaborative workspaces
                  </p>
                  <ul className="mt-4 text-left text-sm text-gray-500 dark:text-gray-400 space-y-1">
                    <li>• Advanced project management</li>
                    <li>• Team collaboration tools</li>
                    <li>• Multi-user chat channels</li>
                    <li>• Analytics and reporting</li>
                    <li>• Custom branding options</li>
                  </ul>
                </div>
              </motion.div>
            </div>

            {/* Account Creation Form */}
            <div className="mt-8 p-6 border rounded-xl bg-gray-50 dark:bg-gray-800/50">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Create Your Account
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Full Name
                  </label>
                  <input
                    type="text"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Enter your full name"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Enter your email"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Password
                  </label>
                  <input
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Create a secure password"
                    minLength={6}
                  />
                </div>

                <div className="flex flex-col sm:flex-row gap-3">
                  <button
                    onClick={() => handleAccountCreation('email')}
                    disabled={loading}
                    className="flex-1 py-3 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors font-medium"
                  >
                    {loading ? 'Creating Account...' : `Create ${accountType === 'organization' ? 'Organization' : 'Individual'} Account`}
                  </button>
                  
                  <button
                    onClick={() => handleAccountCreation('google')}
                    disabled={loading}
                    className="flex-1 py-3 px-4 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 transition-colors font-medium flex items-center justify-center space-x-2"
                  >
                    <svg className="w-5 h-5" viewBox="0 0 24 24">
                      <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                      <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                      <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                      <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    <span>Continue with Google</span>
                  </button>
                </div>
              </div>
            </div>

            {error && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <p className="text-red-700 dark:text-red-300 text-sm">{error}</p>
              </div>
            )}
          </motion.div>
        );

      case 'org_details':
        return (
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            className="space-y-6"
          >
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Organization Details
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Tell us about your organization
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Organization Name *
                </label>
                <input
                  type="text"
                  value={data.orgName}
                  onChange={(e) => handleOrgNameChange(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Enter your organization name"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Organization URL
                </label>
                <div className="flex items-center">
                  <span className="text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-3 py-3 rounded-l-lg border border-r-0 border-gray-300 dark:border-gray-600">
                    hieltech.com/org/
                  </span>
                  <input
                    type="text"
                    value={data.orgSlug}
                    onChange={(e) => updateData({ orgSlug: generateSlug(e.target.value) })}
                    className="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-r-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="organization-name"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Industry
                </label>
                <select
                  value={data.orgIndustry}
                  onChange={(e) => updateData({ orgIndustry: e.target.value })}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="">Select industry</option>
                  {industries.map(industry => (
                    <option key={industry} value={industry}>{industry}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Organization Size
                </label>
                <select
                  value={data.orgSize}
                  onChange={(e) => updateData({ orgSize: e.target.value as Organization['size'] })}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="startup">Startup (1-10 employees)</option>
                  <option value="small">Small (11-50 employees)</option>
                  <option value="medium">Medium (51-200 employees)</option>
                  <option value="large">Large (201-1000 employees)</option>
                  <option value="enterprise">Enterprise (1000+ employees)</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Country
                </label>
                <select
                  value={data.orgCountry}
                  onChange={(e) => updateData({ orgCountry: e.target.value })}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="">Select country</option>
                  {countries.map(country => (
                    <option key={country} value={country}>{country}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Website (Optional)
                </label>
                <input
                  type="url"
                  value={data.orgWebsite}
                  onChange={(e) => updateData({ orgWebsite: e.target.value })}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="https://yourwebsite.com"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Description (Optional)
                </label>
                <textarea
                  value={data.orgDescription}
                  onChange={(e) => updateData({ orgDescription: e.target.value })}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Tell us about your organization..."
                />
              </div>
            </div>

            <div className="flex justify-between">
              <button
                onClick={prevStep}
                className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Back
              </button>
              <button
                onClick={nextStep}
                disabled={!data.orgName || !data.orgSlug}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Continue
              </button>
            </div>
          </motion.div>
        );

      case 'team_setup':
        return (
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            className="space-y-6"
          >
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Invite Your Team
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Add team members to get started with collaboration
              </p>
            </div>

            <div className="space-y-4">
              {data.teamMembers.map((member, index) => (
                <div key={index} className="flex gap-4 items-start p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                  <div className="flex-1 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <input
                        type="email"
                        value={member.email}
                        onChange={(e) => updateTeamMember(index, { email: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                        placeholder="Email address"
                      />
                    </div>
                    <div>
                      <select
                        value={member.role}
                        onChange={(e) => updateTeamMember(index, { role: e.target.value as OrganizationMember['role'] })}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                      >
                        <option value="member">Member</option>
                        <option value="project_manager">Project Manager</option>
                        <option value="admin">Admin</option>
                      </select>
                    </div>
                    <div>
                      <input
                        type="text"
                        value={member.department}
                        onChange={(e) => updateTeamMember(index, { department: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                        placeholder="Department (optional)"
                      />
                    </div>
                  </div>
                  <button
                    onClick={() => removeTeamMember(index)}
                    className="p-2 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                  >
                    ✕
                  </button>
                </div>
              ))}

              <button
                onClick={addTeamMember}
                className="w-full py-3 border-2 border-dashed border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 rounded-lg hover:border-blue-500 hover:text-blue-500 transition-colors"
              >
                + Add Team Member
              </button>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <p className="text-blue-800 dark:text-blue-200 text-sm">
                💡 <strong>Tip:</strong> You can always invite more team members later from your organization dashboard.
              </p>
            </div>

            <div className="flex justify-between">
              <button
                onClick={prevStep}
                className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Back
              </button>
              <button
                onClick={nextStep}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Continue
              </button>
            </div>
          </motion.div>
        );

      case 'preferences':
        return (
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            className="space-y-6"
          >
            <div className="text-center">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Organization Preferences
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Configure your organization&apos;s default settings
              </p>
            </div>

            <div className="space-y-6">
              {/* Project Settings */}
              <div className="p-6 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Project Management
                </h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Default Project Visibility
                    </label>
                    <select
                      value={data.projectVisibility}
                      onChange={(e) => updateData({ projectVisibility: e.target.value as 'private' | 'organization' | 'public' })}
                      className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option value="private">Private (Project members only)</option>
                      <option value="organization">Organization (All team members)</option>
                      <option value="public">Public (Anyone with link)</option>
                    </select>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Time Tracking</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Enable time tracking for tasks and projects</p>
                    </div>
                    <button
                      onClick={() => updateData({ enableTimeTracking: !data.enableTimeTracking })}
                      className={`w-12 h-6 rounded-full transition-colors ${
                        data.enableTimeTracking ? 'bg-blue-600' : 'bg-gray-300 dark:bg-gray-600'
                      }`}
                    >
                      <div className={`w-5 h-5 bg-white rounded-full transition-transform ${
                        data.enableTimeTracking ? 'translate-x-6' : 'translate-x-1'
                      }`} />
                    </button>
                  </div>
                </div>
              </div>

              {/* Communication Settings */}
              <div className="p-6 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Communication
                </h3>
                <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white">Public Chat Channels</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Allow creation of public channels visible to all members</p>
                  </div>
                  <button
                    onClick={() => updateData({ enablePublicChat: !data.enablePublicChat })}
                    className={`w-12 h-6 rounded-full transition-colors ${
                      data.enablePublicChat ? 'bg-blue-600' : 'bg-gray-300 dark:bg-gray-600'
                    }`}
                  >
                    <div className={`w-5 h-5 bg-white rounded-full transition-transform ${
                      data.enablePublicChat ? 'translate-x-6' : 'translate-x-1'
                    }`} />
                  </button>
                </div>
              </div>

              {/* Subscription Tier */}
              <div className="p-6 border border-gray-200 dark:border-gray-700 rounded-lg">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Subscription Plan
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {[
                    { 
                      id: 'free', 
                      name: 'Free', 
                      price: '$0', 
                      features: ['5 team members', 'Basic project management', 'Community support'] 
                    },
                    { 
                      id: 'pro', 
                      name: 'Pro', 
                      price: '$15/month', 
                      features: ['Unlimited members', 'Advanced analytics', 'Priority support', 'Custom branding'] 
                    },
                    { 
                      id: 'enterprise', 
                      name: 'Enterprise', 
                      price: 'Custom', 
                      features: ['Everything in Pro', 'SSO integration', 'Advanced security', 'Dedicated support'] 
                    }
                  ].map((plan) => (
                    <div
                      key={plan.id}
                      onClick={() => updateData({ subscriptionTier: plan.id as 'free' | 'pro' | 'enterprise' })}
                      className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                        data.subscriptionTier === plan.id
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                          : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                      }`}
                    >
                      <h4 className="font-semibold text-gray-900 dark:text-white">{plan.name}</h4>
                      <p className="text-2xl font-bold text-blue-600 dark:text-blue-400 my-2">{plan.price}</p>
                      <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                        {plan.features.map((feature, index) => (
                          <li key={index}>• {feature}</li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="flex justify-between">
              <button
                onClick={prevStep}
                className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Back
              </button>
              <button
                onClick={handleCreateOrganization}
                disabled={loading}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center space-x-2"
              >
                {loading && <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />}
                <span>{loading ? 'Creating Organization...' : 'Create Organization'}</span>
              </button>
            </div>
          </motion.div>
        );

      case 'complete':
        return (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center space-y-6"
          >
            <div className="w-24 h-24 mx-auto bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
              <span className="text-4xl">🎉</span>
            </div>
            
            <div>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Welcome to Your Organization!
              </h2>
              <p className="text-gray-600 dark:text-gray-400 text-lg">
                <strong>{data.orgName}</strong> has been successfully created.
              </p>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
              <h3 className="font-semibold text-blue-900 dark:text-blue-300 mb-2">
                What&apos;s Next?
              </h3>
              <ul className="text-left text-blue-800 dark:text-blue-200 space-y-2">
                <li>• Team invitations have been sent to {data.teamMembers.length} members</li>
                <li>• Set up your first project and start collaborating</li>
                <li>• Explore advanced features in your organization dashboard</li>
                <li>• Customize your organization settings and branding</li>
              </ul>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => router.push('/organization/dashboard')}
                className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                Go to Dashboard
              </button>
              <button
                onClick={() => router.push('/organization/projects')}
                className="px-8 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors font-medium"
              >
                Create First Project
              </button>
            </div>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 py-12">
      <div className="max-w-4xl mx-auto px-4">
        {/* Progress Bar */}
        {currentStep !== 'complete' && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                Setup Progress
              </span>
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {currentStep === 'account_type' ? '1' : 
                 currentStep === 'org_details' ? '2' :
                 currentStep === 'team_setup' ? '3' : '4'} of 4
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                style={{ 
                  width: `${
                    currentStep === 'account_type' ? 25 : 
                    currentStep === 'org_details' ? 50 :
                    currentStep === 'team_setup' ? 75 : 100
                  }%` 
                }}
              />
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg rounded-2xl shadow-xl border border-white/20 dark:border-gray-700/30 p-8">
          <AnimatePresence mode="wait">
            {renderStep()}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
} 