'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FaInstagram, 
  FaFacebook, 
  FaCalendarAlt, 
  FaChartLine, 
  FaPlus,
  FaCog,
  FaConnectdevelop
} from 'react-icons/fa';
import { useAuth } from '@/lib/auth/AuthContext';
import { SocialMediaAccount, SocialMediaPost } from '@/lib/types/social-media';

interface SocialMediaPlannerProps {
  className?: string;
}

type ActiveTab = 'dashboard' | 'calendar' | 'analytics' | 'accounts' | 'settings';

export default function SocialMediaPlanner({ className = '' }: SocialMediaPlannerProps) {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<ActiveTab>('dashboard');
  const [connectedAccounts, setConnectedAccounts] = useState<SocialMediaAccount[]>([]);
  const [recentPosts, setRecentPosts] = useState<SocialMediaPost[]>([]);

  useEffect(() => {
    if (user) {
      loadConnectedAccounts();
      loadRecentPosts();
    }
  }, [user]);

  const loadConnectedAccounts = async () => {
    try {
      // TODO: Implement API call to load connected accounts
      // For now, using mock data
      setConnectedAccounts([]);
    } catch (error) {
      console.error('Error loading connected accounts:', error);
    }
  };

  const loadRecentPosts = async () => {
    try {
      // TODO: Implement API call to load recent posts
      // For now, using mock data
      setRecentPosts([]);
    } catch (error) {
      console.error('Error loading recent posts:', error);
    }
  };

  const handleConnectAccount = async (platform: 'instagram' | 'facebook') => {
    // TODO: Implement OAuth flow for connecting social media accounts
    console.log(`Connecting ${platform} account...`);
    
    if (platform === 'facebook') {
      // This would redirect to Facebook OAuth with appropriate scopes
      const scopes = 'email,public_profile,pages_show_list,pages_read_engagement,instagram_basic,business_management';
      const redirectUri = `${window.location.origin}/auth/callback`;
      const clientId = process.env.NEXT_PUBLIC_FACEBOOK_APP_ID;
      
      if (clientId) {
        const authUrl = `https://www.facebook.com/v18.0/dialog/oauth?client_id=${clientId}&redirect_uri=${redirectUri}&scope=${scopes}&response_type=code&state=facebook_media_planner`;
        window.location.href = authUrl;
      }
    }
  };

  const tabs = [
    {
      id: 'dashboard' as ActiveTab,
      label: 'Dashboard',
      icon: FaChartLine,
      description: 'Overview of your social media performance'
    },
    {
      id: 'calendar' as ActiveTab,
      label: 'Content Calendar',
      icon: FaCalendarAlt,
      description: 'Schedule and manage your posts'
    },
    {
      id: 'analytics' as ActiveTab,
      label: 'Analytics',
      icon: FaChartLine,
      description: 'Track performance and insights'
    },
    {
      id: 'accounts' as ActiveTab,
      label: 'Connected Accounts',
      icon: FaConnectdevelop,
      description: 'Manage your social media accounts'
    },
    {
      id: 'settings' as ActiveTab,
      label: 'Settings',
      icon: FaCog,
      description: 'Configure your preferences'
    }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <DashboardTab connectedAccounts={connectedAccounts} recentPosts={recentPosts} />;
      case 'calendar':
        return <CalendarTab />;
      case 'analytics':
        return <AnalyticsTab />;
      case 'accounts':
        return <AccountsTab connectedAccounts={connectedAccounts} onConnectAccount={handleConnectAccount} />;
      case 'settings':
        return <SettingsTab />;
      default:
        return <DashboardTab connectedAccounts={connectedAccounts} recentPosts={recentPosts} />;
    }
  };

  return (
    <div className={`bg-white dark:bg-gray-900 rounded-xl shadow-lg ${className}`}>
      {/* Header */}
      <div className="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Social Media Planner
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage your Instagram and Facebook content
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <span className="px-3 py-1 bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 rounded-full text-sm font-medium">
              Beta
            </span>
            <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center space-x-2">
              <FaPlus className="w-4 h-4" />
              <span>Create Post</span>
            </button>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-8 px-6" aria-label="Tabs">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <Icon className="w-4 h-4" />
                  <span>{tab.label}</span>
                </div>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.2 }}
          >
            {renderTabContent()}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
}

// Dashboard Tab Component
function DashboardTab({ 
  connectedAccounts, 
  recentPosts 
}: { 
  connectedAccounts: SocialMediaAccount[]; 
  recentPosts: SocialMediaPost[]; 
}) {
  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-100">Connected Accounts</p>
              <p className="text-3xl font-bold">{connectedAccounts.length}</p>
            </div>
            <FaConnectdevelop className="w-12 h-12 text-purple-200" />
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100">Scheduled Posts</p>
              <p className="text-3xl font-bold">0</p>
            </div>
            <FaCalendarAlt className="w-12 h-12 text-blue-200" />
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-100">This Month</p>
              <p className="text-3xl font-bold">{recentPosts.length}</p>
            </div>
            <FaChartLine className="w-12 h-12 text-green-200" />
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Quick Start
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <QuickActionCard
            icon={FaInstagram}
            title="Connect Instagram"
            description="Link your Instagram account to start planning"
            action="Connect Account"
            color="bg-pink-500"
          />
          <QuickActionCard
            icon={FaFacebook}
            title="Connect Facebook"
            description="Link your Facebook page for posting"
            action="Connect Page"
            color="bg-blue-500"
          />
          <QuickActionCard
            icon={FaPlus}
            title="Create First Post"
            description="Start planning your content"
            action="Create Post"
            color="bg-purple-500"
          />
        </div>
      </div>
    </div>
  );
}

// Quick Action Card Component
function QuickActionCard({ 
  icon: Icon, 
  title, 
  description, 
  action, 
  color 
}: {
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  description: string;
  action: string;
  color: string;
}) {
  return (
    <div className="bg-white dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
      <div className="flex items-center space-x-3 mb-3">
        <div className={`w-10 h-10 ${color} rounded-lg flex items-center justify-center`}>
          <Icon className="w-5 h-5 text-white" />
        </div>
        <h4 className="font-medium text-gray-900 dark:text-white">{title}</h4>
      </div>
      <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">{description}</p>
      <button className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm font-medium">
        {action} →
      </button>
    </div>
  );
}

// Placeholder Tab Components
function CalendarTab() {
  return (
    <div className="text-center py-12">
      <FaCalendarAlt className="w-16 h-16 text-gray-400 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
        Content Calendar
      </h3>
      <p className="text-gray-600 dark:text-gray-400">
        Schedule and manage your social media posts across platforms
      </p>
      <p className="text-sm text-purple-600 dark:text-purple-400 mt-2">
        Coming soon in the full implementation
      </p>
    </div>
  );
}

function AnalyticsTab() {
  return (
    <div className="text-center py-12">
      <FaChartLine className="w-16 h-16 text-gray-400 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
        Analytics & Insights
      </h3>
      <p className="text-gray-600 dark:text-gray-400">
        Track performance metrics and get insights about your audience
      </p>
      <p className="text-sm text-purple-600 dark:text-purple-400 mt-2">
        Instagram Insights & Facebook Analytics integration ready
      </p>
    </div>
  );
}

function AccountsTab({ 
  connectedAccounts, 
  onConnectAccount 
}: { 
  connectedAccounts: SocialMediaAccount[]; 
  onConnectAccount: (platform: 'instagram' | 'facebook') => void;
}) {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Connected Accounts
        </h3>
        <div className="flex space-x-3">
          <button
            onClick={() => onConnectAccount('facebook')}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center space-x-2"
          >
            <FaFacebook className="w-4 h-4" />
            <span>Connect Facebook</span>
          </button>
          <button
            onClick={() => onConnectAccount('instagram')}
            className="px-4 py-2 bg-pink-600 hover:bg-pink-700 text-white rounded-lg transition-colors flex items-center space-x-2"
          >
            <FaInstagram className="w-4 h-4" />
            <span>Connect Instagram</span>
          </button>
        </div>
      </div>

      {connectedAccounts.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <FaConnectdevelop className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No Connected Accounts
          </h4>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Connect your Instagram and Facebook accounts to start planning your content
          </p>
          <div className="flex justify-center space-x-4">
            <button
              onClick={() => onConnectAccount('facebook')}
              className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center space-x-2"
            >
              <FaFacebook className="w-5 h-5" />
              <span>Connect Facebook</span>
            </button>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {connectedAccounts.map((account) => (
            <div
              key={account.id}
              className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4"
            >
              <div className="flex items-center space-x-3">
                {account.platform === 'instagram' ? (
                  <FaInstagram className="w-8 h-8 text-pink-500" />
                ) : (
                  <FaFacebook className="w-8 h-8 text-blue-500" />
                )}
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    {account.display_name}
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    @{account.username}
                  </p>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  account.is_active 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                    : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                }`}>
                  {account.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

function SettingsTab() {
  return (
    <div className="text-center py-12">
      <FaCog className="w-16 h-16 text-gray-400 mx-auto mb-4" />
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
        Planner Settings
      </h3>
      <p className="text-gray-600 dark:text-gray-400">
        Configure your posting preferences, default hashtags, and automation settings
      </p>
      <p className="text-sm text-purple-600 dark:text-purple-400 mt-2">
        Settings panel with optimal timing & hashtag management
      </p>
    </div>
  );
} 