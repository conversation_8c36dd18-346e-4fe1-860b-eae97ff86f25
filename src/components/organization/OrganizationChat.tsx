'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { motion } from 'framer-motion';
import { OrganizationChannel, OrganizationMessage, OrganizationMember, OrganizationProject } from '@/types/organization';

interface OrganizationChatProps {
  organizationId: string;
  currentUser: OrganizationMember;
  projects?: OrganizationProject[];
}

type ChatView = 'channels' | 'direct' | 'threads';

export default function OrganizationChat({ organizationId, currentUser, projects = [] }: OrganizationChatProps) {
  const [chatView, setChatView] = useState<ChatView>('channels');
  const [channels, setChannels] = useState<OrganizationChannel[]>([]);
  const [selectedChannel, setSelectedChannel] = useState<OrganizationChannel | null>(null);
  const [messages, setMessages] = useState<OrganizationMessage[]>([]);
  const [teamMembers, setTeamMembers] = useState<OrganizationMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateChannel, setShowCreateChannel] = useState(false);
  const [showChannelSettings, setShowChannelSettings] = useState(false);
  const [messageInput, setMessageInput] = useState('');
  const [onlineUsers, setOnlineUsers] = useState<string[]>([]);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const loadChatData = useCallback(async () => {
    try {
      setLoading(true);
      
      // Mock data - replace with actual API calls
      const mockChannels: OrganizationChannel[] = [
        {
          id: 'general',
          organization_id: organizationId,
          name: 'general',
          description: 'General discussion for the team',
          type: 'public',
          is_archived: false,
          is_readonly: false,
          members: ['user-1', 'user-2', 'user-3'],
          admins: ['user-1'],
          message_count: 156,
          last_activity_at: '2024-01-20T15:30:00Z',
          created_by: 'user-1',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-20T15:30:00Z'
        },
        {
          id: 'dev-team',
          organization_id: organizationId,
          name: 'dev-team',
          description: 'Development team discussions',
          type: 'private',
          is_archived: false,
          is_readonly: false,
          members: ['user-1', 'user-2'],
          admins: ['user-1'],
          message_count: 89,
          last_activity_at: '2024-01-20T14:15:00Z',
          created_by: 'user-1',
          created_at: '2024-01-05T00:00:00Z',
          updated_at: '2024-01-20T14:15:00Z'
        },
        {
          id: 'project-website',
          organization_id: organizationId,
          name: 'project-website',
          description: 'Website redesign project discussions',
          type: 'project',
          project_id: '1',
          is_archived: false,
          is_readonly: false,
          members: ['user-1', 'user-2', 'user-3'],
          admins: ['user-1'],
          message_count: 234,
          last_activity_at: '2024-01-20T16:45:00Z',
          created_by: 'user-1',
          created_at: '2024-01-15T00:00:00Z',
          updated_at: '2024-01-20T16:45:00Z'
        }
      ];

      const mockMessages: OrganizationMessage[] = [
        {
          id: 'msg-1',
          channel_id: 'general',
          sender_id: 'user-1',
          sender: {
            id: 'user-1',
            display_name: 'John Smith',
            email: '<EMAIL>',
            avatar_url: ''
          },
          content: 'Welcome to the team chat! 🎉',
          message_type: 'text',
          attachments: [],
          mentions: [],
          reactions: [
            { emoji: '👍', users: ['user-2', 'user-3'], count: 2 },
            { emoji: '🎉', users: ['user-2'], count: 1 }
          ],
          reply_count: 0,
          is_edited: false,
          edit_history: [],
          created_at: '2024-01-20T15:30:00Z',
          updated_at: '2024-01-20T15:30:00Z'
        },
        {
          id: 'msg-2',
          channel_id: 'general',
          sender_id: 'user-2',
          sender: {
            id: 'user-2',
            display_name: 'Sarah Wilson',
            email: '<EMAIL>',
            avatar_url: ''
          },
          content: 'Thanks for the warm welcome! Excited to be here 🚀',
          message_type: 'text',
          attachments: [],
          mentions: ['user-1'],
          reactions: [],
          reply_count: 0,
          is_edited: false,
          edit_history: [],
          created_at: '2024-01-20T15:32:00Z',
          updated_at: '2024-01-20T15:32:00Z'
        }
      ];

      const mockMembers: OrganizationMember[] = [
        {
          id: 'member-1',
          organization_id: organizationId,
          user_id: 'user-1',
          role: 'admin',
          permissions: [],
          department: 'Engineering',
          title: 'Team Lead',
          status: 'active',
          joined_at: '2024-01-01T00:00:00Z',
          last_activity_at: '2024-01-20T16:00:00Z',
          user: {
            id: 'user-1',
            display_name: 'John Smith',
            email: '<EMAIL>',
            avatar_url: ''
          }
        },
        {
          id: 'member-2',
          organization_id: organizationId,
          user_id: 'user-2',
          role: 'member',
          permissions: [],
          department: 'Design',
          title: 'UI/UX Designer',
          status: 'active',
          joined_at: '2024-01-05T00:00:00Z',
          last_activity_at: '2024-01-20T15:45:00Z',
          user: {
            id: 'user-2',
            display_name: 'Sarah Wilson',
            email: '<EMAIL>',
            avatar_url: ''
          }
        }
      ];

      setChannels(mockChannels);
      setMessages(mockMessages);
      setTeamMembers(mockMembers);
      setOnlineUsers(['user-1', 'user-2']);
      
      // Auto-select general channel
      setSelectedChannel(mockChannels[0]);
      
    } catch (error) {
      console.error('Error loading chat data:', error);
    } finally {
      setLoading(false);
    }
  }, [organizationId]);

  useEffect(() => {
    loadChatData();
  }, [loadChatData]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const sendMessage = async () => {
    if (!messageInput.trim() || !selectedChannel) return;

    const newMessage: OrganizationMessage = {
      id: `msg-${Date.now()}`,
      channel_id: selectedChannel.id,
      sender_id: currentUser.user_id,
      sender: currentUser.user,
      content: messageInput.trim(),
      message_type: 'text',
      attachments: [],
      mentions: [],
      reactions: [],
      reply_count: 0,
      is_edited: false,
      edit_history: [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    setMessages(prev => [...prev, newMessage]);
    setMessageInput('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const addReaction = (messageId: string, emoji: string) => {
    setMessages(prev => prev.map(msg => {
      if (msg.id === messageId) {
        const existingReaction = msg.reactions.find(r => r.emoji === emoji);
        if (existingReaction) {
          if (existingReaction.users.includes(currentUser.user_id)) {
            // Remove reaction
            return {
              ...msg,
              reactions: msg.reactions.map(r => 
                r.emoji === emoji 
                  ? { ...r, users: r.users.filter(u => u !== currentUser.user_id), count: r.count - 1 }
                  : r
              ).filter(r => r.count > 0)
            };
          } else {
            // Add reaction
            return {
              ...msg,
              reactions: msg.reactions.map(r => 
                r.emoji === emoji 
                  ? { ...r, users: [...r.users, currentUser.user_id], count: r.count + 1 }
                  : r
              )
            };
          }
        } else {
          // New reaction
          return {
            ...msg,
            reactions: [...msg.reactions, { emoji, users: [currentUser.user_id], count: 1 }]
          };
        }
      }
      return msg;
    }));
  };

  const renderChannelList = () => (
    <div className="w-64 bg-gray-900 text-white flex flex-col h-full">
      {/* Organization Header */}
      <div className="p-4 border-b border-gray-700">
        <h2 className="font-semibold text-lg">Team Chat</h2>
        <p className="text-gray-400 text-sm">Organization Name</p>
      </div>

      {/* Chat Views */}
      <div className="px-4 py-2">
        <div className="flex space-x-1 bg-gray-800 rounded-lg p-1">
          {[
            { id: 'channels', label: 'Channels', icon: '#' },
            { id: 'direct', label: 'DMs', icon: '@' },
            { id: 'threads', label: 'Threads', icon: '💬' }
          ].map((view) => (
            <button
              key={view.id}
              onClick={() => setChatView(view.id as ChatView)}
              className={`flex-1 flex items-center justify-center space-x-1 px-3 py-2 rounded-md text-sm transition-colors ${
                chatView === view.id
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-300 hover:text-white hover:bg-gray-700'
              }`}
            >
              <span>{view.icon}</span>
              <span className="hidden sm:inline">{view.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Channels List */}
      <div className="flex-1 overflow-y-auto">
        {chatView === 'channels' && (
          <div className="px-4 py-2">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium text-gray-300 uppercase tracking-wide">Channels</h3>
              <button
                onClick={() => setShowCreateChannel(true)}
                className="text-gray-400 hover:text-white text-sm"
              >
                +
              </button>
            </div>
            
            <div className="space-y-1">
              {channels.map((channel) => (
                <motion.button
                  key={channel.id}
                  onClick={() => setSelectedChannel(channel)}
                  whileHover={{ x: 2 }}
                  className={`w-full flex items-center space-x-2 px-2 py-2 rounded-md text-left transition-colors ${
                    selectedChannel?.id === channel.id
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                  }`}
                >
                  <span className="text-gray-400">
                    {channel.type === 'public' ? '#' : 
                     channel.type === 'private' ? '🔒' : '📁'}
                  </span>
                  <span className="flex-1 truncate">{channel.name}</span>
                  {channel.type === 'project' && (
                    <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                  )}
                </motion.button>
              ))}
            </div>
          </div>
        )}

        {chatView === 'direct' && (
          <div className="px-4 py-2">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-medium text-gray-300 uppercase tracking-wide">Direct Messages</h3>
            </div>
            
            <div className="space-y-1">
              {teamMembers
                .filter(member => member.user_id !== currentUser.user_id)
                .map((member) => (
                  <button
                    key={member.id}
                    className="w-full flex items-center space-x-3 px-2 py-2 rounded-md text-left hover:bg-gray-700 transition-colors"
                  >
                    <div className="relative">
                      <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-400 to-purple-500 flex items-center justify-center text-white text-sm font-medium">
                        {member.user?.display_name?.charAt(0) || 'U'}
                      </div>
                      {onlineUsers.includes(member.user_id) && (
                        <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-400 border-2 border-gray-900 rounded-full"></div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-white text-sm font-medium truncate">
                        {member.user?.display_name || 'Unknown User'}
                      </p>
                      <p className="text-gray-400 text-xs truncate">{member.title}</p>
                    </div>
                  </button>
                ))}
            </div>
          </div>
        )}
      </div>

      {/* User Info */}
      <div className="p-4 border-t border-gray-700">
        <div className="flex items-center space-x-3">
          <div className="relative">
            <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-400 to-purple-500 flex items-center justify-center text-white text-sm font-medium">
              {currentUser.user?.display_name?.charAt(0) || 'U'}
            </div>
            <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-400 border-2 border-gray-900 rounded-full"></div>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-white text-sm font-medium truncate">
              {currentUser.user?.display_name || 'Unknown User'}
            </p>
            <p className="text-gray-400 text-xs truncate">{currentUser.title}</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderChatArea = () => {
    if (!selectedChannel) {
      return (
        <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-800">
          <div className="text-center">
            <div className="text-6xl mb-4">💬</div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Select a Channel
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Choose a channel to start chatting with your team
            </p>
          </div>
        </div>
      );
    }

    const channelMessages = messages.filter(msg => msg.channel_id === selectedChannel.id);

    return (
      <div className="flex-1 flex flex-col bg-white dark:bg-gray-800">
        {/* Channel Header */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-gray-400 text-xl">
                {selectedChannel.type === 'public' ? '#' : 
                 selectedChannel.type === 'private' ? '🔒' : '📁'}
              </span>
              <div>
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {selectedChannel.name}
                </h2>
                {selectedChannel.description && (
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {selectedChannel.description}
                  </p>
                )}
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {selectedChannel.members.length} members
              </span>
              <button
                onClick={() => setShowChannelSettings(true)}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                ⚙️
              </button>
            </div>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-6 space-y-4">
          {channelMessages.map((message) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex space-x-3"
            >
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-400 to-purple-500 flex items-center justify-center text-white text-sm font-medium flex-shrink-0">
                {message.sender?.display_name?.charAt(0) || 'U'}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-baseline space-x-2 mb-1">
                  <span className="font-medium text-gray-900 dark:text-white">
                    {message.sender?.display_name || 'Unknown User'}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {new Date(message.created_at).toLocaleTimeString()}
                  </span>
                </div>
                
                <div className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                  {message.content}
                </div>

                {/* Reactions */}
                {message.reactions.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {message.reactions.map((reaction) => (
                      <button
                        key={reaction.emoji}
                        onClick={() => addReaction(message.id, reaction.emoji)}
                        className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs border transition-colors ${
                          reaction.users.includes(currentUser.user_id)
                            ? 'bg-blue-100 dark:bg-blue-900 border-blue-300 dark:border-blue-700 text-blue-800 dark:text-blue-200'
                            : 'bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                        }`}
                      >
                        <span>{reaction.emoji}</span>
                        <span>{reaction.count}</span>
                      </button>
                    ))}
                    <button
                      onClick={() => addReaction(message.id, '👍')}
                      className="w-6 h-6 flex items-center justify-center rounded-full text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-600 dark:hover:text-gray-300"
                    >
                      +
                    </button>
                  </div>
                )}
              </div>
            </motion.div>
          ))}
          <div ref={messagesEndRef} />
        </div>

        {/* Message Input */}
        <div className="p-6 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-end space-x-3">
            <div className="flex-1">
              <textarea
                value={messageInput}
                onChange={(e) => setMessageInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={`Message #${selectedChannel.name}`}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none dark:bg-gray-700 dark:text-white"
                rows={1}
                style={{ minHeight: '44px', maxHeight: '120px' }}
              />
            </div>
            <button
              onClick={sendMessage}
              disabled={!messageInput.trim()}
              className="px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              Send
            </button>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="flex h-full bg-white dark:bg-gray-900 rounded-lg shadow-lg overflow-hidden">
      {renderChannelList()}
      {renderChatArea()}

      {/* Create Channel Modal */}
      {showCreateChannel && (
        <CreateChannelModal
          organizationId={organizationId}
          projects={projects}
          teamMembers={teamMembers}
          onClose={() => setShowCreateChannel(false)}
          onSave={(channel) => {
            setChannels([...channels, channel]);
            setShowCreateChannel(false);
          }}
        />
      )}

      {/* Channel Settings Modal */}
      {showChannelSettings && selectedChannel && (
        <ChannelSettingsModal
          channel={selectedChannel}
          teamMembers={teamMembers}
          onClose={() => setShowChannelSettings(false)}
          onSave={(updatedChannel) => {
            setChannels(channels.map(c => c.id === updatedChannel.id ? updatedChannel : c));
            setSelectedChannel(updatedChannel);
            setShowChannelSettings(false);
          }}
        />
      )}
    </div>
  );
}

// Additional modal components
interface CreateChannelModalProps {
  organizationId: string;
  projects: OrganizationProject[];
  teamMembers: OrganizationMember[];
  onClose: () => void;
  onSave: (channel: OrganizationChannel) => void;
}

function CreateChannelModal({ organizationId, projects, teamMembers, onClose, onSave }: CreateChannelModalProps) {
  const [channelName, setChannelName] = useState('');
  const [channelDescription, setChannelDescription] = useState('');
  const [channelType, setChannelType] = useState<'public' | 'private' | 'project'>('public');
  const [selectedProject, setSelectedProject] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!channelName.trim()) return;

    const newChannel: OrganizationChannel = {
      id: `channel-${Date.now()}`,
      organization_id: organizationId,
      name: channelName.toLowerCase().replace(/\s+/g, '-'),
      description: channelDescription || undefined,
      type: channelType,
      project_id: channelType === 'project' ? selectedProject : undefined,
      is_archived: false,
      is_readonly: false,
      members: teamMembers.map(m => m.user_id),
      admins: [teamMembers[0]?.user_id],
      message_count: 0,
      created_by: teamMembers[0]?.user_id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    onSave(newChannel);
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white dark:bg-gray-800 rounded-xl max-w-md w-full"
      >
        <div className="p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Create New Channel
          </h2>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Channel Name
              </label>
              <input
                type="text"
                value={channelName}
                onChange={(e) => setChannelName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="e.g., marketing-team"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Description (Optional)
              </label>
              <textarea
                value={channelDescription}
                onChange={(e) => setChannelDescription(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                rows={2}
                placeholder="What's this channel about?"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Channel Type
              </label>
              <select
                value={channelType}
                onChange={(e) => setChannelType(e.target.value as 'public' | 'private' | 'project')}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              >
                <option value="public">Public - Anyone can join</option>
                <option value="private">Private - Invite only</option>
                <option value="project">Project - Linked to a project</option>
              </select>
            </div>

            {channelType === 'project' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Select Project
                </label>
                <select
                  value={selectedProject}
                  onChange={(e) => setSelectedProject(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  required
                >
                  <option value="">Choose a project</option>
                  {projects.map((project) => (
                    <option key={project.id} value={project.id}>
                      {project.name}
                    </option>
                  ))}
                </select>
              </div>
            )}

            <div className="flex justify-end space-x-3 mt-6">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Create Channel
              </button>
            </div>
          </form>
        </div>
      </motion.div>
    </div>
  );
}

interface ChannelSettingsModalProps {
  channel: OrganizationChannel;
  teamMembers: OrganizationMember[];
  onClose: () => void;
  onSave: (channel: OrganizationChannel) => void;
}

function ChannelSettingsModal({ channel, onClose }: ChannelSettingsModalProps) {
  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white dark:bg-gray-800 rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
      >
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Channel Settings - #{channel.name}
            </h2>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
              ✕
            </button>
          </div>
          {/* Channel settings implementation */}
        </div>
      </motion.div>
    </div>
  );
} 