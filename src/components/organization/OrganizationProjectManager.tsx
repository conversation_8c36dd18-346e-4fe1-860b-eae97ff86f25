'use client';

import { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { OrganizationProject, OrganizationTask, OrganizationMember } from '@/types/organization';

interface ProjectManagerProps {
  organizationId: string;
  currentUser: OrganizationMember;
}

type ViewMode = 'overview' | 'kanban' | 'timeline' | 'calendar' | 'analytics';

export default function OrganizationProjectManager({ organizationId }: ProjectManagerProps) {
  const [viewMode, setViewMode] = useState<ViewMode>('overview');
  const [projects, setProjects] = useState<OrganizationProject[]>([]);
  const [selectedProject, setSelectedProject] = useState<OrganizationProject | null>(null);
  const [tasks, setTasks] = useState<OrganizationTask[]>([]);
  const [teamMembers, setTeamMembers] = useState<OrganizationMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateProject, setShowCreateProject] = useState(false);
  const [showCreateTask, setShowCreateTask] = useState(false);

  // Load initial data
  const loadProjectData = useCallback(async () => {
    try {
      setLoading(true);
      // Mock data - replace with actual API calls
      const mockProjects: OrganizationProject[] = [
        {
          id: '1',
          organization_id: organizationId,
          name: 'Website Redesign',
          description: 'Complete overhaul of the company website with new branding and improved UX',
          status: 'active',
          priority: 'high',
          visibility: 'organization',
          start_date: '2024-01-15',
          due_date: '2024-03-15',
          budget: 50000,
          estimated_hours: 320,
          actual_hours: 145,
          project_manager_id: 'user-1',
          team_members: ['user-1', 'user-2', 'user-3'],
          tags: ['design', 'development', 'branding'],
          custom_fields: {},
          progress_percentage: 45,
          tasks_total: 28,
          tasks_completed: 12,
          created_by: 'user-1',
          created_at: '2024-01-10T00:00:00Z',
          updated_at: '2024-01-20T00:00:00Z'
        },
        {
          id: '2',
          organization_id: organizationId,
          name: 'Mobile App Development',
          description: 'Native mobile application for iOS and Android platforms',
          status: 'planning',
          priority: 'medium',
          visibility: 'organization',
          start_date: '2024-02-01',
          due_date: '2024-06-01',
          budget: 75000,
          estimated_hours: 480,
          actual_hours: 0,
          project_manager_id: 'user-2',
          team_members: ['user-2', 'user-4', 'user-5'],
          tags: ['mobile', 'react-native', 'api'],
          custom_fields: {},
          progress_percentage: 10,
          tasks_total: 45,
          tasks_completed: 2,
          created_by: 'user-2',
          created_at: '2024-01-25T00:00:00Z',
          updated_at: '2024-01-25T00:00:00Z'
        }
      ];

      const mockTasks: OrganizationTask[] = [
        {
          id: 'task-1',
          organization_id: organizationId,
          project_id: '1',
          title: 'Design new homepage mockup',
          description: 'Create high-fidelity mockup for the new homepage design',
          status: 'in_progress',
          priority: 'high',
          assignee_id: 'user-2',
          reporter_id: 'user-1',
          due_date: '2024-01-30',
          estimated_hours: 16,
          actual_hours: 8,
          time_entries: [],
          dependencies: [],
          subtasks: [],
          labels: ['design', 'high-priority'],
          custom_fields: {},
          comments_count: 3,
          attachments_count: 2,
          created_by: 'user-1',
          created_at: '2024-01-15T00:00:00Z',
          updated_at: '2024-01-18T00:00:00Z'
        }
      ];

      const mockMembers: OrganizationMember[] = [
        {
          id: 'member-1',
          organization_id: organizationId,
          user_id: 'user-1',
          role: 'admin',
          permissions: ['projects.create', 'projects.edit', 'tasks.create'],
          department: 'Engineering',
          title: 'Project Manager',
          status: 'active',
          joined_at: '2024-01-01T00:00:00Z',
          user: {
            id: 'user-1',
            display_name: 'John Smith',
            email: '<EMAIL>',
            avatar_url: ''
          }
        }
      ];

      setProjects(mockProjects);
      setTasks(mockTasks);
      setTeamMembers(mockMembers);
    } catch (error) {
      console.error('Error loading project data:', error);
    } finally {
      setLoading(false);
    }
  }, [organizationId]);

  useEffect(() => {
    loadProjectData();
  }, [loadProjectData]);

  const renderProjectOverview = () => (
    <div className="space-y-6">
      {/* Project Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {[
          { label: 'Total Projects', value: projects.length, color: 'blue', icon: '📁' },
          { label: 'Active Projects', value: projects.filter(p => p.status === 'active').length, color: 'green', icon: '⚡' },
          { label: 'Tasks Completed', value: projects.reduce((sum, p) => sum + p.tasks_completed, 0), color: 'purple', icon: '✅' },
          { label: 'Team Members', value: teamMembers.length, color: 'orange', icon: '👥' }
        ].map((stat, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className={`bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700`}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{stat.label}</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{stat.value}</p>
              </div>
              <div className="text-2xl">{stat.icon}</div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Active Projects */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700">
        <div className="p-6 border-b border-gray-100 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Active Projects</h2>
            <button
              onClick={() => setShowCreateProject(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
            >
              <span>+</span>
              <span>New Project</span>
            </button>
          </div>
        </div>
        
        <div className="p-6 space-y-4">
          {projects.map((project) => (
            <motion.div
              key={project.id}
              whileHover={{ scale: 1.02 }}
              className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-all cursor-pointer"
              onClick={() => setSelectedProject(project)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{project.name}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      project.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                      project.status === 'planning' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                      'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                    }`}>
                      {project.status}
                    </span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      project.priority === 'high' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                      project.priority === 'medium' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                      'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                    }`}>
                      {project.priority}
                    </span>
                  </div>
                  
                  <p className="text-gray-600 dark:text-gray-400 mb-3">{project.description}</p>
                  
                  <div className="flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400">
                    <span>📅 Due: {new Date(project.due_date!).toLocaleDateString()}</span>
                    <span>💰 Budget: ${project.budget?.toLocaleString()}</span>
                    <span>⏱️ {project.actual_hours}h / {project.estimated_hours}h</span>
                    <span>✅ {project.tasks_completed} / {project.tasks_total} tasks</span>
                  </div>
                </div>
                
                <div className="flex flex-col items-end">
                  <div className="flex -space-x-2 mb-2">
                    {project.team_members.slice(0, 3).map((memberId, index) => (
                      <div
                        key={index}
                        className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-400 to-purple-500 border-2 border-white dark:border-gray-800 flex items-center justify-center text-white text-xs font-medium"
                      >
                        {memberId.slice(-1)}
                      </div>
                    ))}
                    {project.team_members.length > 3 && (
                      <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-600 border-2 border-white dark:border-gray-800 flex items-center justify-center text-gray-600 dark:text-gray-300 text-xs font-medium">
                        +{project.team_members.length - 3}
                      </div>
                    )}
                  </div>
                  
                  <div className="w-32">
                    <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                      <span>Progress</span>
                      <span>{project.progress_percentage}%</span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div 
                        className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${project.progress_percentage}%` }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderKanbanView = () => (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Task Board</h2>
        <button
          onClick={() => setShowCreateTask(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          + Add Task
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {[
          { id: 'todo', title: 'To Do', color: 'gray' },
          { id: 'in_progress', title: 'In Progress', color: 'blue' },
          { id: 'review', title: 'Review', color: 'yellow' },
          { id: 'done', title: 'Done', color: 'green' }
        ].map((column) => (
          <div key={column.id} className="space-y-3">
            <div className={`flex items-center space-x-2 p-3 rounded-lg bg-${column.color}-50 dark:bg-${column.color}-900/20`}>
              <h3 className={`font-medium text-${column.color}-800 dark:text-${column.color}-200`}>
                {column.title}
              </h3>
              <span className={`px-2 py-1 rounded-full text-xs bg-${column.color}-100 dark:bg-${column.color}-900/40 text-${column.color}-800 dark:text-${column.color}-200`}>
                {tasks.filter(task => task.status === column.id).length}
              </span>
            </div>
            
            <div className="space-y-3 min-h-[400px]">
              {tasks
                .filter(task => task.status === column.id)
                .map((task) => (
                  <motion.div
                    key={task.id}
                    layout
                    whileHover={{ scale: 1.02 }}
                    className="p-4 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg shadow-sm hover:shadow-md transition-all cursor-pointer"
                  >
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">{task.title}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">{task.description}</p>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          task.priority === 'urgent' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                          task.priority === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' :
                          task.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                          'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                        }`}>
                          {task.priority}
                        </span>
                        {task.due_date && (
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            📅 {new Date(task.due_date).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                      
                      {task.assignee && (
                        <div className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-400 to-purple-500 flex items-center justify-center text-white text-xs font-medium">
                          {task.assignee.display_name?.charAt(0) || 'U'}
                        </div>
                      )}
                    </div>
                  </motion.div>
                ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderTimelineView = () => (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 p-6">
      <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Project Timeline</h2>
      
      <div className="space-y-4">
        {projects.map((project, index) => (
          <motion.div
            key={project.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            className="relative"
          >
            <div className="flex items-center space-x-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg">
              <div className="flex-shrink-0 w-4 h-4 rounded-full bg-blue-500"></div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900 dark:text-white">{project.name}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {new Date(project.start_date!).toLocaleDateString()} - {new Date(project.due_date!).toLocaleDateString()}
                </p>
              </div>
              <div className="flex-shrink-0">
                <div className="w-32">
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full"
                      style={{ width: `${project.progress_percentage}%` }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );

  const renderAnalytics = () => (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-100 dark:border-gray-700 p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Project Analytics</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[
            { title: 'Project Completion Rate', value: '78%', change: '+12%', positive: true },
            { title: 'Average Task Time', value: '4.2h', change: '-0.8h', positive: true },
            { title: 'Team Productivity', value: '94%', change: '+5%', positive: true },
            { title: 'Budget Utilization', value: '67%', change: '+3%', positive: false },
            { title: 'On-Time Delivery', value: '85%', change: '+10%', positive: true },
            { title: 'Resource Allocation', value: '92%', change: '-2%', positive: false }
          ].map((metric, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="p-4 border border-gray-200 dark:border-gray-600 rounded-lg"
            >
              <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">{metric.title}</h3>
              <div className="flex items-center justify-between">
                <span className="text-2xl font-bold text-gray-900 dark:text-white">{metric.value}</span>
                <span className={`text-sm font-medium ${
                  metric.positive ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                }`}>
                  {metric.change}
                </span>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Project Management</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage your organization&apos;s projects and tasks
          </p>
        </div>
        
        {/* View Mode Selector */}
        <div className="flex items-center space-x-2 mt-4 sm:mt-0">
          {[
            { id: 'overview', label: 'Overview', icon: '📊' },
            { id: 'kanban', label: 'Kanban', icon: '📋' },
            { id: 'timeline', label: 'Timeline', icon: '📅' },
            { id: 'analytics', label: 'Analytics', icon: '📈' }
          ].map((mode) => (
            <button
              key={mode.id}
              onClick={() => setViewMode(mode.id as ViewMode)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                viewMode === mode.id
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
              }`}
            >
              <span>{mode.icon}</span>
              <span className="hidden sm:inline">{mode.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Main Content */}
      <AnimatePresence mode="wait">
        <motion.div
          key={viewMode}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          {viewMode === 'overview' && renderProjectOverview()}
          {viewMode === 'kanban' && renderKanbanView()}
          {viewMode === 'timeline' && renderTimelineView()}
          {viewMode === 'analytics' && renderAnalytics()}
        </motion.div>
      </AnimatePresence>

      {/* Create Project Modal */}
      {showCreateProject && (
        <CreateProjectModal 
          onClose={() => setShowCreateProject(false)}
          onSave={(project) => {
            setProjects([...projects, project]);
            setShowCreateProject(false);
          }}
          organizationId={organizationId}
          teamMembers={teamMembers}
        />
      )}

      {/* Create Task Modal */}
      {showCreateTask && (
        <CreateTaskModal
          onClose={() => setShowCreateTask(false)}
          onSave={(task) => {
            setTasks([...tasks, task]);
            setShowCreateTask(false);
          }}
          organizationId={organizationId}
          projects={projects}
          teamMembers={teamMembers}
        />
      )}

      {/* Project Details Modal */}
      {selectedProject && (
        <ProjectDetailsModal
          project={selectedProject}
          onClose={() => setSelectedProject(null)}
          tasks={tasks.filter(t => t.project_id === selectedProject.id)}
          teamMembers={teamMembers}
        />
      )}
    </div>
  );
}

// Additional modal components would be defined here...
interface CreateProjectModalProps {
  onClose: () => void;
  onSave: (project: OrganizationProject) => void;
  organizationId: string;
  teamMembers: OrganizationMember[];
}

function CreateProjectModal({ onClose }: CreateProjectModalProps) {
  // Implementation for create project modal
  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Create New Project</h2>
          {/* Form implementation */}
          <div className="flex justify-end space-x-3 mt-6">
            <button onClick={onClose} className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
              Cancel
            </button>
            <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
              Create Project
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

interface CreateTaskModalProps {
  onClose: () => void;
  onSave: (task: OrganizationTask) => void;
  organizationId: string;
  projects: OrganizationProject[];
  teamMembers: OrganizationMember[];
}

function CreateTaskModal({ onClose }: CreateTaskModalProps) {
  // Implementation for create task modal
  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Create New Task</h2>
          {/* Form implementation */}
          <div className="flex justify-end space-x-3 mt-6">
            <button onClick={onClose} className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700">
              Cancel
            </button>
            <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
              Create Task
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

interface ProjectDetailsModalProps {
  project: OrganizationProject;
  onClose: () => void;
  tasks: OrganizationTask[];
  teamMembers: OrganizationMember[];
}

function ProjectDetailsModal({ project, onClose }: ProjectDetailsModalProps) {
  // Implementation for project details modal
  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">{project.name}</h2>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600">✕</button>
          </div>
          {/* Project details implementation */}
        </div>
      </div>
    </div>
  );
} 