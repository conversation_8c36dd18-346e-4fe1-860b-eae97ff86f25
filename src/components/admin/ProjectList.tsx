'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { db, Project } from '@/lib/supabase';

interface ProjectListProps {
  projects: Project[];
  onEdit: (project: Project) => void;
  onDelete: () => void;
  onRefresh: () => void;
}

export default function ProjectList({ projects, onEdit, onDelete, onRefresh }: ProjectListProps) {
  const [deleting, setDeleting] = useState<string>('');

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this project?')) {
      return;
    }

    setDeleting(id);
    try {
      const success = await db.deleteProject(id);
      if (success) {
        onDelete();
      } else {
        alert('Failed to delete project');
      }
    } catch (error) {
      console.error('Error deleting project:', error);
      alert('Failed to delete project');
    } finally {
      setDeleting('');
    }
  };

  const handleToggleActive = async (project: Project) => {
    try {
      const success = await db.updateProject(project.id, { 
        is_active: !project.is_active 
      });
      if (success) {
        onRefresh();
      } else {
        alert('Failed to update project status');
      }
    } catch (error) {
      console.error('Error updating project:', error);
      alert('Failed to update project status');
    }
  };

  if (projects.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">📁</div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          No Projects Found
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Get started by creating your first project.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Projects ({projects.length})
        </h3>
        <button
          onClick={onRefresh}
          className="text-blue-600 dark:text-blue-400 hover:underline text-sm"
        >
          Refresh
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {projects.map((project) => (
          <motion.div
            key={project.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className={`
              bg-white dark:bg-gray-800 rounded-lg shadow-sm border 
              ${!project.is_active ? 'opacity-60' : ''}
            `}
          >
            {/* Project Header */}
            <div className={`h-32 relative overflow-hidden bg-gradient-to-br ${project.gradient} rounded-t-lg`}>
              <div className="absolute inset-0 flex items-center justify-center text-white text-2xl font-bold">
                {project.title.split(" ").map((word) => word[0]).join("").toUpperCase()}
              </div>
              {!project.is_active && (
                <div className="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-xs">
                  Inactive
                </div>
              )}
            </div>

            <div className="p-4">
              <div className="flex justify-between items-start mb-2">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                  {project.title}
                </h3>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {project.year}
                </span>
              </div>

              <div className="mb-3">
                <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                  project.status === "completed" 
                    ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" 
                    : project.status === "in-progress" 
                    ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200" 
                    : "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                }`}>
                  {project.status === "completed" 
                    ? "Completed" 
                    : project.status === "in-progress" 
                    ? "In Progress" 
                    : "Upcoming"}
                </span>
              </div>

              <p className="text-gray-600 dark:text-gray-300 text-sm mb-3 line-clamp-3">
                {project.description}
              </p>

              {/* Technologies */}
              <div className="mb-4">
                <div className="flex flex-wrap gap-1">
                  {project.technologies.slice(0, 3).map((tech, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded"
                    >
                      {tech}
                    </span>
                  ))}
                  {project.technologies.length > 3 && (
                    <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded">
                      +{project.technologies.length - 3}
                    </span>
                  )}
                </div>
              </div>

              {/* Links */}
              {(project.android_link || project.windows_link || project.link) && (
                <div className="mb-4 text-xs text-gray-500 dark:text-gray-400">
                  {project.android_link && <span className="block">📱 Android</span>}
                  {project.windows_link && <span className="block">🖥️ Windows</span>}
                  {project.link && <span className="block">🌐 Website</span>}
                </div>
              )}

              {/* Meta Info */}
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-4 space-y-1">
                <p>Order: {project.sort_order}</p>
                <p>Created: {new Date(project.created_at).toLocaleDateString()}</p>
              </div>

              {/* Actions */}
              <div className="flex gap-2 pt-3 border-t border-gray-200 dark:border-gray-700">
                <button
                  onClick={() => onEdit(project)}
                  className="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors"
                >
                  Edit
                </button>
                <button
                  onClick={() => handleToggleActive(project)}
                  className={`px-3 py-2 text-sm rounded transition-colors ${
                    project.is_active
                      ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200 dark:bg-yellow-900 dark:text-yellow-200'
                      : 'bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900 dark:text-green-200'
                  }`}
                >
                  {project.is_active ? 'Hide' : 'Show'}
                </button>
                <button
                  onClick={() => handleDelete(project.id)}
                  disabled={deleting === project.id}
                  className="px-3 py-2 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors disabled:opacity-50"
                >
                  {deleting === project.id ? '...' : 'Delete'}
                </button>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
} 