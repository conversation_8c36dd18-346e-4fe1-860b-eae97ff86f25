'use client';

import { useState, useEffect } from 'react';
import { SecurityConfigManager, SecurityConfig } from '@/lib/security/securityConfig';
import { motion } from 'framer-motion';

interface SecurityStats {
  totalChallenges: number;
  successfulChallenges: number;
  failedChallenges: number;
  blockedIPs: number;
  rateLimitViolations: number;
  recentSecurityEvents: any[];
}

export default function SecurityDashboard() {
  const [config, setConfig] = useState<SecurityConfig>(SecurityConfigManager.getConfig());
  const [stats, setStats] = useState<SecurityStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'config' | 'logs'>('overview');
  const [newIP, setNewIP] = useState('');
  const [ipListType, setIPListType] = useState<'whitelist' | 'blacklist'>('whitelist');

  useEffect(() => {
    loadSecurityStats();
  }, []);

  const loadSecurityStats = async () => {
    try {
      setLoading(true);
      // In a real implementation, this would fetch from your API
      const mockStats: SecurityStats = {
        totalChallenges: 156,
        successfulChallenges: 142,
        failedChallenges: 14,
        blockedIPs: 3,
        rateLimitViolations: 8,
        recentSecurityEvents: [
          { id: 1, type: 'challenge_failed', ip: '*************', timestamp: new Date().toISOString() },
          { id: 2, type: 'rate_limit_exceeded', ip: '*********', timestamp: new Date().toISOString() },
          { id: 3, type: 'login_attempt', ip: '***********', timestamp: new Date().toISOString() }
        ]
      };
      setStats(mockStats);
    } catch (error) {
      console.error('Error loading security stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleConfigUpdate = (updates: Partial<SecurityConfig>) => {
    const validation = SecurityConfigManager.validateConfig(updates);
    if (validation.valid) {
      const newConfig = SecurityConfigManager.updateConfig(updates);
      setConfig(newConfig);
    } else {
      alert(`Configuration error: ${validation.errors.join(', ')}`);
    }
  };

  const handleAddIP = () => {
    if (!newIP.trim()) return;

    if (ipListType === 'whitelist') {
      SecurityConfigManager.addToWhitelist(newIP.trim());
    } else {
      SecurityConfigManager.addToBlacklist(newIP.trim());
    }

    setConfig(SecurityConfigManager.getConfig());
    setNewIP('');
  };

  const handleRemoveIP = (ip: string, listType: 'whitelist' | 'blacklist') => {
    if (listType === 'whitelist') {
      SecurityConfigManager.removeFromWhitelist(ip);
    } else {
      SecurityConfigManager.removeFromBlacklist(ip);
    }
    setConfig(SecurityConfigManager.getConfig());
  };

  const StatCard = ({ title, value, color = 'blue' }: { title: string; value: number | string; color?: string }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`p-6 rounded-lg border border-gray-200 dark:border-gray-700 bg-gradient-to-br from-${color}-50 to-${color}-100 dark:from-${color}-900/20 dark:to-${color}-800/20`}
    >
      <h3 className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</h3>
      <p className={`text-2xl font-bold text-${color}-600 dark:text-${color}-400 mt-2`}>{value}</p>
    </motion.div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600 dark:text-gray-300">Loading security dashboard...</span>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Security Dashboard</h1>
        <p className="text-gray-600 dark:text-gray-400">Monitor and configure security settings</p>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 mb-8 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
        {[
          { id: 'overview', label: 'Overview' },
          { id: 'config', label: 'Configuration' },
          { id: 'logs', label: 'Security Logs' }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === tab.id
                ? 'bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
            }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && stats && (
        <div className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <StatCard title="Total Challenges" value={stats.totalChallenges} color="blue" />
            <StatCard title="Success Rate" value={`${Math.round((stats.successfulChallenges / stats.totalChallenges) * 100)}%`} color="green" />
            <StatCard title="Blocked IPs" value={stats.blockedIPs} color="red" />
            <StatCard title="Rate Limit Violations" value={stats.rateLimitViolations} color="orange" />
            <StatCard title="Challenge Types" value={config.challengeTypes.length} color="purple" />
            <StatCard title="Security Status" value={config.challengeEnabled ? "Active" : "Disabled"} color={config.challengeEnabled ? "green" : "red"} />
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Recent Security Events</h3>
            <div className="space-y-3">
              {stats.recentSecurityEvents.map((event) => (
                <div key={event.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div>
                    <span className="font-medium text-gray-900 dark:text-white">{event.type.replace('_', ' ')}</span>
                    <span className="text-gray-600 dark:text-gray-400 ml-2">from {event.ip}</span>
                  </div>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {new Date(event.timestamp).toLocaleString()}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Configuration Tab */}
      {activeTab === 'config' && (
        <div className="space-y-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Challenge Settings</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={config.challengeEnabled}
                    onChange={(e) => handleConfigUpdate({ challengeEnabled: e.target.checked })}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-gray-700 dark:text-gray-300">Enable Security Challenges</span>
                </label>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Challenge Expiry (minutes)
                </label>
                <input
                  type="number"
                  value={config.challengeExpiryMinutes}
                  onChange={(e) => handleConfigUpdate({ challengeExpiryMinutes: parseInt(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  min="1"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Max Attempts
                </label>
                <input
                  type="number"
                  value={config.maxChallengeAttempts}
                  onChange={(e) => handleConfigUpdate({ maxChallengeAttempts: parseInt(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  min="1"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Tolerance (pixels)
                </label>
                <input
                  type="number"
                  value={config.challengeTolerance}
                  onChange={(e) => handleConfigUpdate({ challengeTolerance: parseInt(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  min="5"
                  max="50"
                />
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Rate Limiting</h3>
            <div className="space-y-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={config.rateLimitEnabled}
                  onChange={(e) => handleConfigUpdate({ rateLimitEnabled: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-gray-700 dark:text-gray-300">Enable Rate Limiting</span>
              </label>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Login Max Requests
                  </label>
                  <input
                    type="number"
                    value={config.loginRateLimit.maxRequests}
                    onChange={(e) => handleConfigUpdate({ 
                      loginRateLimit: { ...config.loginRateLimit, maxRequests: parseInt(e.target.value) }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    min="1"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Window (minutes)
                  </label>
                  <input
                    type="number"
                    value={config.loginRateLimit.windowMinutes}
                    onChange={(e) => handleConfigUpdate({ 
                      loginRateLimit: { ...config.loginRateLimit, windowMinutes: parseInt(e.target.value) }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    min="1"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Block Duration (minutes)
                  </label>
                  <input
                    type="number"
                    value={config.loginRateLimit.blockDurationMinutes}
                    onChange={(e) => handleConfigUpdate({ 
                      loginRateLimit: { ...config.loginRateLimit, blockDurationMinutes: parseInt(e.target.value) }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    min="1"
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">IP Management</h3>
            
            <div className="mb-4">
              <div className="flex space-x-2">
                <select
                  value={ipListType}
                  onChange={(e) => setIPListType(e.target.value as 'whitelist' | 'blacklist')}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="whitelist">Whitelist</option>
                  <option value="blacklist">Blacklist</option>
                </select>
                <input
                  type="text"
                  value={newIP}
                  onChange={(e) => setNewIP(e.target.value)}
                  placeholder="Enter IP address"
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
                <button
                  onClick={handleAddIP}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  Add IP
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Whitelisted IPs</h4>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {config.ipWhitelist.map((ip) => (
                    <div key={ip} className="flex items-center justify-between p-2 bg-green-50 dark:bg-green-900/20 rounded">
                      <span className="text-sm text-gray-900 dark:text-white">{ip}</span>
                      <button
                        onClick={() => handleRemoveIP(ip, 'whitelist')}
                        className="text-red-600 hover:text-red-800 text-sm"
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                  {config.ipWhitelist.length === 0 && (
                    <p className="text-sm text-gray-500 dark:text-gray-400">No whitelisted IPs</p>
                  )}
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Blacklisted IPs</h4>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {config.ipBlacklist.map((ip) => (
                    <div key={ip} className="flex items-center justify-between p-2 bg-red-50 dark:bg-red-900/20 rounded">
                      <span className="text-sm text-gray-900 dark:text-white">{ip}</span>
                      <button
                        onClick={() => handleRemoveIP(ip, 'blacklist')}
                        className="text-red-600 hover:text-red-800 text-sm"
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                  {config.ipBlacklist.length === 0 && (
                    <p className="text-sm text-gray-500 dark:text-gray-400">No blacklisted IPs</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Security Logs Tab */}
      {activeTab === 'logs' && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Security Event Logs</h3>
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <p>Security logs will be displayed here.</p>
            <p className="text-sm mt-2">This feature requires backend integration to fetch real security events.</p>
          </div>
        </div>
      )}
    </div>
  );
}
