'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { db, TeamMember } from '@/lib/supabase';

interface TeamMemberListProps {
  members: TeamMember[];
  onEdit: (member: TeamMember) => void;
  onDelete: () => void;
  onRefresh: () => void;
}

export default function TeamMemberList({ members, onEdit, onDelete, onRefresh }: TeamMemberListProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleDelete = async (member: TeamMember) => {
    if (!confirm(`Are you sure you want to delete ${member.name}?`)) return;
    
    try {
      setLoading(true);
      const success = await db.deleteTeamMember(member.id);
      
      if (!success) {
        throw new Error('Failed to delete team member');
      }

      onDelete();
    } catch (error: unknown) {
      console.error('Error deleting team member:', error);
      setError(error instanceof Error ? error.message : 'Failed to delete team member');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleActive = async (member: TeamMember) => {
    try {
      setLoading(true);
      const updated = await db.updateTeamMember(member.id, {
        is_active: !member.is_active
      });
      
      if (!updated) {
        throw new Error('Failed to update team member');
      }

      onRefresh();
    } catch (error: unknown) {
      console.error('Error updating team member:', error);
      setError(error instanceof Error ? error.message : 'Failed to update team member');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleFeatured = async (member: TeamMember) => {
    try {
      setLoading(true);
      const updated = await db.updateTeamMember(member.id, {
        is_featured: !member.is_featured
      });
      
      if (!updated) {
        throw new Error('Failed to update team member');
      }

      onRefresh();
    } catch (error: unknown) {
      console.error('Error updating team member:', error);
      setError(error instanceof Error ? error.message : 'Failed to update team member');
    } finally {
      setLoading(false);
    }
  };

  if (members.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">👥</div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          No Team Members Yet
        </h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Start building your team by adding the first member.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <p className="text-red-700 dark:text-red-300 text-sm">{error}</p>
        </div>
      )}

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
        {members.map((member) => (
          <motion.div
            key={member.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            whileHover={{ y: -2 }}
            className={`bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden ${
              !member.is_active ? 'opacity-60' : ''
            }`}
          >
            {/* Avatar */}
            <div className="relative h-24 sm:h-32 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center overflow-hidden">
              {member.avatar_url ? (
                <Image
                  src={member.avatar_url}
                  alt={member.name}
                  fill
                  className="object-cover hover:scale-110 transition-transform duration-300"
                />
              ) : (
                <div className="w-12 h-12 sm:w-16 sm:h-16 bg-white/20 rounded-full flex items-center justify-center text-white text-lg sm:text-2xl font-bold">
                  {member.name.charAt(0).toUpperCase()}
                </div>
              )}
              
              {/* Status Badges */}
              <div className="absolute top-2 right-2 flex space-x-1">
                {member.is_featured && (
                  <span className="bg-yellow-400 text-yellow-900 px-2 py-1 rounded-full text-xs font-medium">
                    ⭐
                  </span>
                )}
                {!member.is_active && (
                  <span className="bg-red-400 text-red-900 px-2 py-1 rounded-full text-xs font-medium">
                    Inactive
                  </span>
                )}
              </div>
            </div>

            {/* Content */}
            <div className="p-3 sm:p-4">
              <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-1 truncate">
                {member.name}
              </h3>
              <p className="text-blue-600 dark:text-blue-400 font-medium text-xs sm:text-sm mb-2 truncate">
                {member.role}
              </p>
              
              {member.bio && (
                <p className="text-gray-600 dark:text-gray-300 text-sm mb-3 line-clamp-2">
                  {member.bio}
                </p>
              )}

              {/* Skills */}
              {member.skills && member.skills.length > 0 && (
                <div className="mb-3">
                  <div className="flex flex-wrap gap-1">
                    {member.skills.slice(0, 2).map((skill, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded-full"
                      >
                        {skill}
                      </span>
                    ))}
                    {member.skills.length > 2 && (
                      <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded-full">
                        +{member.skills.length - 2}
                      </span>
                    )}
                  </div>
                </div>
              )}

              {/* Meta Info */}
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-4">
                <p>Order: {member.display_order}</p>
                <p>Joined: {new Date(member.joined_date).toLocaleDateString()}</p>
              </div>

              {/* Actions */}
              <div className="flex flex-wrap gap-1 sm:gap-2">
                <button
                  onClick={() => onEdit(member)}
                  className="px-2 sm:px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded text-xs sm:text-sm hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
                >
                  Edit
                </button>
                <button
                  onClick={() => handleToggleActive(member)}
                  disabled={loading}
                  className={`px-3 py-1 rounded text-sm transition-colors ${
                    member.is_active
                      ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-300 hover:bg-yellow-200 dark:hover:bg-yellow-800'
                      : 'bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 hover:bg-green-200 dark:hover:bg-green-800'
                  }`}
                >
                  {member.is_active ? 'Deactivate' : 'Activate'}
                </button>
                <button
                  onClick={() => handleToggleFeatured(member)}
                  disabled={loading}
                  className={`px-3 py-1 rounded text-sm transition-colors ${
                    member.is_featured
                      ? 'bg-yellow-100 dark:bg-yellow-900 text-yellow-700 dark:text-yellow-300 hover:bg-yellow-200 dark:hover:bg-yellow-800'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                >
                  {member.is_featured ? 'Unfeature' : 'Feature'}
                </button>
                <button
                  onClick={() => handleDelete(member)}
                  disabled={loading}
                  className="px-3 py-1 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 rounded text-sm hover:bg-red-200 dark:hover:bg-red-800 transition-colors"
                >
                  Delete
                </button>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {loading && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 flex items-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-3"></div>
            <span className="text-gray-900 dark:text-white">Processing...</span>
          </div>
        </div>
      )}
    </div>
  );
}
