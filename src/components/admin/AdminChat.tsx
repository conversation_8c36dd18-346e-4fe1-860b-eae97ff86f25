'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { db, ChatRoom, Profile } from '@/lib/supabase';
import { useAuth } from '@/lib/auth/AuthContext';
import Chat from '../chat/Chat';

interface AdminChatProps {
  className?: string;
}

export default function AdminChat({ className = '' }: AdminChatProps) {
  const { user, isAdmin } = useAuth();
  const [adminRooms, setAdminRooms] = useState<ChatRoom[]>([]);
  const [admins, setAdmins] = useState<Profile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showCreateRoom, setShowCreateRoom] = useState(false);
  const [newRoomName, setNewRoomName] = useState('');
  const [selectedAdmins, setSelectedAdmins] = useState<string[]>([]);

  // Load admin chat rooms and admin users
  useEffect(() => {
    const loadData = async () => {
      if (!user?.id || !isAdmin) {
        console.log('Skipping admin chat data load - user not authenticated or not admin');
        return;
      }

      try {
        setLoading(true);
        setError(''); // Clear any previous errors

        console.log('Loading admin chat data for user:', user.id);

        // Load admin-only chat rooms
        console.log('Fetching admin-only chat rooms...');
        const rooms = await db.getChatRooms(user.id, 'admin_only');
        console.log('Loaded admin rooms:', rooms);
        setAdminRooms(rooms);

        // Load all admin users
        console.log('Fetching admin users...');
        const allAdmins = await db.getAdminUsers();
        console.log('Loaded admin users:', allAdmins);
        setAdmins(allAdmins);

        console.log('Admin chat data loaded successfully');
      } catch (error) {
        console.error('Error loading admin chat data:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        setError(`Failed to load admin chat data: ${errorMessage}`);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [user?.id, isAdmin]);

  const handleCreateRoom = async () => {
    if (!user?.id || !newRoomName.trim() || selectedAdmins.length === 0) return;

    try {
      setLoading(true);
      
      // Create the admin chat room
      const room = await db.createChatRoom({
        type: 'admin_only',
        name: newRoomName.trim(),
        created_by: user.id,
        is_active: true
      });

      if (room) {
        // Add creator as participant
        await db.addChatRoomParticipant(room.id, user.id);
        
        // Add selected admins as participants
        for (const adminId of selectedAdmins) {
          await db.addChatRoomParticipant(room.id, adminId);
        }

        // Send system message
        await db.sendChatMessage({
          room_id: room.id,
          sender_id: user.id,
          message: `Admin chat room "${newRoomName}" created`,
          message_type: 'system'
        });

        // Refresh rooms
        const updatedRooms = await db.getChatRooms(user.id, 'admin_only');
        setAdminRooms(updatedRooms);

        // Reset form
        setNewRoomName('');
        setSelectedAdmins([]);
        setShowCreateRoom(false);
      }
    } catch (error) {
      console.error('Error creating admin chat room:', error);
      setError('Failed to create chat room');
    } finally {
      setLoading(false);
    }
  };

  const toggleAdminSelection = (adminId: string) => {
    setSelectedAdmins(prev => 
      prev.includes(adminId) 
        ? prev.filter(id => id !== adminId)
        : [...prev, adminId]
    );
  };

  if (!isAdmin) {
    return (
      <div className={`flex flex-col items-center justify-center h-96 text-center p-4 ${className}`}>
        <div className="text-6xl mb-4">🚫</div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          Access Denied
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Admin privileges required to access this feature.
        </p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-96 ${className}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Admin Team Chat
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Secure communication for admin team members
          </p>
        </div>
        
        <button
          onClick={() => setShowCreateRoom(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          <span>New Room</span>
        </button>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <p className="text-red-700 dark:text-red-300 text-sm">{error}</p>
        </div>
      )}

      {/* Create Room Modal */}
      {showCreateRoom && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 w-full max-w-md"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Create Admin Chat Room
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Room Name
                </label>
                <input
                  type="text"
                  value={newRoomName}
                  onChange={(e) => setNewRoomName(e.target.value)}
                  placeholder="Enter room name..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Select Admins ({selectedAdmins.length} selected)
                </label>
                <div className="max-h-40 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-lg">
                  {admins.filter(admin => admin.id !== user?.id).map((admin) => (
                    <label
                      key={admin.id}
                      className="flex items-center p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
                    >
                      <input
                        type="checkbox"
                        checked={selectedAdmins.includes(admin.id)}
                        onChange={() => toggleAdminSelection(admin.id)}
                        className="mr-3 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {admin.display_name || admin.email}
                        </div>
                        {admin.display_name && (
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {admin.email}
                          </div>
                        )}
                      </div>
                    </label>
                  ))}
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowCreateRoom(false);
                  setNewRoomName('');
                  setSelectedAdmins([]);
                }}
                className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateRoom}
                disabled={!newRoomName.trim() || selectedAdmins.length === 0 || loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Create Room
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}

      {/* Chat Interface */}
      <div className="h-[600px]">
        <Chat className="h-full" />
      </div>

      {/* Admin Room Stats */}
      {adminRooms.length > 0 && (
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
            Admin Chat Statistics
          </h4>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-600 dark:text-gray-400">Total Rooms:</span>
              <span className="ml-2 font-medium text-gray-900 dark:text-white">
                {adminRooms.length}
              </span>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">Active Admins:</span>
              <span className="ml-2 font-medium text-gray-900 dark:text-white">
                {admins.length}
              </span>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">Your Rooms:</span>
              <span className="ml-2 font-medium text-gray-900 dark:text-white">
                {adminRooms.filter(room => 
                  room.participants?.some(p => p.user_id === user?.id)
                ).length}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
