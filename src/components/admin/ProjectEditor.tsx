'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { db, Project } from '@/lib/supabase';
import { useAuth } from '@/lib/auth/AuthContext';

interface ProjectEditorProps {
  project: Project | null;
  onSave: (project: Project) => void;
  onCancel: () => void;
}

interface FormData {
  title: string;
  slug: string;
  description: string;
  technologies: string[];
  gradient: string;
  logo_url: string;
  images: string[];
  android_link: string;
  windows_link: string;
  link: string;
  youtube_video_id: string;
  demo_video_url: string;
  status: 'completed' | 'in-progress' | 'upcoming';
  year: string;
  featured_image_url: string;
  project_details: {
    overview: string;
    features: string[];
    architecture: string;
    deployment: string;
  };
  tech_details: string;
  challenges: string;
  solutions: string;
  results: string;
  team_members: string[];
  project_duration: string;
  client_name: string;
  project_url: string;
  sort_order: number;
  is_active: boolean;
}

const initialFormData: FormData = {
  title: '',
  slug: '',
  description: '',
  technologies: [],
  gradient: 'from-blue-500 to-indigo-600',
  logo_url: '',
  images: [],
  android_link: '',
  windows_link: '',
  link: '',
  youtube_video_id: '',
  demo_video_url: '',
  status: 'upcoming',
  year: new Date().getFullYear().toString(),
  featured_image_url: '',
  project_details: {
    overview: '',
    features: [],
    architecture: '',
    deployment: ''
  },
  tech_details: '',
  challenges: '',
  solutions: '',
  results: '',
  team_members: [],
  project_duration: '',
  client_name: '',
  project_url: '',
  sort_order: 0,
  is_active: true
};

const gradientOptions = [
  { value: 'from-blue-500 to-indigo-600', label: 'Blue to Indigo', preview: 'bg-gradient-to-br from-blue-500 to-indigo-600' },
  { value: 'from-purple-500 to-indigo-600', label: 'Purple to Indigo', preview: 'bg-gradient-to-br from-purple-500 to-indigo-600' },
  { value: 'from-blue-500 to-teal-400', label: 'Blue to Teal', preview: 'bg-gradient-to-br from-blue-500 to-teal-400' },
  { value: 'from-orange-400 to-pink-500', label: 'Orange to Pink', preview: 'bg-gradient-to-br from-orange-400 to-pink-500' },
  { value: 'from-blue-600 to-violet-500', label: 'Blue to Violet', preview: 'bg-gradient-to-br from-blue-600 to-violet-500' },
  { value: 'from-green-400 to-blue-500', label: 'Green to Blue', preview: 'bg-gradient-to-br from-green-400 to-blue-500' },
  { value: 'from-yellow-400 to-orange-500', label: 'Yellow to Orange', preview: 'bg-gradient-to-br from-yellow-400 to-orange-500' },
  { value: 'from-red-400 to-pink-500', label: 'Red to Pink', preview: 'bg-gradient-to-br from-red-400 to-pink-500' }
];

const commonTechnologies = [
  'React', 'Next.js', 'Vue.js', 'Angular', 'JavaScript', 'TypeScript',
  'Node.js', 'Python', 'Java', 'C#', 'PHP', 'Ruby',
  'HTML/CSS', 'Tailwind CSS', 'Bootstrap', 'SASS/SCSS',
  'MongoDB', 'PostgreSQL', 'MySQL', 'Firebase', 'Supabase',
  'AWS', 'Google Cloud', 'Azure', 'Docker', 'Kubernetes',
  'Git', 'GitHub', 'GitLab', 'CI/CD', 'Testing',
  'Figma', 'Adobe Creative Suite', 'Sketch', 'Prototyping',
  'Flutter', 'Dart', 'React Native', 'iOS', 'Android',
  'AI/ML', 'TensorFlow', 'PyTorch', 'OpenAI', 'Machine Learning',
  'Vercel', 'Netlify', 'Heroku', 'DigitalOcean'
];

export default function ProjectEditor({ project, onSave, onCancel }: ProjectEditorProps) {
  const { user } = useAuth();
  const [formData, setFormData] = useState<FormData>(initialFormData);
  const [saving, setSaving] = useState(false);
  const [newTech, setNewTech] = useState('');
  const [newFeature, setNewFeature] = useState('');
  const [newTeamMember, setNewTeamMember] = useState('');

  useEffect(() => {
    if (project) {
      setFormData({
        title: project.title,
        slug: project.slug,
        description: project.description,
        technologies: project.technologies || [],
        gradient: project.gradient,
        logo_url: project.logo_url || '',
        images: project.images || [],
        android_link: project.android_link || '',
        windows_link: project.windows_link || '',
        link: project.link || '',
        youtube_video_id: project.youtube_video_id || '',
        demo_video_url: project.demo_video_url || '',
        status: project.status,
        year: project.year,
        featured_image_url: project.featured_image_url || '',
        project_details: {
          overview: project.project_details?.overview || '',
          features: project.project_details?.features || [],
          architecture: project.project_details?.architecture || '',
          deployment: project.project_details?.deployment || ''
        },
        tech_details: project.tech_details || '',
        challenges: project.challenges || '',
        solutions: project.solutions || '',
        results: project.results || '',
        team_members: project.team_members || [],
        project_duration: project.project_duration || '',
        client_name: project.client_name || '',
        project_url: project.project_url || '',
        sort_order: project.sort_order,
        is_active: project.is_active
      });
    } else {
      setFormData(initialFormData);
    }
  }, [project]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);

    try {
      const projectData = {
        ...formData,
        created_by: user?.id
      };

      let result;
      if (project) {
        result = await db.updateProject(project.id, projectData);
      } else {
        result = await db.createProject(projectData);
      }

      if (result) {
        onSave(result);
      } else {
        alert(`Failed to ${project ? 'update' : 'create'} project`);
      }
    } catch (error) {
      console.error('Error saving project:', error);
      alert(`Error ${project ? 'updating' : 'creating'} project`);
    } finally {
      setSaving(false);
    }
  };

  const handleAddTechnology = () => {
    if (newTech.trim() && !formData.technologies.includes(newTech.trim())) {
      setFormData(prev => ({
        ...prev,
        technologies: [...prev.technologies, newTech.trim()]
      }));
      setNewTech('');
    }
  };

  const handleRemoveTechnology = (index: number) => {
    setFormData(prev => ({
      ...prev,
      technologies: prev.technologies.filter((_, i) => i !== index)
    }));
  };

  const handleQuickAddTech = (tech: string) => {
    if (!formData.technologies.includes(tech)) {
      setFormData(prev => ({
        ...prev,
        technologies: [...prev.technologies, tech]
      }));
    }
  };

  const handleImageUpload = async (file: File, type: 'logo' | 'featured' | 'gallery') => {
    if (!file) return;

    try {
      const imageUrl = await db.uploadProjectImage(file, project?.id);
      if (imageUrl) {
        if (type === 'logo') {
          setFormData(prev => ({ ...prev, logo_url: imageUrl }));
        } else if (type === 'featured') {
          setFormData(prev => ({ ...prev, featured_image_url: imageUrl }));
        } else if (type === 'gallery') {
          setFormData(prev => ({ 
            ...prev, 
            images: [...prev.images, imageUrl]
          }));
        }
      } else {
        alert('Failed to upload image');
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      alert('Error uploading image');
    }
  };

  const handleRemoveImage = async (imageUrl: string, type: 'logo' | 'featured' | 'gallery', index?: number) => {
    try {
      await db.deleteProjectImage(imageUrl);
      
      if (type === 'logo') {
        setFormData(prev => ({ ...prev, logo_url: '' }));
      } else if (type === 'featured') {
        setFormData(prev => ({ ...prev, featured_image_url: '' }));
      } else if (type === 'gallery' && index !== undefined) {
        setFormData(prev => ({ 
          ...prev, 
          images: prev.images.filter((_, i) => i !== index)
        }));
      }
    } catch (error) {
      console.error('Error removing image:', error);
      alert('Error removing image');
    }
  };

  const extractYouTubeId = (url: string): string => {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : url;
  };

  const handleAddFeature = () => {
    if (newFeature.trim()) {
      setFormData(prev => ({
        ...prev,
        project_details: {
          ...prev.project_details,
          features: [...prev.project_details.features, newFeature.trim()]
        }
      }));
      setNewFeature('');
    }
  };

  const handleRemoveFeature = (index: number) => {
    setFormData(prev => ({
      ...prev,
      project_details: {
        ...prev.project_details,
        features: prev.project_details.features.filter((_, i) => i !== index)
      }
    }));
  };

  const handleAddTeamMember = () => {
    if (newTeamMember.trim() && !formData.team_members.includes(newTeamMember.trim())) {
      setFormData(prev => ({
        ...prev,
        team_members: [...prev.team_members, newTeamMember.trim()]
      }));
      setNewTeamMember('');
    }
  };

  const handleRemoveTeamMember = (index: number) => {
    setFormData(prev => ({
      ...prev,
      team_members: prev.team_members.filter((_, i) => i !== index)
    }));
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="max-w-4xl mx-auto"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
            {project ? 'Edit Project' : 'Create New Project'}
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Title */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Project Title *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                required
              />
            </div>

            {/* Year */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Year *
              </label>
              <input
                type="text"
                value={formData.year}
                onChange={(e) => setFormData(prev => ({ ...prev, year: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                required
              />
            </div>

            {/* Status */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Status *
              </label>
              <select
                value={formData.status}
                onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as FormData['status'] }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                required
              >
                <option value="upcoming">Upcoming</option>
                <option value="in-progress">In Progress</option>
                <option value="completed">Completed</option>
              </select>
            </div>

            {/* Sort Order */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Sort Order
              </label>
              <input
                type="number"
                value={formData.sort_order}
                onChange={(e) => setFormData(prev => ({ ...prev, sort_order: parseInt(e.target.value) || 0 }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
          </div>

          {/* Description */}
          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Description *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              required
            />
          </div>

          {/* Gradient Selection */}
          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Gradient Background
            </label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {gradientOptions.map((option) => (
                <button
                  key={option.value}
                  type="button"
                  onClick={() => setFormData(prev => ({ ...prev, gradient: option.value }))}
                  className={`relative h-16 rounded-lg ${option.preview} border-2 transition-all ${
                    formData.gradient === option.value
                      ? 'border-blue-500 ring-2 ring-blue-200'
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                >
                  <span className="absolute inset-0 flex items-center justify-center text-white text-xs font-medium bg-black bg-opacity-20">
                    {option.label}
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Technologies */}
          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Technologies
            </label>
            
            {/* Current Technologies */}
            <div className="flex flex-wrap gap-2 mb-3">
              {formData.technologies.map((tech, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-sm rounded-lg"
                >
                  {tech}
                  <button
                    type="button"
                    onClick={() => handleRemoveTechnology(index)}
                    className="ml-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>

            {/* Add Technology */}
            <div className="flex gap-2 mb-3">
              <input
                type="text"
                value={newTech}
                onChange={(e) => setNewTech(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTechnology())}
                placeholder="Add technology"
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
              <button
                type="button"
                onClick={handleAddTechnology}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Add
              </button>
            </div>

            {/* Quick Add Technologies */}
            <div className="space-y-2">
              <p className="text-sm text-gray-600 dark:text-gray-400">Quick add:</p>
              <div className="flex flex-wrap gap-2">
                {commonTechnologies.filter(tech => !formData.technologies.includes(tech)).slice(0, 10).map((tech) => (
                  <button
                    key={tech}
                    type="button"
                    onClick={() => handleQuickAddTech(tech)}
                    className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                  >
                    + {tech}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Links */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Android Link
              </label>
              <input
                type="url"
                value={formData.android_link}
                onChange={(e) => setFormData(prev => ({ ...prev, android_link: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Windows Link
              </label>
              <input
                type="url"
                value={formData.windows_link}
                onChange={(e) => setFormData(prev => ({ ...prev, windows_link: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Website Link
              </label>
              <input
                type="url"
                value={formData.link}
                onChange={(e) => setFormData(prev => ({ ...prev, link: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
          </div>

          {/* Media Upload Section */}
          <div className="mt-8 border-t border-gray-200 dark:border-gray-700 pt-8">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
              Media & Images
            </h4>

            {/* Logo Upload */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Project Logo
              </label>
              {formData.logo_url ? (
                <div className="flex items-center gap-4">
                  <Image
                    src={formData.logo_url}
                    alt="Project logo"
                    width={80}
                    height={80}
                    className="w-20 h-20 object-cover rounded-lg border"
                  />
                  <button
                    type="button"
                    onClick={() => handleRemoveImage(formData.logo_url, 'logo')}
                    className="px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                  >
                    Remove Logo
                  </button>
                </div>
              ) : (
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => e.target.files?.[0] && handleImageUpload(e.target.files[0], 'logo')}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              )}
            </div>

            {/* Featured Image Upload */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Featured Image
              </label>
              {formData.featured_image_url ? (
                <div className="flex items-center gap-4">
                  <Image
                    src={formData.featured_image_url}
                    alt="Featured image"
                    width={128}
                    height={80}
                    className="w-32 h-20 object-cover rounded-lg border"
                  />
                  <button
                    type="button"
                    onClick={() => handleRemoveImage(formData.featured_image_url, 'featured')}
                    className="px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                  >
                    Remove Featured Image
                  </button>
                </div>
              ) : (
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => e.target.files?.[0] && handleImageUpload(e.target.files[0], 'featured')}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              )}
            </div>

            {/* Gallery Images */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Project Gallery
              </label>
              
              {/* Current Images */}
              {formData.images.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  {formData.images.map((image, index) => (
                    <div key={index} className="relative group">
                      <Image
                        src={image}
                        alt={`Gallery image ${index + 1}`}
                        width={200}
                        height={96}
                        className="w-full h-24 object-cover rounded-lg border"
                      />
                      <button
                        type="button"
                        onClick={() => handleRemoveImage(image, 'gallery', index)}
                        className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
              )}

              {/* Add New Image */}
              <input
                type="file"
                accept="image/*"
                onChange={(e) => e.target.files?.[0] && handleImageUpload(e.target.files[0], 'gallery')}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Add multiple images to create a project gallery
              </p>
            </div>

            {/* YouTube Video */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                YouTube Video URL or ID
              </label>
              <input
                type="text"
                value={formData.youtube_video_id}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  youtube_video_id: extractYouTubeId(e.target.value)
                }))}
                placeholder="https://www.youtube.com/watch?v=dQw4w9WgXcQ or dQw4w9WgXcQ"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
              {formData.youtube_video_id && (
                <div className="mt-2">
                  <p className="text-sm text-green-600 dark:text-green-400">
                    ✓ Video ID: {formData.youtube_video_id}
                  </p>
                </div>
              )}
            </div>

            {/* Demo Video URL */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Demo Video URL (Direct link)
              </label>
              <input
                type="url"
                value={formData.demo_video_url}
                onChange={(e) => setFormData(prev => ({ ...prev, demo_video_url: e.target.value }))}
                placeholder="https://example.com/demo-video.mp4"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
          </div>

          {/* Project Details Section */}
          <div className="mt-8 border-t border-gray-200 dark:border-gray-700 pt-8">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
              Project Details
            </h4>

            {/* Project Information Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Client Name
                </label>
                <input
                  type="text"
                  value={formData.client_name}
                  onChange={(e) => setFormData(prev => ({ ...prev, client_name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Project Duration
                </label>
                <input
                  type="text"
                  value={formData.project_duration}
                  onChange={(e) => setFormData(prev => ({ ...prev, project_duration: e.target.value }))}
                  placeholder="e.g., 3 months, 6 weeks"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Project Demo URL
                </label>
                <input
                  type="url"
                  value={formData.project_url}
                  onChange={(e) => setFormData(prev => ({ ...prev, project_url: e.target.value }))}
                  placeholder="https://demo.example.com"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>
            </div>

            {/* Project Overview */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Project Overview
              </label>
              <textarea
                value={formData.project_details.overview}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  project_details: { ...prev.project_details, overview: e.target.value }
                }))}
                rows={4}
                placeholder="Detailed overview of the project..."
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>

            {/* Key Features */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Key Features
              </label>
              
              {/* Current Features */}
              <div className="space-y-2 mb-3">
                {formData.project_details.features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <span className="flex-1 px-3 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg text-gray-900 dark:text-white">
                      {feature}
                    </span>
                    <button
                      type="button"
                      onClick={() => handleRemoveFeature(index)}
                      className="px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                    >
                      Remove
                    </button>
                  </div>
                ))}
              </div>

              {/* Add New Feature */}
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newFeature}
                  onChange={(e) => setNewFeature(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddFeature())}
                  placeholder="Add a key feature"
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
                <button
                  type="button"
                  onClick={handleAddFeature}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Add
                </button>
              </div>
            </div>

            {/* Team Members */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Team Members
              </label>
              
              {/* Current Team Members */}
              <div className="space-y-2 mb-3">
                {formData.team_members.map((member, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <span className="flex-1 px-3 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg text-gray-900 dark:text-white">
                      {member}
                    </span>
                    <button
                      type="button"
                      onClick={() => handleRemoveTeamMember(index)}
                      className="px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                    >
                      Remove
                    </button>
                  </div>
                ))}
              </div>

              {/* Add New Team Member */}
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newTeamMember}
                  onChange={(e) => setNewTeamMember(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTeamMember())}
                  placeholder="Add team member"
                  className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
                <button
                  type="button"
                  onClick={handleAddTeamMember}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Add
                </button>
              </div>
            </div>

            {/* Technical Details */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Technical Implementation Details
              </label>
              <textarea
                value={formData.tech_details}
                onChange={(e) => setFormData(prev => ({ ...prev, tech_details: e.target.value }))}
                rows={4}
                placeholder="Technical architecture, frameworks used, deployment details..."
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>

            {/* Challenges */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Challenges Faced
              </label>
              <textarea
                value={formData.challenges}
                onChange={(e) => setFormData(prev => ({ ...prev, challenges: e.target.value }))}
                rows={3}
                placeholder="What challenges did you face during development?"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>

            {/* Solutions */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Solutions & Approach
              </label>
              <textarea
                value={formData.solutions}
                onChange={(e) => setFormData(prev => ({ ...prev, solutions: e.target.value }))}
                rows={3}
                placeholder="How did you solve the challenges?"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>

            {/* Results */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Results & Impact
              </label>
              <textarea
                value={formData.results}
                onChange={(e) => setFormData(prev => ({ ...prev, results: e.target.value }))}
                rows={3}
                placeholder="What were the outcomes and impact of the project?"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>
          </div>

          {/* Settings */}
          <div className="mt-8 flex items-center">
            <input
              type="checkbox"
              id="is_active"
              checked={formData.is_active}
              onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900 dark:text-white">
              Active (visible on public page)
            </label>
          </div>

          {/* Actions */}
          <div className="mt-8 flex gap-4">
            <button
              type="submit"
              disabled={saving}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              {saving ? 'Saving...' : (project ? 'Update Project' : 'Create Project')}
            </button>
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      </form>
    </motion.div>
  );
} 