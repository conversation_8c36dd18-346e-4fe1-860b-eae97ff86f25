'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { db, Project } from '@/lib/supabase';
import { useAuth } from '@/lib/auth/AuthContext';
import ProjectList from './ProjectList';
import ProjectEditor from './ProjectEditor';

type TabType = 'list' | 'editor';

export default function ProjectManagement() {
  const { isAdmin } = useAuth();
  const [activeTab, setActiveTab] = useState<TabType>('list');
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (isAdmin) {
      loadProjects();
    }
  }, [isAdmin]);

  const loadProjects = async () => {
    try {
      setLoading(true);
      const projectsData = await db.getProjects(false); // Get all projects including inactive
      setProjects(projectsData);
    } catch (error) {
      console.error('Error loading projects:', error);
      setError('Failed to load projects');
    } finally {
      setLoading(false);
    }
  };

  const handleProjectSaved = async () => {
    await loadProjects();
    setActiveTab('list');
    setSelectedProject(null);
  };

  const handleProjectDeleted = async () => {
    await loadProjects();
  };

  const handleEditProject = (project: Project) => {
    setSelectedProject(project);
    setActiveTab('editor');
  };

  const handleNewProject = () => {
    setSelectedProject(null);
    setActiveTab('editor');
  };

  if (!isAdmin) {
    return (
      <div className="p-6 text-center">
        <p className="text-red-600 dark:text-red-400">
          You do not have permission to access project management.
        </p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 text-center">
        <p className="text-red-600 dark:text-red-400">{error}</p>
        <button
          onClick={loadProjects}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          Retry
        </button>
      </div>
    );
  }

  const tabs = [
    { id: 'list', name: 'Projects List', icon: '📋' },
    { id: 'editor', name: selectedProject ? 'Edit Project' : 'New Project', icon: selectedProject ? '✏️' : '➕' }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Project Management
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Manage your portfolio projects and showcase
          </p>
        </div>
        {activeTab === 'list' && (
          <button
            onClick={handleNewProject}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
          >
            <span>➕</span>
            Add Project
          </button>
        )}
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as TabType)}
              className={`
                whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors
                ${activeTab === tab.id
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }
              `}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <motion.div
        key={activeTab}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {activeTab === 'list' ? (
          <ProjectList
            projects={projects}
            onEdit={handleEditProject}
            onDelete={handleProjectDeleted}
            onRefresh={loadProjects}
          />
        ) : (
          <ProjectEditor
            project={selectedProject}
            onSave={handleProjectSaved}
            onCancel={() => {
              setActiveTab('list');
              setSelectedProject(null);
            }}
          />
        )}
      </motion.div>
    </div>
  );
} 