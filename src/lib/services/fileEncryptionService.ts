/**
 * File Encryption Service for HielTech Application
 * Provides secure encryption/decryption for uploaded files
 */

import { EncryptionService } from './encryptionService';

export interface EncryptedFileMetadata {
  originalName: string;
  encryptedName: string;
  mimeType: string;
  size: number;
  checksum: string;
  encryptionVersion: number;
  uploadedAt: string;
}

export class FileEncryptionService {
  private static readonly CHUNK_SIZE = 64 * 1024; // 64KB chunks
  
  /**
   * Encrypt a file before upload
   */
  static async encryptFile(
    file: File,
    keyName: string = 'file_encryption_key'
  ): Promise<{
    encryptedData: Uint8Array;
    metadata: EncryptedFileMetadata;
  }> {
    try {
      // Read file as array buffer
      const fileBuffer = await file.arrayBuffer();
      const fileData = new Uint8Array(fileBuffer);
      
      // Generate encrypted filename
      const encryptedName = await this.generateEncryptedFileName(file.name);
      
      // Encrypt file data
      const encryptedFileData = await this.encryptFileData(fileData, keyName);
      
      // Generate checksum for integrity verification
      const checksum = await this.generateChecksum(fileData);
      
      const metadata: EncryptedFileMetadata = {
        originalName: file.name,
        encryptedName,
        mimeType: file.type,
        size: file.size,
        checksum,
        encryptionVersion: 1,
        uploadedAt: new Date().toISOString(),
      };
      
      return {
        encryptedData: encryptedFileData,
        metadata,
      };
    } catch (error) {
      console.error('Error encrypting file:', error);
      throw new Error('Failed to encrypt file');
    }
  }
  
  /**
   * Decrypt a file after download
   */
  static async decryptFile(
    encryptedData: Uint8Array,
    metadata: EncryptedFileMetadata,
    keyName: string = 'file_encryption_key'
  ): Promise<{
    fileData: Uint8Array;
    verified: boolean;
  }> {
    try {
      // Decrypt file data
      const decryptedData = await this.decryptFileData(encryptedData, keyName);
      
      // Verify integrity
      const calculatedChecksum = await this.generateChecksum(decryptedData);
      const verified = calculatedChecksum === metadata.checksum;
      
      if (!verified) {
        console.warn('File integrity verification failed');
      }
      
      return {
        fileData: decryptedData,
        verified,
      };
    } catch (error) {
      console.error('Error decrypting file:', error);
      throw new Error('Failed to decrypt file');
    }
  }
  
  /**
   * Encrypt file data in chunks
   */
  private static async encryptFileData(
    data: Uint8Array,
    keyName: string
  ): Promise<Uint8Array> {
    // For simplicity, we'll encrypt the entire file as one chunk
    // In a production environment, you might want to encrypt in smaller chunks
    const dataString = btoa(String.fromCharCode(...data));
    const encryptedString = await EncryptionService.encrypt(dataString, keyName);
    
    // Convert back to Uint8Array
    return new Uint8Array(
      atob(encryptedString).split('').map(char => char.charCodeAt(0))
    );
  }
  
  /**
   * Decrypt file data
   */
  private static async decryptFileData(
    encryptedData: Uint8Array,
    keyName: string
  ): Promise<Uint8Array> {
    // Convert to string for decryption
    const encryptedString = btoa(String.fromCharCode(...encryptedData));
    const decryptedString = await EncryptionService.decrypt(encryptedString, keyName);
    
    // Convert back to Uint8Array
    return new Uint8Array(
      atob(decryptedString).split('').map(char => char.charCodeAt(0))
    );
  }
  
  /**
   * Generate encrypted filename
   */
  private static async generateEncryptedFileName(originalName: string): Promise<string> {
    const timestamp = Date.now();
    const random = crypto.getRandomValues(new Uint8Array(8));
    const randomString = Array.from(random)
      .map(byte => byte.toString(16).padStart(2, '0'))
      .join('');
    
    const extension = originalName.split('.').pop() || '';
    return `enc_${timestamp}_${randomString}.${extension}`;
  }
  
  /**
   * Generate SHA-256 checksum for file integrity
   */
  private static async generateChecksum(data: Uint8Array): Promise<string> {
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = new Uint8Array(hashBuffer);
    return btoa(String.fromCharCode(...hashArray));
  }
  
  /**
   * Create a downloadable blob from decrypted file data
   */
  static createDownloadableBlob(
    fileData: Uint8Array,
    mimeType: string
  ): Blob {
    return new Blob([fileData], { type: mimeType });
  }
  
  /**
   * Validate file before encryption
   */
  static validateFile(file: File): { valid: boolean; error?: string } {
    const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
    const ALLOWED_TYPES = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'text/plain',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ];
    
    if (file.size > MAX_FILE_SIZE) {
      return {
        valid: false,
        error: `File size must be less than ${MAX_FILE_SIZE / (1024 * 1024)}MB`,
      };
    }
    
    if (!ALLOWED_TYPES.includes(file.type)) {
      return {
        valid: false,
        error: 'File type not allowed',
      };
    }
    
    return { valid: true };
  }
}

/**
 * Utility functions for common file encryption tasks
 */
export const FileEncryptionUtils = {
  /**
   * Encrypt and upload avatar image
   */
  async encryptAvatar(file: File) {
    const validation = FileEncryptionService.validateFile(file);
    if (!validation.valid) {
      throw new Error(validation.error);
    }
    
    return await FileEncryptionService.encryptFile(file, 'file_encryption_key');
  },
  
  /**
   * Encrypt and upload resume file
   */
  async encryptResume(file: File) {
    const validation = FileEncryptionService.validateFile(file);
    if (!validation.valid) {
      throw new Error(validation.error);
    }
    
    return await FileEncryptionService.encryptFile(file, 'file_encryption_key');
  },
  
  /**
   * Encrypt and upload blog media
   */
  async encryptBlogMedia(file: File) {
    const validation = FileEncryptionService.validateFile(file);
    if (!validation.valid) {
      throw new Error(validation.error);
    }
    
    return await FileEncryptionService.encryptFile(file, 'file_encryption_key');
  },
}; 