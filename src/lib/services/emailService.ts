import { supabase } from '../supabase';
import type { TeamApplication } from '../supabase';

// Email templates
const EMAIL_TEMPLATES = {
  ACCOUNT_CREATION_PROMPT: {
    subject: 'Complete Your Team Application - Create Account',
    template: (data: { name: string; email: string; applicationId: string }) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
          <h1 style="color: white; margin: 0; font-size: 28px;">Welcome to Hiel Tech!</h1>
        </div>
        
        <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px;">
          <h2 style="color: #333; margin-top: 0;">Hi ${data.name},</h2>
          
          <p style="color: #666; line-height: 1.6; font-size: 16px;">
            Thank you for submitting your team application! We're excited about your interest in joining our mission.
          </p>
          
          <div style="background: #e3f2fd; border-left: 4px solid #2196f3; padding: 20px; margin: 20px 0;">
            <h3 style="color: #1976d2; margin-top: 0;">Next Step: Create Your Account</h3>
            <p style="color: #333; margin-bottom: 0;">
              To continue with the application process and enable direct communication with our team, 
              please create an account using this email address: <strong>${data.email}</strong>
            </p>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_SITE_URL}/login" 
               style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                      color: white; 
                      padding: 15px 30px; 
                      text-decoration: none; 
                      border-radius: 25px; 
                      font-weight: bold; 
                      display: inline-block;">
              Create Account Now
            </a>
          </div>
          
          <p style="color: #666; font-size: 14px; margin-top: 30px;">
            After creating your account, our admin team can enable chat functionality to discuss your application directly with you.
          </p>
          
          <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">
          
          <p style="color: #999; font-size: 12px; text-align: center;">
            This email was sent because you submitted a team application at Hiel Tech.<br>
            If you didn't submit this application, please ignore this email.
          </p>
        </div>
      </div>
    `
  },

  CHAT_ENABLED: {
    subject: 'Chat Enabled - Discuss Your Application',
    template: (data: { name: string; email: string; applicationRole: string }) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(135deg, #4caf50 0%, #45a049 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
          <h1 style="color: white; margin: 0; font-size: 28px;">💬 Chat Enabled!</h1>
        </div>
        
        <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px;">
          <h2 style="color: #333; margin-top: 0;">Hi ${data.name},</h2>
          
          <p style="color: #666; line-height: 1.6; font-size: 16px;">
            Great news! Our admin team has enabled chat functionality for your <strong>${data.applicationRole}</strong> application.
          </p>
          
          <div style="background: #e8f5e8; border-left: 4px solid #4caf50; padding: 20px; margin: 20px 0;">
            <h3 style="color: #2e7d32; margin-top: 0;">You can now:</h3>
            <ul style="color: #333; margin: 0; padding-left: 20px;">
              <li>Chat directly with our team members</li>
              <li>Ask questions about the role and organization</li>
              <li>Discuss your application and experience</li>
              <li>Get real-time responses from our admins</li>
            </ul>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_SITE_URL}/profile" 
               style="background: linear-gradient(135deg, #4caf50 0%, #45a049 100%); 
                      color: white; 
                      padding: 15px 30px; 
                      text-decoration: none; 
                      border-radius: 25px; 
                      font-weight: bold; 
                      display: inline-block;">
              Start Chatting
            </a>
          </div>
          
          <p style="color: #666; font-size: 14px;">
            You can access the chat from your profile page or use the chat widget that appears on the site when you're logged in.
          </p>
          
          <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">
          
          <p style="color: #999; font-size: 12px; text-align: center;">
            This email was sent because chat was enabled for your team application at Hiel Tech.
          </p>
        </div>
      </div>
    `
  },

  NEW_MESSAGE_NOTIFICATION: {
    subject: 'New Message in Your Application Chat',
    template: (data: { name: string; senderName: string; messagePreview: string; applicationRole: string }) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
          <h1 style="color: white; margin: 0; font-size: 28px;">💬 New Message</h1>
        </div>
        
        <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px;">
          <h2 style="color: #333; margin-top: 0;">Hi ${data.name},</h2>
          
          <p style="color: #666; line-height: 1.6; font-size: 16px;">
            You have a new message in your <strong>${data.applicationRole}</strong> application chat from <strong>${data.senderName}</strong>.
          </p>
          
          <div style="background: #e3f2fd; border-left: 4px solid #2196f3; padding: 20px; margin: 20px 0;">
            <h3 style="color: #1976d2; margin-top: 0;">Message Preview:</h3>
            <p style="color: #333; font-style: italic; margin: 0;">
              "${data.messagePreview}"
            </p>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_SITE_URL}/profile" 
               style="background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%); 
                      color: white; 
                      padding: 15px 30px; 
                      text-decoration: none; 
                      border-radius: 25px; 
                      font-weight: bold; 
                      display: inline-block;">
              View Message
            </a>
          </div>
          
          <p style="color: #666; font-size: 14px;">
            Click the button above to view the full message and continue the conversation.
          </p>
          
          <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;">
          
          <p style="color: #999; font-size: 12px; text-align: center;">
            You're receiving this because you have an active chat for your team application.<br>
            You can manage your notification preferences in your profile settings.
          </p>
        </div>
      </div>
    `
  }
};

// Email service interface
interface EmailData {
  to: string;
  subject: string;
  html: string;
  from?: string;
}

// Notification data interfaces
interface AccountCreationData {
  name: string;
  email: string;
  applicationId: string;
}

interface ChatEnabledData {
  name: string;
  email: string;
  applicationRole: string;
}

interface NewMessageData {
  recipientName: string;
  recipientEmail: string;
  senderName: string;
  messagePreview: string;
  applicationRole: string;
}

// Union type for notification data
type NotificationData = AccountCreationData | ChatEnabledData | NewMessageData;

// Notification interface
interface EmailNotification {
  type: 'account_creation' | 'chat_enabled' | 'new_message';
  data: NotificationData;
}

interface EmailServiceResponse {
  success: boolean;
  message: string;
}

// Send email function (using Supabase Edge Functions or external service)
async function sendEmail(emailData: EmailData): Promise<boolean> {
  try {
    // Option 1: Use Supabase Edge Function for sending emails
    const { error } = await supabase.functions.invoke('send-email', {
      body: emailData
    });

    if (error) {
      console.error('Error sending email via Supabase:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in sendEmail:', error);
    
    // Fallback: Log email for manual processing in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Email would be sent:', emailData);
      return true;
    }
    
    return false;
  }
}

// Email notification functions
export async function sendAccountCreationPrompt(
  name: string,
  email: string,
  applicationId: string
): Promise<EmailServiceResponse> {
  try {
    console.log('Sending account creation prompt email to:', email);
    
    // Call Supabase Edge Function for sending email
    const { error } = await supabase.functions.invoke('send-email', {
      body: {
        to: email,
        subject: 'Complete Your Team Application - Create Account',
        template: 'account-creation-prompt',
        data: {
          name,
          email,
          applicationId,
          createAccountUrl: `${window.location.origin}/login?signup=true&email=${encodeURIComponent(email)}`
        }
      }
    });

    if (error) {
      throw error;
    }

    console.log('Account creation prompt email sent successfully');
    return { success: true, message: 'Email sent successfully' };
  } catch (error) {
    console.error('Error sending account creation prompt email:', error);
    return { success: false, message: 'Failed to send email' };
  }
}

export async function sendApprovalNotification(
  application: TeamApplication
): Promise<EmailServiceResponse> {
  try {
    console.log('Sending approval notification email to:', application.email);
    
    // Call Supabase Edge Function for sending email
    const { error } = await supabase.functions.invoke('send-email', {
      body: {
        to: application.email,
        subject: `🎉 Congratulations! Your application has been approved`,
        template: 'application-approval',
        data: {
          name: application.name,
          email: application.email,
          desired_role: application.desired_role,
          experience_level: application.experience_level,
          dashboardUrl: `${window.location.origin}/profile`,
          chatUrl: `${window.location.origin}/profile?section=chat`
        }
      }
    });

    if (error) {
      throw error;
    }

    console.log('Approval notification email sent successfully');
    return { success: true, message: 'Email sent successfully' };
  } catch (error) {
    console.error('Error sending approval notification email:', error);
    return { success: false, message: 'Failed to send email' };
  }
}

export async function sendRejectionNotification(
  application: TeamApplication
): Promise<EmailServiceResponse> {
  try {
    console.log('Sending rejection notification email to:', application.email);
    
    // Call Supabase Edge Function for sending email
    const { error } = await supabase.functions.invoke('send-email', {
      body: {
        to: application.email,
        subject: 'Thank you for your application',
        template: 'application-rejection',
        data: {
          name: application.name,
          email: application.email,
          desired_role: application.desired_role,
          supportEmail: '<EMAIL>'
        }
      }
    });

    if (error) {
      throw error;
    }

    console.log('Rejection notification email sent successfully');
    return { success: true, message: 'Email sent successfully' };
  } catch (error) {
    console.error('Error sending rejection notification email:', error);
    return { success: false, message: 'Failed to send email' };
  }
}

export async function sendChatEnabledNotification(
  name: string, 
  email: string, 
  applicationRole: string
): Promise<boolean> {
  const template = EMAIL_TEMPLATES.CHAT_ENABLED;
  const html = template.template({ name, email, applicationRole });
  
  return sendEmail({
    to: email,
    subject: template.subject,
    html,
    from: '<EMAIL>'
  });
}

export async function sendNewMessageNotification(
  recipientName: string,
  recipientEmail: string,
  senderName: string,
  messagePreview: string,
  applicationRole: string
): Promise<boolean> {
  const template = EMAIL_TEMPLATES.NEW_MESSAGE_NOTIFICATION;
  const html = template.template({ 
    name: recipientName, 
    senderName, 
    messagePreview: messagePreview.substring(0, 100) + (messagePreview.length > 100 ? '...' : ''),
    applicationRole 
  });
  
  return sendEmail({
    to: recipientEmail,
    subject: template.subject,
    html,
    from: '<EMAIL>'
  });
}

// Type guard functions
function isAccountCreationData(data: unknown): data is AccountCreationData {
  return typeof data === 'object' &&
         data !== null &&
         typeof (data as Record<string, unknown>).name === 'string' &&
         typeof (data as Record<string, unknown>).email === 'string' &&
         typeof (data as Record<string, unknown>).applicationId === 'string';
}

function isChatEnabledData(data: unknown): data is ChatEnabledData {
  return typeof data === 'object' &&
         data !== null &&
         typeof (data as Record<string, unknown>).name === 'string' &&
         typeof (data as Record<string, unknown>).email === 'string' &&
         typeof (data as Record<string, unknown>).applicationRole === 'string';
}

function isNewMessageData(data: unknown): data is NewMessageData {
  return typeof data === 'object' &&
         data !== null &&
         typeof (data as Record<string, unknown>).recipientName === 'string' &&
         typeof (data as Record<string, unknown>).recipientEmail === 'string' &&
         typeof (data as Record<string, unknown>).senderName === 'string' &&
         typeof (data as Record<string, unknown>).messagePreview === 'string' &&
         typeof (data as Record<string, unknown>).applicationRole === 'string';
}

// Batch email notifications (backward compatible)
export async function sendBatchNotifications(
  notifications: Array<EmailNotification | {
    type: 'account_creation' | 'chat_enabled' | 'new_message';
    data: Record<string, unknown>;
  }>
): Promise<{ sent: number; failed: number }> {
  let sent = 0;
  let failed = 0;

  for (const notification of notifications) {
    try {
      let success = false;

      switch (notification.type) {
        case 'account_creation':
          if (isAccountCreationData(notification.data)) {
            const result = await sendAccountCreationPrompt(
              notification.data.name,
              notification.data.email,
              notification.data.applicationId
            );
            success = result.success;
          } else {
            // Handle legacy format with unknown types
            const data = notification.data as Record<string, unknown>;
            if (typeof data.name === 'string' &&
                typeof data.email === 'string' &&
                typeof data.applicationId === 'string') {
              const result = await sendAccountCreationPrompt(
                data.name,
                data.email,
                data.applicationId
              );
              success = result.success;
            } else {
              console.error('Invalid account creation data:', notification.data);
              failed++;
              continue;
            }
          }
          break;
        case 'chat_enabled':
          if (isChatEnabledData(notification.data)) {
            success = await sendChatEnabledNotification(
              notification.data.name,
              notification.data.email,
              notification.data.applicationRole
            );
          } else {
            // Handle legacy format with unknown types
            const data = notification.data as Record<string, unknown>;
            if (typeof data.name === 'string' &&
                typeof data.email === 'string' &&
                typeof data.applicationRole === 'string') {
              success = await sendChatEnabledNotification(
                data.name,
                data.email,
                data.applicationRole
              );
            } else {
              console.error('Invalid chat enabled data:', notification.data);
              failed++;
              continue;
            }
          }
          break;
        case 'new_message':
          if (isNewMessageData(notification.data)) {
            success = await sendNewMessageNotification(
              notification.data.recipientName,
              notification.data.recipientEmail,
              notification.data.senderName,
              notification.data.messagePreview,
              notification.data.applicationRole
            );
          } else {
            // Handle legacy format with unknown types
            const data = notification.data as Record<string, unknown>;
            if (typeof data.recipientName === 'string' &&
                typeof data.recipientEmail === 'string' &&
                typeof data.senderName === 'string' &&
                typeof data.messagePreview === 'string' &&
                typeof data.applicationRole === 'string') {
              success = await sendNewMessageNotification(
                data.recipientName,
                data.recipientEmail,
                data.senderName,
                data.messagePreview,
                data.applicationRole
              );
            } else {
              console.error('Invalid new message data:', notification.data);
              failed++;
              continue;
            }
          }
          break;
      }

      if (success) {
        sent++;
      } else {
        failed++;
      }
    } catch (error) {
      console.error('Error sending notification:', error);
      failed++;
    }
  }

  return { sent, failed };
}
