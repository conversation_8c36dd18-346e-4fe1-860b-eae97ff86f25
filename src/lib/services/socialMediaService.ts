// Social Media API Service for Instagram and Facebook Graph API integration

import { 
  InstagramBasicApiResponse,
  FacebookPagesApiResponse 
} from '@/lib/types/social-media';

const FACEBOOK_API_VERSION = 'v18.0';
const FACEBOOK_API_BASE = `https://graph.facebook.com/${FACEBOOK_API_VERSION}`;

export class SocialMediaService {
  
  // Instagram Basic Display API methods
  static async getInstagramProfile(accessToken: string): Promise<{
    id: string;
    username: string;
    account_type: string;
    media_count: number;
  }> {
    try {
      const response = await fetch(
        `${FACEBOOK_API_BASE}/me?fields=id,username,account_type,media_count&access_token=${accessToken}`
      );
      
      if (!response.ok) {
        throw new Error(`Instagram API error: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error fetching Instagram profile:', error);
      throw error;
    }
  }

  static async getInstagramMedia(accessToken: string, limit: number = 25): Promise<InstagramBasicApiResponse> {
    try {
      const response = await fetch(
        `${FACEBOOK_API_BASE}/me/media?fields=id,caption,media_type,media_url,thumbnail_url,permalink,timestamp&limit=${limit}&access_token=${accessToken}`
      );
      
      if (!response.ok) {
        throw new Error(`Instagram API error: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error fetching Instagram media:', error);
      throw error;
    }
  }

  // Facebook Pages API methods
  static async getFacebookPages(accessToken: string): Promise<FacebookPagesApiResponse> {
    try {
      const response = await fetch(
        `${FACEBOOK_API_BASE}/me/accounts?fields=id,name,access_token,category,instagram_business_account{id,username}&access_token=${accessToken}`
      );
      
      if (!response.ok) {
        throw new Error(`Facebook API error: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error fetching Facebook pages:', error);
      throw error;
    }
  }

  static async getFacebookPageInsights(pageId: string, accessToken: string, metric: string, period: string = 'day'): Promise<{
    data: Array<{
      name: string;
      period: string;
      values: Array<{ value: number; end_time: string }>;
    }>;
  }> {
    try {
      const response = await fetch(
        `${FACEBOOK_API_BASE}/${pageId}/insights?metric=${metric}&period=${period}&access_token=${accessToken}`
      );
      
      if (!response.ok) {
        throw new Error(`Facebook Insights API error: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error fetching Facebook page insights:', error);
      throw error;
    }
  }

  // Instagram Business API methods (requires Facebook Page)
  static async getInstagramBusinessProfile(instagramAccountId: string, accessToken: string): Promise<{
    id: string;
    username: string;
    account_type: string;
    followers_count: number;
    follows_count: number;
    media_count: number;
    profile_picture_url: string;
    biography: string;
  }> {
    try {
      const response = await fetch(
        `${FACEBOOK_API_BASE}/${instagramAccountId}?fields=id,username,account_type,followers_count,follows_count,media_count,profile_picture_url,biography&access_token=${accessToken}`
      );
      
      if (!response.ok) {
        throw new Error(`Instagram Business API error: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error fetching Instagram business profile:', error);
      throw error;
    }
  }

  static async getInstagramBusinessMedia(instagramAccountId: string, accessToken: string, limit: number = 25): Promise<{
    data: Array<{
      id: string;
      caption: string;
      media_type: string;
      media_url: string;
      thumbnail_url?: string;
      permalink: string;
      timestamp: string;
      like_count: number;
      comments_count: number;
    }>;
  }> {
    try {
      const response = await fetch(
        `${FACEBOOK_API_BASE}/${instagramAccountId}/media?fields=id,caption,media_type,media_url,thumbnail_url,permalink,timestamp,like_count,comments_count&limit=${limit}&access_token=${accessToken}`
      );
      
      if (!response.ok) {
        throw new Error(`Instagram Business API error: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error fetching Instagram business media:', error);
      throw error;
    }
  }

  // Content Publishing Methods
  static async createInstagramMediaObject(instagramAccountId: string, accessToken: string, mediaData: {
    image_url?: string;
    video_url?: string;
    caption?: string;
    media_type: 'IMAGE' | 'VIDEO' | 'CAROUSEL_ALBUM';
    children?: string[]; // For carousel
  }): Promise<{ id: string }> {
    try {
      const params = new URLSearchParams({
        access_token: accessToken,
        caption: mediaData.caption || '',
      });

      if (mediaData.media_type === 'IMAGE' && mediaData.image_url) {
        params.append('image_url', mediaData.image_url);
      } else if (mediaData.media_type === 'VIDEO' && mediaData.video_url) {
        params.append('video_url', mediaData.video_url);
      }

      if (mediaData.children && mediaData.media_type === 'CAROUSEL_ALBUM') {
        params.append('children', mediaData.children.join(','));
      }

      const response = await fetch(
        `${FACEBOOK_API_BASE}/${instagramAccountId}/media`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: params.toString(),
        }
      );
      
      if (!response.ok) {
        throw new Error(`Instagram Media Creation API error: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error creating Instagram media object:', error);
      throw error;
    }
  }

  static async publishInstagramMedia(instagramAccountId: string, accessToken: string, creationId: string): Promise<{ id: string }> {
    try {
      const params = new URLSearchParams({
        access_token: accessToken,
        creation_id: creationId,
      });

      const response = await fetch(
        `${FACEBOOK_API_BASE}/${instagramAccountId}/media_publish`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: params.toString(),
        }
      );
      
      if (!response.ok) {
        throw new Error(`Instagram Media Publish API error: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error publishing Instagram media:', error);
      throw error;
    }
  }

  static async createFacebookPost(pageId: string, accessToken: string, postData: {
    message?: string;
    link?: string;
    published?: boolean;
    scheduled_publish_time?: number;
  }): Promise<{ id: string; post_id?: string }> {
    try {
      const params = new URLSearchParams({
        access_token: accessToken,
      });

      if (postData.message) params.append('message', postData.message);
      if (postData.link) params.append('link', postData.link);
      if (postData.published !== undefined) params.append('published', postData.published.toString());
      if (postData.scheduled_publish_time) params.append('scheduled_publish_time', postData.scheduled_publish_time.toString());

      const response = await fetch(
        `${FACEBOOK_API_BASE}/${pageId}/feed`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: params.toString(),
        }
      );
      
      if (!response.ok) {
        throw new Error(`Facebook Post API error: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error creating Facebook post:', error);
      throw error;
    }
  }

  // Token Management
  static async refreshLongLivedToken(shortLivedToken: string, appId: string, appSecret: string): Promise<{
    access_token: string;
    token_type: string;
    expires_in?: number;
  }> {
    try {
      const response = await fetch(
        `${FACEBOOK_API_BASE}/oauth/access_token?grant_type=fb_exchange_token&client_id=${appId}&client_secret=${appSecret}&fb_exchange_token=${shortLivedToken}`
      );
      
      if (!response.ok) {
        throw new Error(`Token refresh error: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error refreshing token:', error);
      throw error;
    }
  }

  static async validateToken(accessToken: string): Promise<{
    id: string;
    name?: string;
    email?: string;
  }> {
    try {
      const response = await fetch(
        `${FACEBOOK_API_BASE}/me?access_token=${accessToken}`
      );
      
      if (!response.ok) {
        throw new Error(`Token validation error: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error validating token:', error);
      throw error;
    }
  }

  // Utility methods
  static getOptimalPostingTimes(platform: 'instagram' | 'facebook'): string[] {
    // These are general best practices - can be customized based on audience analytics
    if (platform === 'instagram') {
      return [
        '06:00', '08:00', '12:00', '17:00', '19:00', '21:00'
      ];
    } else if (platform === 'facebook') {
      return [
        '09:00', '13:00', '15:00', '20:00', '21:00'
      ];
    }
    return [];
  }

  static generateHashtagSuggestions(caption: string, platform: 'instagram' | 'facebook'): string[] {
    // Basic hashtag suggestion based on content
    // In a real implementation, this could use AI/ML for better suggestions
    // const words = caption.toLowerCase().split(/\s+/); // TODO: Use for intelligent suggestions
    const commonHashtags = {
      instagram: ['#instagood', '#photooftheday', '#love', '#beautiful', '#happy', '#fun'],
      facebook: ['#business', '#marketing', '#social', '#community', '#growth']
    };

    return commonHashtags[platform] || [];
  }

  static validateMediaFile(file: File, platform: 'instagram' | 'facebook'): { valid: boolean; error?: string } {
    const maxSizes = {
      instagram: {
        image: 8 * 1024 * 1024, // 8MB
        video: 100 * 1024 * 1024, // 100MB
      },
      facebook: {
        image: 4 * 1024 * 1024, // 4MB
        video: 1024 * 1024 * 1024, // 1GB
      }
    };

    const allowedTypes = {
      instagram: {
        image: ['image/jpeg', 'image/png'],
        video: ['video/mp4', 'video/mov'],
      },
      facebook: {
        image: ['image/jpeg', 'image/png', 'image/gif'],
        video: ['video/mp4', 'video/mov', 'video/avi'],
      }
    };

    const isImage = file.type.startsWith('image/');
    const fileType = isImage ? 'image' : 'video';

    // Check file type
    if (!allowedTypes[platform][fileType].includes(file.type)) {
      return {
        valid: false,
        error: `File type ${file.type} is not supported for ${platform}`
      };
    }

    // Check file size
    if (file.size > maxSizes[platform][fileType]) {
      return {
        valid: false,
        error: `File size exceeds the limit for ${platform} ${fileType}s`
      };
    }

    return { valid: true };
  }
} 