/**
 * Encryption Service for HielTech Application
 * Provides secure encryption/decryption for sensitive user data
 */

import { supabase } from '@/lib/supabase';

export interface EncryptionConfig {
  algorithm: 'AES-GCM';
  keyLength: 256;
  ivLength: 12;
}

export interface EncryptedData {
  data: string;
  iv: string;
  version: number;
}

export class EncryptionService {
  private static readonly config: EncryptionConfig = {
    algorithm: 'AES-GCM',
    keyLength: 256,
    ivLength: 12,
  };

  private static keyCache = new Map<string, CryptoKey>();

  /**
   * Get encryption key from Supabase Vault
   */
  private static async getEncryptionKey(keyName: string): Promise<CryptoKey> {
    // Check cache first
    if (this.keyCache.has(keyName)) {
      return this.keyCache.get(keyName)!;
    }

    try {
      // Call the database function to get the key
      const { data, error } = await supabase.rpc('get_encryption_key', {
        key_name: keyName
      });

      if (error) {
        throw new Error(`Failed to retrieve encryption key: ${error.message}`);
      }

      if (!data) {
        throw new Error(`Encryption key '${keyName}' not found`);
      }

      // Convert string key to CryptoKey
      const keyBuffer = new TextEncoder().encode(data);
      const key = await crypto.subtle.importKey(
        'raw',
        keyBuffer.slice(0, 32), // Use first 32 bytes for AES-256
        { name: this.config.algorithm },
        false,
        ['encrypt', 'decrypt']
      );

      // Cache the key for performance
      this.keyCache.set(keyName, key);
      return key;
    } catch (error) {
      console.error('Error retrieving encryption key:', error);
      throw new Error('Failed to retrieve encryption key');
    }
  }

  /**
   * Encrypt sensitive data
   */
  static async encrypt(data: string, keyName: string): Promise<string> {
    try {
      if (!data) return data;

      const key = await this.getEncryptionKey(keyName);
      const encoder = new TextEncoder();
      const dataBuffer = encoder.encode(data);

      // Generate random IV
      const iv = crypto.getRandomValues(new Uint8Array(this.config.ivLength));

      // Encrypt the data
      const encrypted = await crypto.subtle.encrypt(
        {
          name: this.config.algorithm,
          iv: iv,
        },
        key,
        dataBuffer
      );

      // Combine IV and encrypted data
      const combined = new Uint8Array(iv.length + encrypted.byteLength);
      combined.set(iv);
      combined.set(new Uint8Array(encrypted), iv.length);

      // Return as base64 string
      return btoa(String.fromCharCode(...combined));
    } catch (error) {
      console.error('Encryption error:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  /**
   * Decrypt sensitive data
   */
  static async decrypt(encryptedData: string, keyName: string): Promise<string> {
    try {
      if (!encryptedData) return encryptedData;

      const key = await this.getEncryptionKey(keyName);

      // Decode from base64
      const combined = new Uint8Array(
        atob(encryptedData).split('').map(char => char.charCodeAt(0))
      );

      // Extract IV and encrypted data
      const iv = combined.slice(0, this.config.ivLength);
      const encrypted = combined.slice(this.config.ivLength);

      // Decrypt the data
      const decrypted = await crypto.subtle.decrypt(
        {
          name: this.config.algorithm,
          iv: iv,
        },
        key,
        encrypted
      );

      const decoder = new TextDecoder();
      return decoder.decode(decrypted);
    } catch (error) {
      console.error('Decryption error:', error);
      throw new Error('Failed to decrypt data');
    }
  }

  /**
   * Encrypt object with specific fields
   */
  static async encryptFields<T extends Record<string, unknown>>(
    data: T,
    fieldsToEncrypt: (keyof T)[],
    keyName: string
  ): Promise<T & { encrypted_fields: Record<string, string> }> {
    const result = { ...data } as T & { encrypted_fields: Record<string, string> };
    result.encrypted_fields = {};

    for (const field of fieldsToEncrypt) {
      const value = data[field];
      if (value && typeof value === 'string') {
        result.encrypted_fields[field as string] = await this.encrypt(value, keyName);
        // Remove the original field or replace with placeholder
        delete result[field];
      }
    }

    return result;
  }

  /**
   * Decrypt object with encrypted fields
   */
  static async decryptFields<T extends Record<string, unknown>>(
    data: T & { encrypted_fields?: Record<string, string> },
    keyName: string
  ): Promise<T> {
    const result = { ...data } as T;

    if (data.encrypted_fields) {
      for (const [field, encryptedValue] of Object.entries(data.encrypted_fields)) {
        try {
          result[field as keyof T] = await this.decrypt(encryptedValue, keyName) as T[keyof T];
        } catch (error) {
          console.warn(`Failed to decrypt field ${field}:`, error);
          // Keep the field empty rather than exposing encrypted data
          result[field as keyof T] = '' as T[keyof T];
        }
      }
    }

    // Remove the encrypted_fields object from the result
    delete (result as Record<string, unknown>).encrypted_fields;
    return result;
  }

  /**
   * Hash sensitive data for indexing (one-way)
   */
  static async hashForIndex(data: string): Promise<string> {
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);
    const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
    const hashArray = new Uint8Array(hashBuffer);
    return btoa(String.fromCharCode(...hashArray));
  }

  /**
   * Securely wipe a string from memory (best effort)
   */
  static secureWipe(str: string): void {
    // Note: This is limited in JavaScript, but we can try
    if (typeof str === 'string') {
      // In a more secure environment, you'd use crypto.getRandomValues
      // to overwrite the memory, but JS doesn't give us direct memory access
      str = '';
    }
  }

  /**
   * Clear the key cache (call on logout)
   */
  static clearKeyCache(): void {
    this.keyCache.clear();
  }
}

/**
 * Utility functions for common encryption tasks
 */
export const EncryptionUtils = {
  /**
   * Encrypt profile data
   */
  async encryptProfile(profile: Record<string, unknown>) {
    return await EncryptionService.encryptFields(
      profile,
      ['email', 'display_name', 'bio', 'company', 'position'],
      'profile_encryption_key'
    );
  },

  /**
   * Decrypt profile data
   */
  async decryptProfile(encryptedProfile: Record<string, unknown>) {
    return await EncryptionService.decryptFields(encryptedProfile, 'profile_encryption_key');
  },

  /**
   * Encrypt inquiry data
   */
  async encryptInquiry(inquiry: Record<string, unknown>) {
    return await EncryptionService.encryptFields(
      inquiry,
      ['email', 'phone', 'message', 'company'],
      'profile_encryption_key'
    );
  },

  /**
   * Decrypt inquiry data
   */
  async decryptInquiry(encryptedInquiry: Record<string, unknown>) {
    return await EncryptionService.decryptFields(encryptedInquiry, 'profile_encryption_key');
  },

  /**
   * Encrypt team application data
   */
  async encryptTeamApplication(application: Record<string, unknown>) {
    return await EncryptionService.encryptFields(
      application,
      ['email', 'phone', 'motivation', 'additional_info'],
      'profile_encryption_key'
    );
  },

  /**
   * Decrypt team application data
   */
  async decryptTeamApplication(encryptedApplication: Record<string, unknown>) {
    return await EncryptionService.decryptFields(encryptedApplication, 'profile_encryption_key');
  },

  /**
   * Encrypt chat message
   */
  async encryptChatMessage(message: string) {
    return await EncryptionService.encrypt(message, 'chat_encryption_key');
  },

  /**
   * Decrypt chat message
   */
  async decryptChatMessage(encryptedMessage: string) {
    return await EncryptionService.decrypt(encryptedMessage, 'chat_encryption_key');
  },

  /**
   * Hash IP address for analytics
   */
  async hashIPAddress(ip: string) {
    return await EncryptionService.hashForIndex(ip);
  },
}; 