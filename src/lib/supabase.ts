import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false, // Disabled - we handle OAuth callbacks manually for better control
    storage: typeof window !== 'undefined' ? window.localStorage : undefined,
    storageKey: 'supabase.auth.token',
    flowType: 'pkce',
    // Enhanced OAuth configuration
    debug: process.env.NODE_ENV === 'development',
  },
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
  db: {
    schema: 'public',
  },
  global: {
    headers: {
      'X-Client-Info': 'hieltech-app'
    },
    fetch: (url, options = {}) => {
      return fetch(url, {
        ...options,
        // Reduced timeout for better performance
        signal: AbortSignal.timeout(10000), // 10 second timeout instead of 45s
      });
    }
  }
});

// Optimized session getter with faster timeout and better error handling
export const getSessionSafe = async () => {
  try {
    // Reduced retry attempts and faster timeout
    let lastError;
    for (let attempt = 1; attempt <= 2; attempt++) { // Reduced from 3 to 2 attempts
      try {
        const result = await Promise.race([
          supabase.auth.getSession(),
          new Promise<never>((_, reject) => 
            setTimeout(() => reject(new Error('Session timeout')), 5000) // Reduced from no timeout to 5s
          )
        ]);
        
        if (result.data || !result.error) {
          return result;
        }
        lastError = result.error;
        
        // Special handling for refresh token errors
        if (result.error?.message?.includes('Invalid Refresh Token')) {
          console.warn('Refresh token invalid, clearing local storage...');
          
          // Clear potentially stale tokens
          if (typeof window !== 'undefined') {
            Object.keys(localStorage).forEach(key => {
              if (key.startsWith('supabase.auth.')) {
                localStorage.removeItem(key);
              }
            });
          }
          
          // Don't retry for refresh token errors - let auth flow restart
          return { data: { session: null }, error: result.error };
        }
        
      } catch (error) {
        lastError = error;
        console.warn(`Session retrieval attempt ${attempt} failed:`, error);
        
        // Wait before retrying, reduced wait time
        if (attempt < 2) {
          await new Promise(resolve => setTimeout(resolve, 500 * attempt)); // Reduced from 1000ms
        }
      }
    }
    
    console.error('All session retrieval attempts failed:', lastError);
    return { data: { session: null }, error: lastError };
  } catch (error) {
    console.error('Error getting session:', error);
    return { data: { session: null }, error };
  }
};

// Add visibility change handling to prevent unnecessary auth refreshes
if (typeof window !== 'undefined') {
  let isVisible = !document.hidden;

  const handleVisibilityChange = () => {
    const currentlyVisible = !document.hidden;

    // Only handle if transitioning from hidden to visible
    if (!isVisible && currentlyVisible) {
      console.log('Page became visible - checking auth state gracefully');
      // Debounce auth checks to prevent rapid fire requests
      setTimeout(() => {
        supabase.auth.getSession().catch(console.error);
      }, 1000);
    }

    isVisible = currentlyVisible;
  };

  document.addEventListener('visibilitychange', handleVisibilityChange);
}

// Database types
export interface Profile {
  id: string;
  email: string;
  display_name?: string;
  bio?: string;
  company?: string;
  position?: string;
  role: 'user' | 'admin';
  avatar_url?: string;  // Added for Google OAuth profile pictures
  created_at: string;
  updated_at: string;
}

export interface Task {
  id: string;
  title: string;
  description?: string;
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high';
  due_date?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface Inquiry {
  id: string;
  type: 'contact' | 'service';
  name: string;
  email: string;
  phone?: string;
  company?: string;
  subject?: string;
  message: string;
  service_type?: string;
  budget_range?: string;
  timeline?: string;
  status: 'new' | 'read' | 'responded' | 'closed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  admin_notes?: string;
  created_at: string;
  updated_at: string;
  read_at?: string;
  responded_at?: string;
}

// Blog Management Types
export interface BlogCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  color: string;
  icon?: string;
  sort_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface BlogTag {
  id: string;
  name: string;
  slug: string;
  description?: string;
  color: string;
  usage_count: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface BlogMedia {
  id: string;
  filename: string;
  original_name: string;
  file_path: string;
  file_size: number;
  mime_type: string;
  width?: number;
  height?: number;
  alt_text?: string;
  caption?: string;
  uploaded_by?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt?: string;
  content: string;
  featured_image_id?: string;
  featured_image?: BlogMedia;
  category_id?: string;
  category?: BlogCategory;
  author_id?: string;
  author?: Profile;
  status: 'draft' | 'published' | 'scheduled' | 'archived';
  visibility: 'public' | 'private' | 'password';
  password?: string;
  published_at?: string;
  scheduled_at?: string;
  meta_title?: string;
  meta_description?: string;
  meta_keywords?: string[];
  og_title?: string;
  og_description?: string;
  og_image_id?: string;
  og_image?: BlogMedia;
  reading_time: number;
  view_count: number;
  like_count: number;
  comment_count: number;
  is_featured: boolean;
  allow_comments: boolean;
  template: string;
  custom_css?: string;
  custom_js?: string;
  external_links?: {
    github?: string;
    demo?: string;
    playstore?: string;
    appstore?: string;
    youtube?: string;
    website?: string;
    [key: string]: string | undefined;
  };
  tags?: BlogTag[];
  created_at: string;
  updated_at: string;
}

export interface BlogPostTag {
  id: string;
  post_id: string;
  tag_id: string;
  created_at: string;
}

export interface BlogPostAnalytics {
  id: string;
  post_id: string;
  date: string;
  views: number;
  unique_views: number;
  likes: number;
  shares: number;
  comments: number;
  bounce_rate?: number;
  avg_time_on_page?: number;
  referrer_data?: Record<string, unknown>;
  device_data?: Record<string, unknown>;
  location_data?: Record<string, unknown>;
  created_at: string;
  updated_at: string;
}

export interface TeamMember {
  id: string;
  name: string;
  role: string;
  bio?: string;
  email?: string;
  phone?: string;
  avatar_url?: string;
  linkedin_url?: string;
  github_url?: string;
  twitter_url?: string;
  website_url?: string;
  skills?: string[];
  is_active: boolean;
  is_featured: boolean;
  display_order: number;
  joined_date: string;
  created_at: string;
  updated_at: string;
  created_by?: string;
}

export interface TeamApplication {
  id: string;
  name: string;
  email: string;
  phone?: string;
  desired_role: string;
  experience_level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  motivation: string;
  skills?: string[];
  portfolio_url?: string;
  linkedin_url?: string;
  github_url?: string;
  resume_url?: string;
  availability: 'part-time' | 'full-time' | 'volunteer' | 'flexible';
  location?: string;
  timezone?: string;
  previous_nonprofit_experience?: string;
  additional_info?: string;
  status: 'pending' | 'reviewing' | 'approved' | 'rejected' | 'withdrawn';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  admin_notes?: string;
  reviewed_by?: string;
  reviewed_at?: string;
  account_created: boolean;
  account_created_at?: string;
  user_id?: string;
  created_at: string;
  updated_at: string;
}

// Chat System Types
export interface ChatPermission {
  id: string;
  user_id: string;
  application_id: string | null;
  is_enabled: boolean;
  enabled_by?: string | null;
  enabled_at?: string | null;
  disabled_at?: string | null;
  created_at: string;
  updated_at: string;
  chat_type?: 'application' | 'general' | 'inquiry';
}

export interface ChatRoom {
  id: string;
  type: 'applicant_admin' | 'admin_only';
  name?: string;
  application_id?: string | null;
  created_by?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  participants?: ChatRoomParticipant[];
  last_message?: ChatMessage;
  unread_count?: number;
}

export interface ChatMessage {
  id: string;
  room_id: string;
  sender_id: string;
  sender?: Profile;
  message: string;
  message_type: 'text' | 'file' | 'system';
  file_url?: string;
  is_edited: boolean;
  edited_at?: string;
  created_at: string;
}

export interface ChatRoomParticipant {
  id: string;
  room_id: string;
  user_id: string;
  user?: Profile;
  joined_at: string;
  last_read_at: string;
}

export interface BlogComment {
  id: string;
  post_id: string;
  parent_id?: string;
  author_name: string;
  author_email: string;
  author_website?: string;
  content: string;
  status: 'pending' | 'approved' | 'spam' | 'trash';
  ip_address?: string;
  user_agent?: string;
  created_at: string;
  updated_at: string;
}

// HielLinks Service Types
export interface HielProfile {
  id: string;
  user_id: string;
  username: string;
  business_name: string;
  description?: string;
  logo_url?: string;
  background_image_url?: string;
  theme_color: string;
  text_color: string;
  location?: string;
  latitude?: number;
  longitude?: number;
  phone?: string;
  email?: string;
  website?: string;
  is_active: boolean;
  is_featured: boolean;
  view_count: number;
  click_count: number;
  status: 'draft' | 'published' | 'suspended';
  created_at: string;
  updated_at: string;
  user?: Profile;
  links?: HielLink[];
}

export interface HielLink {
  id: string;
  profile_id: string;
  title: string;
  url: string;
  type: 'website' | 'social' | 'contact' | 'custom';
  platform?: 'instagram' | 'facebook' | 'twitter' | 'linkedin' | 'youtube' | 'tiktok' | 'whatsapp' | 'telegram' | 'github' | 'other';
  icon?: string;
  description?: string;
  is_active: boolean;
  click_count: number;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface HielAnalytics {
  id: string;
  profile_id: string;
  link_id?: string;
  event_type: 'profile_view' | 'link_click';
  visitor_ip?: string;
  user_agent?: string;
  referrer?: string;
  country?: string;
  city?: string;
  device_type?: string;
  browser?: string;
  created_at: string;
}

export interface HielSettings {
  id: string;
  user_id: string;
  max_profiles: number;
  max_links_per_profile: number;
  max_storage_mb: number;
  can_use_custom_domain: boolean;
  can_use_analytics: boolean;
  can_remove_branding: boolean;
  subscription_type: 'free' | 'premium' | 'enterprise';
  subscription_expires_at?: string;
  created_at: string;
  updated_at: string;
}

export interface Project {
  id: string;
  title: string;
  slug: string;
  description: string;
  technologies: string[];
  gradient: string;
  logo_url?: string;
  images: string[];
  android_link?: string;
  windows_link?: string;
  link?: string;
  youtube_video_id?: string;
  demo_video_url?: string;
  status: 'completed' | 'in-progress' | 'upcoming';
  year: string;
  featured_image_url?: string;
  project_details?: {
    overview?: string;
    features?: string[];
    architecture?: string;
    deployment?: string;
  };
  tech_details?: string;
  challenges?: string;
  solutions?: string;
  results?: string;
  team_members?: string[];
  project_duration?: string;
  client_name?: string;
  project_url?: string;
  sort_order: number;
  is_active: boolean;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

// Blog Management Form Types
export interface BlogPostFormData {
  title: string;
  slug?: string;
  excerpt?: string;
  content: string;
  featured_image_id?: string;
  category_id?: string;
  status: 'draft' | 'published' | 'scheduled' | 'archived';
  visibility: 'public' | 'private' | 'password';
  password?: string;
  published_at?: string;
  scheduled_at?: string;
  meta_title?: string;
  meta_description?: string;
  meta_keywords?: string[];
  og_title?: string;
  og_description?: string;
  og_image_id?: string;
  is_featured: boolean;
  allow_comments: boolean;
  template: string;
  custom_css?: string;
  custom_js?: string;
  external_links?: {
    github?: string;
    demo?: string;
    playstore?: string;
    appstore?: string;
    youtube?: string;
    website?: string;
    [key: string]: string | undefined;
  };
  tag_ids?: string[];
}

export interface BlogCategoryFormData {
  name: string;
  slug?: string;
  description?: string;
  color: string;
  icon?: string;
  sort_order: number;
  is_active: boolean;
}

export interface BlogTagFormData {
  name: string;
  slug?: string;
  description?: string;
  color: string;
  is_active: boolean;
}

// Database helper functions
export const db = {
  // Profile operations
  async getProfile(userId: string): Promise<Profile | null> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        // Handle specific error cases
        if (error.code === 'PGRST116') {
          // No rows returned - profile doesn't exist
          console.log('Profile not found for user:', userId);
          return null;
        }

        console.error('Error fetching profile:', {
          error: error,
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code,
          userId: userId
        });
        return null;
      }

      return data;
    } catch (error) {
      console.error('Unexpected error fetching profile:', error);
      return null;
    }
  },

  async createProfile(userId: string, email: string, displayName?: string): Promise<Profile | null> {
    try {
      // Get user metadata from Supabase auth for OAuth users
      const { data: { user } } = await supabase.auth.getUser();
      const isGoogleUser = user?.app_metadata?.provider === 'google';
      const googleDisplayName = user?.user_metadata?.full_name || user?.user_metadata?.name;
      const googleAvatar = user?.user_metadata?.avatar_url;
      
      const profileData = {
        id: userId,
        email: email,
        display_name: 
          googleDisplayName || 
          displayName || 
          email.split('@')[0],
        avatar_url: 
          (isGoogleUser && googleAvatar) ? googleAvatar : undefined,
        role: email === '<EMAIL>' ? 'admin' as const : 'user' as const,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('🎨 Creating profile with auto OAuth profile picture:', {
        userId: profileData.id,
        email: profileData.email,
        displayName: profileData.display_name,
        isGoogleUser,
        hasGoogleAvatar: !!googleAvatar,
        avatarUrl: profileData.avatar_url,
        provider: user?.app_metadata?.provider
      });

      const { data, error } = await supabase
        .from('profiles')
        .insert([profileData])
        .select()
        .single();

      if (error) {
        console.error('Error creating profile:', {
          error: error,
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code,
          userId: userId,
          email: email
        });
        
        // If it's a duplicate key error, try to get the existing profile
        if (error.code === '23505') { // Duplicate key error
          console.log('Profile already exists, fetching existing profile...');
          return await this.getProfile(userId);
        }
        
        return null;
      }

      console.log('Profile created successfully:', data);
      return data;
    } catch (error) {
      console.error('Unexpected error creating profile:', error);
      return null;
    }
  },

  async updateProfile(userId: string, updates: Partial<Profile>): Promise<Profile | null> {
    try {
      console.log('Updating profile for user:', userId, 'with updates:', updates);
      
      // First check if profile exists
      const existingProfile = await this.getProfile(userId);
      if (!existingProfile) {
        console.log('Profile does not exist, creating one first...');
        // Get user info from auth to create profile
        const { data: { user } } = await supabase.auth.getUser();
        if (user && user.id === userId) {
          const newProfile = await this.createProfile(userId, user.email || '', user.user_metadata?.display_name);
          if (!newProfile) {
            throw new Error('Failed to create profile before update');
          }
          console.log('Profile created, now applying updates...');
        } else {
          throw new Error('User not found or unauthorized');
        }
      }

      const { data, error } = await supabase
        .from('profiles')
        .update({ 
          ...updates, 
          updated_at: new Date().toISOString() 
        })
        .eq('id', userId)
        .select('*')
        .single();

      if (error) {
        console.error('Error updating profile:', {
          error: error,
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code,
          userId: userId,
          updates: updates
        });
        
        // Handle specific error codes
        if (error.code === 'PGRST116') {
          throw new Error('Profile not found. Please refresh the page and try again.');
        }
        
        throw error;
      }

      console.log('Profile updated successfully:', data);
      return data;
    } catch (error) {
      console.error('Error in updateProfile:', error);
      throw error;
    }
  },

  // Task operations
  async getTasks(userId: string): Promise<Task[]> {
    const { data, error } = await supabase
      .from('tasks')
      .select('*')
      .eq('created_by', userId)
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching tasks:', error);
      return [];
    }
    
    return data || [];
  },

  async createTask(task: Omit<Task, 'id' | 'created_at' | 'updated_at'>): Promise<Task | null> {
    const { data, error } = await supabase
      .from('tasks')
      .insert([task])
      .select()
      .single();
    
    if (error) {
      console.error('Error creating task:', error);
      return null;
    }
    
    return data;
  },

  async updateTask(taskId: string, updates: Partial<Task>): Promise<Task | null> {
    const { data, error } = await supabase
      .from('tasks')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', taskId)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating task:', error);
      return null;
    }
    
    return data;
  },

  async deleteTask(taskId: string): Promise<boolean> {
    const { error } = await supabase
      .from('tasks')
      .delete()
      .eq('id', taskId);
    
    if (error) {
      console.error('Error deleting task:', error);
      return false;
    }
    
    return true;
  },

  // Inquiry operations
  async getInquiries(): Promise<Inquiry[]> {
    const { data, error } = await supabase
      .from('inquiries')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching inquiries:', error);
      return [];
    }

    return data || [];
  },

  async createInquiry(inquiry: Omit<Inquiry, 'id' | 'created_at' | 'updated_at' | 'status' | 'priority' | 'read_at' | 'responded_at'>): Promise<Inquiry | null> {
    const { data, error } = await supabase
      .from('inquiries')
      .insert([inquiry])
      .select()
      .single();

    if (error) {
      console.error('Error creating inquiry:', error);
      return null;
    }

    return data;
  },

  async updateInquiry(inquiryId: string, updates: Partial<Inquiry>): Promise<Inquiry | null> {
    const updateData: Partial<Inquiry> & { read_at?: string; responded_at?: string } = { ...updates };

    // Set read_at timestamp if status is being changed to 'read'
    if (updates.status === 'read' && !updates.read_at) {
      updateData.read_at = new Date().toISOString();
    }

    // Set responded_at timestamp if status is being changed to 'responded'
    if (updates.status === 'responded' && !updates.responded_at) {
      updateData.responded_at = new Date().toISOString();
    }

    const { data, error } = await supabase
      .from('inquiries')
      .update(updateData)
      .eq('id', inquiryId)
      .select()
      .single();

    if (error) {
      console.error('Error updating inquiry:', error);
      return null;
    }

    return data;
  },

  async deleteInquiry(inquiryId: string): Promise<boolean> {
    const { error } = await supabase
      .from('inquiries')
      .delete()
      .eq('id', inquiryId);

    if (error) {
      console.error('Error deleting inquiry:', error);
      return false;
    }

    return true;
  },

  // Admin helper functions
  async isUserAdmin(userId: string): Promise<boolean> {
    const profile = await this.getProfile(userId);
    return profile?.role === 'admin' || profile?.email === '<EMAIL>';
  },

  // Blog Categories operations
  async getCategories(): Promise<BlogCategory[]> {
    const { data, error } = await supabase
      .from('blog_categories')
      .select('*')
      .eq('is_active', true)
      .order('sort_order', { ascending: true });

    if (error) {
      console.error('Error fetching categories:', error);
      return [];
    }

    return data || [];
  },

  async getCategoryBySlug(slug: string): Promise<BlogCategory | null> {
    const { data, error } = await supabase
      .from('blog_categories')
      .select('*')
      .eq('slug', slug)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Error fetching category:', error);
      return null;
    }

    return data;
  },

  async createCategory(categoryData: BlogCategoryFormData): Promise<BlogCategory | null> {
    const { data, error } = await supabase
      .from('blog_categories')
      .insert([categoryData])
      .select()
      .single();

    if (error) {
      console.error('Error creating category:', error);
      return null;
    }

    return data;
  },

  async updateCategory(categoryId: string, updates: Partial<BlogCategoryFormData>): Promise<BlogCategory | null> {
    const { data, error } = await supabase
      .from('blog_categories')
      .update(updates)
      .eq('id', categoryId)
      .select()
      .single();

    if (error) {
      console.error('Error updating category:', error);
      return null;
    }

    return data;
  },

  async deleteCategory(categoryId: string): Promise<boolean> {
    const { error } = await supabase
      .from('blog_categories')
      .delete()
      .eq('id', categoryId);

    if (error) {
      console.error('Error deleting category:', error);
      return false;
    }

    return true;
  },

  // Blog Tags operations
  async getTags(): Promise<BlogTag[]> {
    const { data, error } = await supabase
      .from('blog_tags')
      .select('*')
      .eq('is_active', true)
      .order('usage_count', { ascending: false });

    if (error) {
      console.error('Error fetching tags:', error);
      return [];
    }

    return data || [];
  },

  async getTagBySlug(slug: string): Promise<BlogTag | null> {
    const { data, error } = await supabase
      .from('blog_tags')
      .select('*')
      .eq('slug', slug)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Error fetching tag:', error);
      return null;
    }

    return data;
  },

  async createTag(tagData: BlogTagFormData): Promise<BlogTag | null> {
    const { data, error } = await supabase
      .from('blog_tags')
      .insert([tagData])
      .select()
      .single();

    if (error) {
      console.error('Error creating tag:', error);
      return null;
    }

    return data;
  },

  async updateTag(tagId: string, updates: Partial<BlogTagFormData>): Promise<BlogTag | null> {
    const { data, error } = await supabase
      .from('blog_tags')
      .update(updates)
      .eq('id', tagId)
      .select()
      .single();

    if (error) {
      console.error('Error updating tag:', error);
      return null;
    }

    return data;
  },

  async deleteTag(tagId: string): Promise<boolean> {
    const { error } = await supabase
      .from('blog_tags')
      .delete()
      .eq('id', tagId);

    if (error) {
      console.error('Error deleting tag:', error);
      return false;
    }

    return true;
  },

  // Blog Posts operations
  async getBlogPosts(options?: {
    status?: string;
    category?: string;
    tag?: string;
    featured?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<BlogPost[]> {
    let query = supabase
      .from('blog_posts')
      .select(`
        *,
        category:blog_categories(*),
        author:profiles(*),
        featured_image:blog_media!blog_posts_featured_image_id_fkey(*),
        og_image:blog_media!blog_posts_og_image_id_fkey(*),
        tags:blog_post_tags(blog_tags(*))
      `);

    if (options?.status) {
      query = query.eq('status', options.status);
    }

    if (options?.category) {
      query = query.eq('category_id', options.category);
    }

    if (options?.featured !== undefined) {
      query = query.eq('is_featured', options.featured);
    }

    query = query.order('created_at', { ascending: false });

    if (options?.limit) {
      query = query.limit(options.limit);
    }

    if (options?.offset) {
      query = query.range(options.offset, (options.offset + (options.limit || 10)) - 1);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching blog posts:', error);
      return [];
    }

    // Transform the data to include tags properly
    return (data || []).map(post => ({
      ...post,
      tags: post.tags?.map((pt: { blog_tags: BlogTag }) => pt.blog_tags) || []
    }));
  },

  async getBlogPostBySlug(slug: string): Promise<BlogPost | null> {
    const { data, error } = await supabase
      .from('blog_posts')
      .select(`
        *,
        category:blog_categories(*),
        author:profiles(*),
        featured_image:blog_media!blog_posts_featured_image_id_fkey(*),
        og_image:blog_media!blog_posts_og_image_id_fkey(*),
        tags:blog_post_tags(blog_tags(*))
      `)
      .eq('slug', slug)
      .single();

    if (error) {
      console.error('Error fetching blog post:', error);
      return null;
    }

    // Transform the data to include tags properly
    return {
      ...data,
      tags: data.tags?.map((pt: { blog_tags: BlogTag }) => pt.blog_tags) || []
    };
  },

  async createBlogPost(postData: BlogPostFormData): Promise<BlogPost | null> {
    const { tag_ids, ...postFields } = postData;

    const { data: post, error: postError } = await supabase
      .from('blog_posts')
      .insert([postFields])
      .select()
      .single();

    if (postError) {
      console.error('Error creating blog post:', postError);
      return null;
    }

    // Add tags if provided
    if (tag_ids && tag_ids.length > 0) {
      const tagRelations = tag_ids.map(tagId => ({
        post_id: post.id,
        tag_id: tagId
      }));

      const { error: tagError } = await supabase
        .from('blog_post_tags')
        .insert(tagRelations);

      if (tagError) {
        console.error('Error adding tags to post:', tagError);
      }
    }

    return this.getBlogPostBySlug(post.slug);
  },

  // HielLinks Service Methods
  async getHielProfile(username: string): Promise<HielProfile | null> {
    try {
      const { data: profileData, error } = await supabase
        .from('hiel_profiles')
        .select('*')
        .eq('username', username)
        .eq('status', 'published')
        .eq('is_active', true)
        .single();

      if (error || !profileData) {
        console.error('Error fetching HielProfile:', error);
        return null;
      }

      // Fetch related data separately
      const [userResult, linksResult] = await Promise.all([
        supabase
          .from('profiles')
          .select('id, display_name, email')
          .eq('id', profileData.user_id)
          .single(),
        supabase
          .from('hiel_links')
          .select('*')
          .eq('profile_id', profileData.id)
          .order('sort_order', { ascending: true })
      ]);

      const data = {
        ...profileData,
        user: userResult.data || null,
        links: linksResult.data || []
      };

      return data;
    } catch (error) {
      console.error('Unexpected error fetching HielProfile:', error);
      return null;
    }
  },

  async getUserHielProfiles(userId: string): Promise<HielProfile[]> {
    try {
      const { data: profilesData, error: profilesError } = await supabase
        .from('hiel_profiles')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (profilesError) {
        console.error('Error fetching user HielProfiles:', profilesError);
        return [];
      }

      if (!profilesData || profilesData.length === 0) {
        return [];
      }

      // Fetch links for each profile separately
      const profilesWithLinks = await Promise.all(
        profilesData.map(async (profile) => {
          const { data: links, error: linksError } = await supabase
            .from('hiel_links')
            .select('*')
            .eq('profile_id', profile.id)
            .order('sort_order', { ascending: true });

          if (linksError) {
            console.error(`Error fetching links for profile ${profile.id}:`, linksError);
            return {
              ...profile,
              links: []
            };
          }

          return {
            ...profile,
            links: links || []
          };
        })
      );

      return profilesWithLinks;
    } catch (error) {
      console.error('Unexpected error fetching user HielProfiles:', error);
      return [];
    }
  },

  async createHielProfile(profileData: Partial<HielProfile>): Promise<HielProfile | null> {
    try {
      console.log('Creating HielProfile with data:', profileData);

      // First, try a simple insert
      const { data: insertData, error: insertError } = await supabase
        .from('hiel_profiles')
        .insert([profileData])
        .select()
        .single();

      if (insertError) {
        console.error('Error creating HielProfile:', insertError);
        console.error('Error details:', JSON.stringify(insertError, null, 2));
        console.error('Error code:', insertError.code);
        console.error('Error message:', insertError.message);
        return null;
      }

      console.log('Successfully created HielProfile:', insertData);

      // Now fetch with relations
      const [userResult, linksResult] = await Promise.all([
        supabase
          .from('profiles')
          .select('id, display_name, email')
          .eq('id', insertData.user_id)
          .single(),
        supabase
          .from('hiel_links')
          .select('*')
          .eq('profile_id', insertData.id)
          .order('sort_order', { ascending: true })
      ]);

      const fullData = {
        ...insertData,
        user: userResult.data || null,
        links: linksResult.data || []
      };

      return fullData;
    } catch (error) {
      console.error('Unexpected error creating HielProfile:', error);
      return null;
    }
  },

  async updateHielProfile(id: string, updates: Partial<HielProfile>): Promise<HielProfile | null> {
    try {
      // Log the update data for debugging
      console.log('Updating HielProfile with ID:', id);
      console.log('Update data:', updates);

      // Validate username if it's being updated
      if (updates.username) {
        const usernameRegex = /^[a-zA-Z0-9._]+$/;
        if (updates.username.length < 3 || updates.username.length > 30 || !usernameRegex.test(updates.username)) {
          console.error('Invalid username format:', updates.username);
          throw new Error(`Username must be 3-30 characters and contain only letters, numbers, dots, and underscores`);
        }
      }

      const { data, error } = await supabase
        .from('hiel_profiles')
        .update(updates)
        .eq('id', id)
        .select('*')
        .single();

      if (error) {
        console.error('Error updating HielProfile:', {
          error,
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint
        });
        
        // Provide more specific error messages
        if (error.code === '23505') {
          throw new Error('Username is already taken. Please choose a different username.');
        } else if (error.code === '23514') {
          throw new Error('Invalid data format. Please check your input and try again.');
        } else {
          throw new Error(error.message || 'Failed to update profile');
        }
      }

      if (!data) {
        console.error('No data returned from update operation');
        throw new Error('Update operation completed but no data was returned');
      }

      // Fetch the complete profile with relationships
      const profileWithRelations = await this.getHielProfile(data.username);
      
      console.log('Successfully updated HielProfile:', data.id);
      return profileWithRelations || data;
    } catch (error) {
      console.error('Unexpected error updating HielProfile:', error);
      throw error; // Re-throw the error instead of returning null
    }
  },

  async deleteHielProfile(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('hiel_profiles')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting HielProfile:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Unexpected error deleting HielProfile:', error);
      return false;
    }
  },

  async createHielLink(linkData: Partial<HielLink>): Promise<HielLink | null> {
    try {
      const { data, error } = await supabase
        .from('hiel_links')
        .insert([linkData])
        .select()
        .single();

      if (error) {
        console.error('Error creating HielLink:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Unexpected error creating HielLink:', error);
      return null;
    }
  },

  async updateHielLink(id: string, updates: Partial<HielLink>): Promise<HielLink | null> {
    try {
      const { data, error } = await supabase
        .from('hiel_links')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating HielLink:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Unexpected error updating HielLink:', error);
      return null;
    }
  },

  async deleteHielLink(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('hiel_links')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting HielLink:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Unexpected error deleting HielLink:', error);
      return false;
    }
  },

  async trackHielAnalytics(analyticsData: Partial<HielAnalytics>): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('hiel_analytics')
        .insert([analyticsData]);

      if (error) {
        console.error('Error tracking HielAnalytics:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Unexpected error tracking HielAnalytics:', error);
      return false;
    }
  },

  async getHielAnalytics(profileId: string, days: number = 30): Promise<HielAnalytics[]> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data, error } = await supabase
        .from('hiel_analytics')
        .select('*')
        .eq('profile_id', profileId)
        .gte('created_at', startDate.toISOString())
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching HielAnalytics:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Unexpected error fetching HielAnalytics:', error);
      return [];
    }
  },

  async getHielSettings(userId: string): Promise<HielSettings | null> {
    try {
      const { data, error } = await supabase
        .from('hiel_settings')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        // If no settings exist for the user, create default settings
        if (error.code === 'PGRST116') {
          const defaultSettings = {
            user_id: userId,
            max_profiles: 1,
            max_links_per_profile: 5,
            max_storage_mb: 10,
            can_use_custom_domain: false,
            can_use_analytics: true,
            can_remove_branding: false,
            subscription_type: 'free' as const
          };

          const { data: newSettings, error: createError } = await supabase
            .from('hiel_settings')
            .insert([defaultSettings])
            .select()
            .single();

          if (createError) {
            return null;
          }

          return newSettings;
        }
        
        return null;
      }

      return data;
    } catch {
      return null;
    }
  },

  async updateHielSettings(userId: string, updates: Partial<HielSettings>): Promise<HielSettings | null> {
    try {
      const { data, error } = await supabase
        .from('hiel_settings')
        .update(updates)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        console.error('Error updating HielSettings:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Unexpected error updating HielSettings:', error);
      return null;
    }
  },

  async incrementProfileView(profileId: string): Promise<boolean> {
    try {
      // First try using the RPC function
      const { error } = await supabase.rpc('increment_profile_view', {
        profile_id: profileId
      });

      if (error) {
        // If RPC function doesn't exist, fall back to direct update
        if (error.code === '42883' || error.message?.includes('function') || error.message?.includes('does not exist')) {
          console.warn('RPC function increment_profile_view not found, using fallback method');
          return await this.incrementProfileViewFallback(profileId);
        }

        console.error('Error incrementing profile view:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Unexpected error incrementing profile view:', error);
      // Try fallback method as last resort
      return await this.incrementProfileViewFallback(profileId);
    }
  },

  async incrementProfileViewFallback(profileId: string): Promise<boolean> {
    try {
      // First get the current view count
      const { data: profile, error: fetchError } = await supabase
        .from('hiel_profiles')
        .select('view_count')
        .eq('id', profileId)
        .single();

      if (fetchError || !profile) {
        console.error('Error fetching profile for view count:', fetchError);
        return false;
      }

      // Increment the view count
      const { error } = await supabase
        .from('hiel_profiles')
        .update({
          view_count: (profile.view_count || 0) + 1
        })
        .eq('id', profileId);

      if (error) {
        console.error('Error incrementing profile view (fallback):', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Unexpected error in profile view fallback:', error);
      return false;
    }
  },

  async incrementLinkClick(linkId: string): Promise<boolean> {
    try {
      // First try using the RPC function
      const { error } = await supabase.rpc('increment_link_click', {
        link_id: linkId
      });

      if (error) {
        // If RPC function doesn't exist, fall back to direct update
        if (error.code === '42883' || error.message?.includes('function') || error.message?.includes('does not exist')) {
          console.warn('RPC function increment_link_click not found, using fallback method');
          return await this.incrementLinkClickFallback(linkId);
        }

        console.error('Error incrementing link click:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Unexpected error incrementing link click:', error);
      // Try fallback method as last resort
      return await this.incrementLinkClickFallback(linkId);
    }
  },

  async incrementLinkClickFallback(linkId: string): Promise<boolean> {
    try {
      // First get the current click count
      const { data: link, error: fetchError } = await supabase
        .from('hiel_links')
        .select('click_count')
        .eq('id', linkId)
        .single();

      if (fetchError || !link) {
        console.error('Error fetching link for click count:', fetchError);
        return false;
      }

      // Increment the click count
      const { error } = await supabase
        .from('hiel_links')
        .update({
          click_count: (link.click_count || 0) + 1
        })
        .eq('id', linkId);

      if (error) {
        console.error('Error incrementing link click (fallback):', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Unexpected error in link click fallback:', error);
      return false;
    }
  },

  async checkUsernameAvailability(username: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('hiel_profiles')
        .select('id')
        .eq('username', username)
        .single();

      if (error && error.code === 'PGRST116') {
        // No rows returned, username is available
        return true;
      }

      if (error) {
        console.error('Error checking username availability:', error);
        return false;
      }

      // Username exists
      return false;
    } catch (error) {
      console.error('Unexpected error checking username availability:', error);
      return false;
    }
  },

  async getFeaturedHielProfiles(limit: number = 10): Promise<HielProfile[]> {
    try {
      const { data, error } = await supabase
        .from('hiel_profiles')
        .select('*')
        .eq('status', 'published')
        .eq('is_active', true)
        .eq('is_featured', true)
        .order('view_count', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching featured HielProfiles:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Unexpected error fetching featured HielProfiles:', error);
      return [];
    }
  },

  async updateBlogPost(postId: string, updates: Partial<BlogPostFormData>): Promise<BlogPost | null> {
    const { tag_ids, ...postFields } = updates;

    const { data: post, error: postError } = await supabase
      .from('blog_posts')
      .update(postFields)
      .eq('id', postId)
      .select()
      .single();

    if (postError) {
      console.error('Error updating blog post:', postError);
      return null;
    }

    // Update tags if provided
    if (tag_ids !== undefined) {
      // Remove existing tags
      await supabase
        .from('blog_post_tags')
        .delete()
        .eq('post_id', postId);

      // Add new tags
      if (tag_ids.length > 0) {
        const tagRelations = tag_ids.map(tagId => ({
          post_id: postId,
          tag_id: tagId
        }));

        const { error: tagError } = await supabase
          .from('blog_post_tags')
          .insert(tagRelations);

        if (tagError) {
          console.error('Error updating tags for post:', tagError);
        }
      }
    }

    return this.getBlogPostBySlug(post.slug);
  },

  async deleteBlogPost(postId: string): Promise<boolean> {
    const { error } = await supabase
      .from('blog_posts')
      .delete()
      .eq('id', postId);

    if (error) {
      console.error('Error deleting blog post:', error);
      return false;
    }

    return true;
  },

  async incrementPostViewCount(postId: string): Promise<void> {
    try {
      // Get current view count
      const { data: post, error: fetchError } = await supabase
        .from('blog_posts')
        .select('view_count')
        .eq('id', postId)
        .single();

      if (fetchError || !post) {
        console.error('Error fetching post for view count:', fetchError);
        return;
      }

      // Increment view count
      const { error: updateError } = await supabase
        .from('blog_posts')
        .update({ view_count: (post.view_count || 0) + 1 })
        .eq('id', postId);

      if (updateError) {
        console.error('Error incrementing post view count:', updateError);
      }
    } catch (error) {
      console.error('Error incrementing post view count:', error);
    }
  },

  // Blog Media operations
  async getMedia(): Promise<BlogMedia[]> {
    const { data, error } = await supabase
      .from('blog_media')
      .select('*')
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching media:', error);
      return [];
    }

    return data || [];
  },

  async getMediaById(mediaId: string): Promise<BlogMedia | null> {
    const { data, error } = await supabase
      .from('blog_media')
      .select('*')
      .eq('id', mediaId)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Error fetching media:', error);
      return null;
    }

    return data;
  },

  async uploadMedia(file: File, metadata?: Partial<BlogMedia>): Promise<BlogMedia | null> {
    try {
      // Generate unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `blog-media/${fileName}`;

      // Upload to Supabase Storage
      const { error: uploadError } = await supabase.storage
        .from('blog-media')
        .upload(filePath, file);

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('blog-media')
        .getPublicUrl(filePath);

      // Get image dimensions if it's an image
      let width: number | null = null;
      let height: number | null = null;

      if (file.type.startsWith('image/')) {
        const dimensions = await new Promise<{ width: number; height: number }>((resolve) => {
          const img = new Image();
          img.onload = () => resolve({ width: img.width, height: img.height });
          img.src = URL.createObjectURL(file);
        });
        width = dimensions.width;
        height = dimensions.height;
      }

      // Save media record to database
      const mediaData = {
        filename: fileName,
        original_name: file.name,
        file_path: publicUrl,
        file_size: file.size,
        mime_type: file.type,
        width,
        height,
        alt_text: metadata?.alt_text || '',
        caption: metadata?.caption || '',
        is_active: true,
        ...metadata
      };

      const { data: mediaRecord, error: dbError } = await supabase
        .from('blog_media')
        .insert([mediaData])
        .select()
        .single();

      if (dbError) throw dbError;
      return mediaRecord;
    } catch (error) {
      console.error('Error uploading media:', error);
      return null;
    }
  },

  async updateMedia(mediaId: string, updates: Partial<BlogMedia>): Promise<BlogMedia | null> {
    const { data, error } = await supabase
      .from('blog_media')
      .update(updates)
      .eq('id', mediaId)
      .select()
      .single();

    if (error) {
      console.error('Error updating media:', error);
      return null;
    }

    return data;
  },

  async deleteMedia(mediaId: string): Promise<boolean> {
    try {
      // Get media info first
      const media = await this.getMediaById(mediaId);
      if (!media) return false;

      // Delete from storage
      const { error: storageError } = await supabase.storage
        .from('blog-media')
        .remove([`blog-media/${media.filename}`]);

      if (storageError) {
        console.error('Error deleting from storage:', storageError);
      }

      // Delete from database
      const { error: dbError } = await supabase
        .from('blog_media')
        .delete()
        .eq('id', mediaId);

      if (dbError) {
        console.error('Error deleting from database:', dbError);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error deleting media:', error);
      return false;
    }
  },

  // Team Management
  async getTeamMembers(activeOnly: boolean = true): Promise<TeamMember[]> {
    let query = supabase.from('team_members').select('*');

    if (activeOnly) {
      query = query.eq('is_active', true);
    }

    const { data, error } = await query.order('display_order', { ascending: true });

    if (error) {
      console.error('Error fetching team members:', error);
      return [];
    }

    return data || [];
  },

  async getTeamMember(id: string): Promise<TeamMember | null> {
    const { data, error } = await supabase
      .from('team_members')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching team member:', error);
      return null;
    }

    return data;
  },

  async createTeamMember(memberData: Omit<TeamMember, 'id' | 'created_at' | 'updated_at'>): Promise<TeamMember | null> {
    const { data, error } = await supabase
      .from('team_members')
      .insert([memberData])
      .select()
      .single();

    if (error) {
      console.error('Error creating team member:', error);
      return null;
    }

    return data;
  },

  async updateTeamMember(id: string, memberData: Partial<TeamMember>): Promise<TeamMember | null> {
    const { data, error } = await supabase
      .from('team_members')
      .update(memberData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating team member:', error);
      return null;
    }

    return data;
  },

  async deleteTeamMember(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('team_members')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting team member:', error);
      return false;
    }

    return true;
  },

  async reorderTeamMembers(memberIds: string[]): Promise<boolean> {
    try {
      const updates = memberIds.map((id, index) =>
        supabase
          .from('team_members')
          .update({ display_order: index })
          .eq('id', id)
      );

      await Promise.all(updates);
      return true;
    } catch (error) {
      console.error('Error reordering team members:', error);
      return false;
    }
  },

  // Team Applications
  async getTeamApplications(status?: string): Promise<TeamApplication[]> {
    try {
      let query = supabase.from('team_applications').select('*');

      if (status) {
        query = query.eq('status', status);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching team applications:', {
          error: error,
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code,
          status: status
        });
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Unexpected error fetching team applications:', error);
      return [];
    }
  },

  async getTeamApplication(id: string): Promise<TeamApplication | null> {
    const { data, error } = await supabase
      .from('team_applications')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching team application:', error);
      return null;
    }

    return data;
  },

  async createTeamApplication(applicationData: Omit<TeamApplication, 'id' | 'created_at' | 'updated_at' | 'status' | 'priority'>): Promise<TeamApplication | null> {
    try {
      // Get current user if authenticated
      const { data: { user } } = await supabase.auth.getUser();
      
      // Prepare application data with proper user_id handling
      const dataToInsert = {
        ...applicationData,
        // Set user_id if user is authenticated, otherwise leave null for anonymous submissions
        user_id: user?.id || null,
        // Ensure default values
        status: 'pending' as const,
        priority: 'medium' as const,
        account_created: applicationData.account_created || false
      };

      console.log('Creating team application with data:', {
        email: dataToInsert.email,
        user_id: dataToInsert.user_id,
        account_created: dataToInsert.account_created
      });

      const { data, error } = await supabase
        .from('team_applications')
        .insert([dataToInsert])
        .select()
        .single();

      if (error) {
        console.error('Error creating team application:', error);
        return null;
      }

      // Send account creation prompt email
      try {
        const { sendAccountCreationPrompt } = await import('./services/emailService');
        const emailResult = await sendAccountCreationPrompt(
          applicationData.name,
          applicationData.email,
          data.id
        );
        
        if (!emailResult.success) {
          console.warn('Failed to send account creation email:', emailResult.message);
        }
      } catch (emailError) {
        console.error('Error sending account creation email:', emailError);
        // Don't fail the application creation if email fails
      }

      return data;
    } catch (error) {
      console.error('Error in createTeamApplication:', error);
      return null;
    }
  },

  async updateTeamApplication(id: string, applicationData: Partial<TeamApplication>): Promise<TeamApplication | null> {
    const updateData = { ...applicationData };

    // Set reviewed_at timestamp if status is being changed from pending
    if (applicationData.status && applicationData.status !== 'pending' && !applicationData.reviewed_at) {
      updateData.reviewed_at = new Date().toISOString();
    }

    const { data, error } = await supabase
      .from('team_applications')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating team application:', error);
      return null;
    }

    // Handle post-approval workflow
    if (data.status === 'approved' && applicationData.status === 'approved') {
      try {
        await this.handleApprovedCandidate(data);
      } catch (error) {
        console.error('Error handling approved candidate workflow:', error);
        // Don't fail the update if workflow handling fails
      }
    }

    return data;
  },

  // New method: Handle approved candidate workflow
  async handleApprovedCandidate(application: TeamApplication): Promise<void> {
    console.log('Processing approved candidate:', application.name);

    // 1. Enable chat automatically for approved candidates
    if (application.user_id) {
      try {
        await this.createChatPermission({
          user_id: application.user_id,
          application_id: application.id,
          is_enabled: true,
          enabled_by: application.reviewed_by || 'system',
          enabled_at: new Date().toISOString()
        });
        console.log('✅ Chat enabled for approved candidate');
      } catch (error) {
        console.error('Error enabling chat for approved candidate:', error);
      }
    }

    // 2. Send approval notification email (placeholder for now)
    try {
      const { sendApprovalNotification } = await import('./services/emailService');
      const emailResult = await sendApprovalNotification(application);
      
      if (emailResult.success) {
        console.log('✅ Approval notification sent');
      } else {
        console.warn('Failed to send approval notification:', emailResult.message);
      }
    } catch (error) {
      console.error('Error sending approval notification:', error);
    }

    // 3. Create onboarding task/reminder for admin
    try {
      await this.createOnboardingTask(application);
      console.log('✅ Onboarding task created');
    } catch (error) {
      console.error('Error creating onboarding task:', error);
    }
  },

  // New method: Create onboarding task for approved candidate
  async createOnboardingTask(application: TeamApplication): Promise<void> {
    // Get current user (admin) to assign task
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    const taskData = {
      title: `Onboard ${application.name} - ${application.desired_role}`,
      description: `Complete onboarding process for approved candidate:\n\n` +
        `• Discuss role details and responsibilities\n` +
        `• Set start date and schedule\n` +
        `• Provide access to team resources\n` +
        `• Add to team communication channels\n` +
        `• Create team member profile\n\n` +
        `Candidate Email: ${application.email}\n` +
        `Applied for: ${application.desired_role}\n` +
        `Experience: ${application.experience_level}\n` +
        `Availability: ${application.availability}`,
      status: 'pending' as const,
      priority: 'high' as const,
      due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
      category: 'onboarding',
      created_by: user.id,
      metadata: {
        application_id: application.id,
        candidate_name: application.name,
        candidate_email: application.email,
        desired_role: application.desired_role
      }
    };

    await this.createTask(taskData);
  },

  // New method: Promote approved candidate to team member
  async promoteToTeamMember(application: TeamApplication, additionalData?: {
    start_date?: string;
    salary?: string;
    role_description?: string;
  }): Promise<TeamMember | null> {
    try {
      const memberData = {
        name: application.name,
        email: application.email,
        role: application.desired_role,
        bio: application.motivation,
        skills: application.skills || [],
        portfolio_url: application.portfolio_url,
        linkedin_url: application.linkedin_url,
        github_url: application.github_url,
        location: application.location,
        timezone: application.timezone,
        availability: application.availability,
        experience_level: application.experience_level,
        start_date: additionalData?.start_date || new Date().toISOString(),
        status: 'active' as const,
        is_active: true,
        application_id: application.id,
        user_id: application.user_id,
        ...additionalData
      };

      const { data, error } = await supabase
        .from('team_members')
        .insert([memberData])
        .select()
        .single();

      if (error) {
        console.error('Error creating team member:', error);
        return null;
      }

      // Update application to reflect promotion
      await this.updateTeamApplication(application.id, {
        admin_notes: `${application.admin_notes || ''}\n\n[PROMOTED] Candidate promoted to team member on ${new Date().toLocaleDateString()}`
      });

      console.log('✅ Candidate promoted to team member:', data);
      return data;
    } catch (error) {
      console.error('Error promoting candidate to team member:', error);
      return null;
    }
  },

  async deleteTeamApplication(id: string): Promise<boolean> {
    const { error } = await supabase
      .from('team_applications')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting team application:', error);
      return false;
    }

    return true;
  },

  // Chat Permissions
  async getChatPermissions(applicationId?: string): Promise<ChatPermission[]> {
    try {
      let query = supabase
        .from('chat_permissions')
        .select('*');

      if (applicationId) {
        query = query.eq('application_id', applicationId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching chat permissions:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error in getChatPermissions:', error);
      return [];
    }
  },

  async createChatPermission(permissionData: Omit<ChatPermission, 'id' | 'created_at' | 'updated_at'>): Promise<ChatPermission | null> {
    try {
      const { data, error } = await supabase
        .from('chat_permissions')
        .insert(permissionData)
        .select('*')
        .single();

      if (error) {
        console.error('Error creating chat permission:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in createChatPermission:', error);
      return null;
    }
  },

  // New function to create general chat permission for any user
  async createGeneralChatPermission(userId: string, enabledBy: string): Promise<ChatPermission | null> {
    try {
      const permissionData = {
        user_id: userId,
        application_id: null, // No application needed for general chat
        is_enabled: true,
        enabled_by: enabledBy,
        enabled_at: new Date().toISOString(),
        chat_type: 'general'
      };

      const { data, error } = await supabase
        .from('chat_permissions')
        .insert(permissionData)
        .select('*')
        .single();

      if (error) {
        console.error('Error creating general chat permission:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in createGeneralChatPermission:', error);
      return null;
    }
  },

  // New function to create inquiry-based chat permission
  async createInquiryChatPermission(userId: string, enabledBy: string): Promise<ChatPermission | null> {
    try {
      const permissionData = {
        user_id: userId,
        application_id: null, // No application needed for inquiry chat
        is_enabled: true,
        enabled_by: enabledBy,
        enabled_at: new Date().toISOString(),
        chat_type: 'inquiry'
      };

      const { data, error } = await supabase
        .from('chat_permissions')
        .insert(permissionData)
        .select('*')
        .single();

      if (error) {
        console.error('Error creating inquiry chat permission:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in createInquiryChatPermission:', error);
      return null;
    }
  },

  async updateChatPermission(id: string, permissionData: Partial<ChatPermission>): Promise<ChatPermission | null> {
    try {
      const { data, error } = await supabase
        .from('chat_permissions')
        .update({
          ...permissionData,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select('*')
        .single();

      if (error) {
        console.error('Error updating chat permission:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error in updateChatPermission:', error);
      return null;
    }
  },

  async toggleChatPermission(applicationId: string, userId: string, enabledBy: string, isEnabled: boolean): Promise<ChatPermission | null> {
    try {
      // Check if permission already exists
      let query = supabase
        .from('chat_permissions')
        .select('*')
        .eq('user_id', userId);

      // Handle both application-based and general chat
      if (applicationId && applicationId !== 'general') {
        query = query.eq('application_id', applicationId);
      } else {
        query = query.is('application_id', null);
      }

      const { data: existing } = await query.single();

      if (existing) {
        // Update existing permission
        return await this.updateChatPermission(existing.id, {
          is_enabled: isEnabled,
          enabled_by: isEnabled ? enabledBy : null,
          enabled_at: isEnabled ? new Date().toISOString() : null,
          disabled_at: !isEnabled ? new Date().toISOString() : null
        });
      } else {
        // Create new permission
        if (applicationId && applicationId !== 'general') {
          // Application-based chat
          return await this.createChatPermission({
            user_id: userId,
            application_id: applicationId,
            is_enabled: isEnabled,
            enabled_by: enabledBy,
            enabled_at: isEnabled ? new Date().toISOString() : null,
            disabled_at: null,
            chat_type: 'application'
          });
        } else {
          // General chat
          return await this.createGeneralChatPermission(userId, enabledBy);
        }
      }
    } catch (error) {
      console.error('Error in toggleChatPermission:', error);
      return null;
    }
  },

  // New function to check if user has chat permission (any type)
  async hasUserChatPermission(userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('chat_permissions')
        .select('id')
        .eq('user_id', userId)
        .eq('is_enabled', true)
        .limit(1);

      if (error) {
        console.error('Error checking user chat permission:', error);
        return false;
      }

      return (data && data.length > 0) || false;
    } catch (error) {
      console.error('Error in hasUserChatPermission:', error);
      return false;
    }
  },

  // Chat Rooms
  async getChatRooms(userId: string, type?: 'applicant_admin' | 'admin_only'): Promise<ChatRoom[]> {
    try {
      // Validate input
      if (!userId || typeof userId !== 'string') {
        console.error('Invalid userId provided to getChatRooms:', userId);
        return [];
      }

      console.log(`Fetching chat rooms for user: ${userId}, type: ${type || 'all'}`);

      // First get room IDs where user is a participant
      const { data: participantRooms, error: participantError } = await supabase
        .from('chat_room_participants')
        .select('room_id')
        .eq('user_id', userId);

      if (participantError) {
        console.error('Error fetching participant rooms:', {
          error: participantError,
          message: participantError.message,
          details: participantError.details,
          hint: participantError.hint,
          code: participantError.code,
          userId: userId
        });

        // If it's a permission error, provide more context
        if (participantError.code === 'PGRST301' || participantError.message?.includes('permission')) {
          console.error('Permission denied accessing chat room participants. Check RLS policies.');
        }

        return [];
      }

      if (!participantRooms || participantRooms.length === 0) {
        console.log('No chat room participations found for user:', userId);
        return [];
      }

      const roomIds = participantRooms.map(p => p.room_id).filter(Boolean);
      console.log(`Found ${roomIds.length} room IDs for user:`, roomIds);

      if (roomIds.length === 0) {
        console.log('No valid room IDs found for user:', userId);
        return [];
      }

      // Build the query step by step for better debugging
      let query = supabase
        .from('chat_rooms')
        .select(`
          *,
          participants:chat_room_participants(
            *
          )
        `)
        .eq('is_active', true)
        .in('id', roomIds);

      if (type) {
        console.log(`Filtering by type: ${type}`);
        query = query.eq('type', type);
      }

      console.log('Executing chat rooms query...');
      const { data, error } = await query
        .order('updated_at', { ascending: false });

      if (error) {
        console.error('Error fetching chat rooms:', {
          error: error,
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code,
          roomIds: roomIds,
          userId: userId,
          type: type
        });

        // Try a fallback approach with simpler query
        console.log('Attempting fallback query without participants join...');
        try {
          let fallbackQuery = supabase
            .from('chat_rooms')
            .select('*')
            .eq('is_active', true)
            .in('id', roomIds);

          if (type) {
            fallbackQuery = fallbackQuery.eq('type', type);
          }

          const { data: fallbackData, error: fallbackError } = await fallbackQuery
            .order('updated_at', { ascending: false });

          if (fallbackError) {
            console.error('Fallback query also failed:', fallbackError);
            return [];
          }

          console.log('Fallback query succeeded, fetching participants separately...');
          const roomsWithParticipants = await Promise.all(
            (fallbackData || []).map(async (room) => {
              try {
                const { data: participants } = await supabase
                  .from('chat_room_participants')
                  .select('*')
                  .eq('room_id', room.id);

                return { ...room, participants: participants || [] };
              } catch (error) {
                console.error(`Error fetching participants for room ${room.id}:`, error);
                return { ...room, participants: [] };
              }
            })
          );

          console.log(`Fallback approach succeeded with ${roomsWithParticipants.length} rooms`);
          return roomsWithParticipants;
        } catch (fallbackError) {
          console.error('Fallback approach failed:', fallbackError);
        }

        // Provide more specific error context
        if (error.code === 'PGRST301' || error.message?.includes('permission')) {
          console.error('Permission denied accessing chat rooms. Check RLS policies.');
        } else if (error.code === 'PGRST116') {
          console.log('No chat rooms found matching criteria');
          return [];
        } else if (error.code === '42601' || error.message?.includes('syntax')) {
          console.error('SQL syntax error in chat rooms query');
        }

        return [];
      }

      // Filter out any null/undefined results and ensure proper structure
      const validRooms = (data || []).filter(room => room && room.id);

      console.log(`Successfully fetched ${validRooms.length} chat rooms for user ${userId}:`,
        validRooms.map(r => ({ id: r.id, type: r.type, name: r.name })));

      return validRooms;

    } catch (error) {
      console.error('Unexpected error fetching chat rooms:', {
        error,
        userId,
        type,
        stack: error instanceof Error ? error.stack : undefined
      });
      return [];
    }
  },

  async createChatRoom(roomData: Omit<ChatRoom, 'id' | 'created_at' | 'updated_at' | 'participants' | 'last_message' | 'unread_count'>): Promise<ChatRoom | null> {
    const { data, error } = await supabase
      .from('chat_rooms')
      .insert([roomData])
      .select()
      .single();

    if (error) {
      console.error('Error creating chat room:', error);
      return null;
    }

    return data;
  },

  async addChatRoomParticipant(roomId: string, userId: string): Promise<ChatRoomParticipant | null> {
    const { data, error } = await supabase
      .from('chat_room_participants')
      .insert([{ room_id: roomId, user_id: userId }])
      .select()
      .single();

    if (error) {
      console.error('Error adding chat room participant:', error);
      return null;
    }

    return data;
  },

  async createApplicantChatRoom(applicationId: string, applicantUserId: string, adminUserId: string): Promise<ChatRoom | null> {
    try {
      // Create the chat room
      const room = await this.createChatRoom({
        type: 'applicant_admin',
        application_id: applicationId,
        created_by: adminUserId,
        is_active: true
      });

      if (!room) {
        throw new Error('Failed to create chat room');
      }

      // Add participants
      await this.addChatRoomParticipant(room.id, applicantUserId);
      await this.addChatRoomParticipant(room.id, adminUserId);

      return room;
    } catch (error) {
      console.error('Error creating applicant chat room:', error);
      return null;
    }
  },

  // New function to create general user chat room
  async createGeneralChatRoom(userId: string, adminUserId: string, roomName?: string): Promise<ChatRoom | null> {
    try {
      // Create the chat room for general support
      const room = await this.createChatRoom({
        type: 'applicant_admin', // Reusing the same type but without application
        application_id: null, // No application for general chat
        created_by: adminUserId,
        name: roomName || `Support Chat - ${userId}`,
        is_active: true
      });

      if (!room) {
        throw new Error('Failed to create general chat room');
      }

      // Add participants
      await this.addChatRoomParticipant(room.id, userId);
      await this.addChatRoomParticipant(room.id, adminUserId);

      return room;
    } catch (error) {
      console.error('Error creating general chat room:', error);
      return null;
    }
  },

  // New function to create inquiry-based chat room
  async createInquiryChatRoom(inquiryId: string, userEmail: string, adminUserId: string): Promise<ChatRoom | null> {
    try {
      // First try to find user by email
      const { data: userData } = await supabase.auth.admin.listUsers();
      const user = userData.users.find(u => u.email === userEmail);
      
      if (!user) {
        console.error('User not found for email:', userEmail);
        return null;
      }

      // Create the chat room for inquiry
      const room = await this.createChatRoom({
        type: 'applicant_admin',
        application_id: null, // No application for inquiry chat
        created_by: adminUserId,
        name: `Inquiry Chat - ${userEmail}`,
        is_active: true
      });

      if (!room) {
        throw new Error('Failed to create inquiry chat room');
      }

      // Add participants
      await this.addChatRoomParticipant(room.id, user.id);
      await this.addChatRoomParticipant(room.id, adminUserId);

      // Enable chat permission for this user
      await this.createInquiryChatPermission(user.id, adminUserId);

      return room;
    } catch (error) {
      console.error('Error creating inquiry chat room:', error);
      return null;
    }
  },

  // Enhanced function to enable chat for any user (from admin panel)
  async enableUserChat(userId: string, adminUserId: string, chatType: 'general' | 'inquiry' = 'general'): Promise<{ room: ChatRoom | null; permission: ChatPermission | null }> {
    try {
      // Check if user already has chat permission
      const hasPermission = await this.hasUserChatPermission(userId);
      
      let permission: ChatPermission | null = null;
      let room: ChatRoom | null = null;

      if (!hasPermission) {
        // Create chat permission
        if (chatType === 'general') {
          permission = await this.createGeneralChatPermission(userId, adminUserId);
        } else {
          permission = await this.createInquiryChatPermission(userId, adminUserId);
        }
      }

      // Check if chat room already exists for this user
      const existingRooms = await this.getChatRooms(userId);
      const generalRoom = existingRooms.find(r => r.application_id === null);

      if (!generalRoom) {
        // Create new chat room
        room = await this.createGeneralChatRoom(userId, adminUserId);
      } else {
        room = generalRoom;
      }

      return { room, permission };
    } catch (error) {
      console.error('Error enabling user chat:', error);
      return { room: null, permission: null };
    }
  },

  // Chat Messages
  async getChatMessages(roomId: string, limit: number = 50, offset: number = 0): Promise<ChatMessage[]> {
    const { data, error } = await supabase
      .from('chat_messages')
      .select('*')
      .eq('room_id', roomId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching chat messages:', error);
      return [];
    }

    return (data || []).reverse(); // Reverse to show oldest first
  },

  async sendChatMessage(messageData: Omit<ChatMessage, 'id' | 'created_at' | 'sender' | 'is_edited' | 'edited_at'>): Promise<ChatMessage | null> {
    // Import security functions dynamically to avoid circular imports
    const {
      checkMessageRateLimit,
      validateChatPermission,
      moderateContent,
      logChatAction
    } = await import('./security/chatSecurity');

    try {
      // Validate permissions
      const permissionCheck = await validateChatPermission(messageData.sender_id, messageData.room_id);
      if (!permissionCheck.allowed) {
        console.error('Permission denied:', permissionCheck.reason);
        throw new Error(permissionCheck.reason || 'Permission denied');
      }

      // Check rate limits
      const rateLimitOk = await checkMessageRateLimit(messageData.sender_id);
      if (!rateLimitOk) {
        throw new Error('Rate limit exceeded. Please wait before sending another message.');
      }

      // Moderate and sanitize content
      const moderation = moderateContent(messageData.message);
      if (!moderation.approved) {
        throw new Error(moderation.reason || 'Message content not approved');
      }

      // Prepare sanitized message data
      const sanitizedMessageData = {
        ...messageData,
        message: moderation.sanitized
      };

      const { data, error } = await supabase
        .from('chat_messages')
        .insert([sanitizedMessageData])
        .select('*')
        .single();

      if (error) {
        console.error('Error sending chat message:', error);
        return null;
      }

      // Update room's updated_at timestamp
      await supabase
        .from('chat_rooms')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', messageData.room_id);

      // Log the action
      await logChatAction('message_sent', {
        messageId: data.id,
        roomId: messageData.room_id,
        messageType: messageData.message_type
      }, messageData.sender_id);

      // Send email notifications to other participants (async, don't wait)
      this.sendMessageNotifications(data).catch((error: unknown) => {
        console.error('Error sending message notifications:', error);
      });

      return data;
    } catch (error) {
      console.error('Error in sendChatMessage:', error);
      throw error;
    }
  },

  async updateLastReadAt(roomId: string, userId: string): Promise<void> {
    const { error } = await supabase
      .from('chat_room_participants')
      .update({ last_read_at: new Date().toISOString() })
      .eq('room_id', roomId)
      .eq('user_id', userId);

    if (error) {
      console.error('Error updating last read timestamp:', error);
    }
  },

  // Account Creation Tracking
  async markAccountCreated(applicationId: string, userId: string): Promise<TeamApplication | null> {
    const { data, error } = await supabase
      .from('team_applications')
      .update({
        account_created: true,
        account_created_at: new Date().toISOString(),
        user_id: userId,
        updated_at: new Date().toISOString()
      })
      .eq('id', applicationId)
      .select()
      .single();

    if (error) {
      console.error('Error marking account as created:', error);
      return null;
    }

    return data;
  },

  async getApplicationByEmail(email: string): Promise<TeamApplication | null> {
    const { data, error } = await supabase
      .from('team_applications')
      .select('*')
      .eq('email', email)
      .eq('account_created', false)
      .order('created_at', { ascending: false })
      .limit(1);

    if (error) {
      console.error('Error fetching application by email:', error);
      return null;
    }

    // Return the first application if any exist, otherwise null
    return data && data.length > 0 ? data[0] : null;
  },

  // Check if email exists but is unconfirmed
  async checkEmailConfirmationStatus(email: string): Promise<{ exists: boolean; confirmed: boolean }> {
    try {
      // This is a workaround since we can't directly query auth.users
      // We'll try to trigger a password reset to see if user exists
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: 'https://example.com/reset' // Dummy URL that won't be used
      });

      if (error) {
        if (error.message.includes('User not found')) {
          return { exists: false, confirmed: false };
        }
        // If any other error, assume user exists but we can't determine status
        return { exists: true, confirmed: false };
      }

      // If no error, user exists (confirmed or not)
      return { exists: true, confirmed: false };
    } catch (error) {
      console.error('Error checking email status:', error);
      return { exists: false, confirmed: false };
    }
  },

  // Admin Users
  async getAdminUsers(): Promise<Profile[]> {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .or('role.eq.admin,<EMAIL>')
      .order('display_name', { ascending: true });

    if (error) {
      console.error('Error fetching admin users:', error);
      return [];
    }

    return data || [];
  },

  // Email Notifications
  async sendMessageNotifications(message: ChatMessage): Promise<void> {
    // Implementation would go here for sending notifications
    // This is a placeholder for future notification system
    console.log('Message notification placeholder:', message.id);
  },

  // Projects Management
  async getProjects(activeOnly: boolean = true): Promise<Project[]> {
    try {
      let query = supabase.from('projects').select('*');

      if (activeOnly) {
        query = query.eq('is_active', true);
      }

      const { data, error } = await query.order('sort_order', { ascending: true });

      if (error) {
        console.error('Error fetching projects:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Unexpected error fetching projects:', error);
      return [];
    }
  },

  async getProject(id: string): Promise<Project | null> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('Error fetching project:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Unexpected error fetching project:', error);
      return null;
    }
  },

  async createProject(projectData: Omit<Project, 'id' | 'created_at' | 'updated_at'>): Promise<Project | null> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .insert([projectData])
        .select()
        .single();

      if (error) {
        console.error('Error creating project:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Unexpected error creating project:', error);
      return null;
    }
  },

  async updateProject(id: string, projectData: Partial<Project>): Promise<Project | null> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .update(projectData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating project:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Unexpected error updating project:', error);
      return null;
    }
  },

  async deleteProject(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting project:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Unexpected error deleting project:', error);
      return false;
    }
  },

  async reorderProjects(projectIds: string[]): Promise<boolean> {
    try {
      const updates = projectIds.map((id, index) =>
        supabase
          .from('projects')
          .update({ sort_order: index + 1 })
          .eq('id', id)
      );

      await Promise.all(updates);
      return true;
    } catch (error) {
      console.error('Error reordering projects:', error);
      return false;
    }
  },

  async getProjectBySlug(slug: string): Promise<Project | null> {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .eq('slug', slug)
        .eq('is_active', true)
        .single();

      if (error) {
        console.error('Error fetching project by slug:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Unexpected error fetching project by slug:', error);
      return null;
    }
  },

  async uploadProjectImage(file: File, projectId?: string): Promise<string | null> {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${projectId || 'temp'}-${Date.now()}.${fileExt}`;
      const filePath = `projects/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('project-images')
        .upload(filePath, file);

      if (uploadError) {
        console.error('Error uploading image:', uploadError);
        return null;
      }

      const { data } = supabase.storage
        .from('project-images')
        .getPublicUrl(filePath);

      return data.publicUrl;
    } catch (error) {
      console.error('Unexpected error uploading image:', error);
      return null;
    }
  },

  async deleteProjectImage(imageUrl: string): Promise<boolean> {
    try {
      // Extract filename from URL
      const url = new URL(imageUrl);
      const pathSegments = url.pathname.split('/');
      const filename = pathSegments[pathSegments.length - 1];

      const { error } = await supabase.storage
        .from('project-images')
        .remove([`project-images/${filename}`]);

      if (error) throw error;
      return true;
    } catch {
      return false;
    }
  },

  // Profile Picture Management
  async uploadProfilePicture(file: File, userId: string): Promise<string | null> {
    try {
      // Generate unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `${userId}-${Date.now()}.${fileExt}`;
      const filePath = `avatars/${fileName}`;

      // Upload to Supabase Storage
      const { error: uploadError } = await supabase.storage
        .from('avatars')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(filePath);

      return publicUrl;
    } catch {
      return null;
    }
  },

  async updateProfilePicture(userId: string, avatarUrl: string): Promise<Profile | null> {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .update({ avatar_url: avatarUrl })
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch {
      return null;
    }
  },

  async deleteProfilePicture(userId: string): Promise<boolean> {
    try {
      // Get current profile to get avatar URL
      const profile = await this.getProfile(userId);
      if (!profile?.avatar_url) return true;

      // Only delete if it's a Supabase storage URL (not Google profile picture)
      if (profile.avatar_url.includes('supabase')) {
        // Extract filename from URL
        const url = new URL(profile.avatar_url);
        const pathSegments = url.pathname.split('/');
        const filename = pathSegments[pathSegments.length - 1];

        // Delete from storage
        await supabase.storage
          .from('avatars')
          .remove([`avatars/${filename}`]);
      }

      // Update profile to remove avatar URL
      const { error } = await supabase
        .from('profiles')
        .update({ avatar_url: null })
        .eq('id', userId);

      if (error) throw error;
      return true;
    } catch {
      return false;
    }
  },

  // Account Deletion Functions
  async requestAccountDeletion(userId: string, reason?: string): Promise<boolean> {
    try {
      console.log('Requesting account deletion for user:', userId);
      
      // Create deletion request record for audit trail
      const { error: requestError } = await supabase
        .from('account_deletion_requests')
        .insert([{
          user_id: userId,
          reason: reason || 'User requested account deletion',
          requested_at: new Date().toISOString(),
          status: 'pending'
        }]);

      if (requestError) {
        console.error('Error creating deletion request:', requestError);
        // Continue even if audit trail fails
      }

      return true;
    } catch (error) {
      console.error('Error requesting account deletion:', error);
      return false;
    }
  },

  async deleteUserAccount(userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('Starting account deletion process for user:', userId);

      // 1. Delete user's HielLinks profiles and related data
      console.log('Deleting HielLinks profiles...');
      const userHielProfiles = await this.getUserHielProfiles(userId);
      for (const profile of userHielProfiles) {
        await this.deleteHielProfile(profile.id);
      }

      // 2. Delete user's HielLinks settings
      console.log('Deleting HielLinks settings...');
      const { error: settingsError } = await supabase
        .from('hiel_settings')
        .delete()
        .eq('user_id', userId);

      if (settingsError) {
        console.warn('Error deleting HielLinks settings:', settingsError);
      }

      // 3. Delete chat messages and conversations
      console.log('Deleting chat data...');
      const { error: chatError } = await supabase
        .from('chat_messages')
        .delete()
        .eq('user_id', userId);

      if (chatError) {
        console.warn('Error deleting chat messages:', chatError);
      }

      // 4. Delete team applications
      console.log('Deleting team applications...');
      const { error: applicationsError } = await supabase
        .from('team_applications')
        .delete()
        .eq('user_id', userId);

      if (applicationsError) {
        console.warn('Error deleting team applications:', applicationsError);
      }

      // 5. Delete contact form submissions
      console.log('Deleting contact submissions...');
      const { error: contactError } = await supabase
        .from('contact_submissions')
        .delete()
        .eq('user_id', userId);

      if (contactError) {
        console.warn('Error deleting contact submissions:', contactError);
      }

      // 6. Delete social media content (if exists)
      console.log('Deleting social media content...');
      const { error: socialError } = await supabase
        .from('social_media_posts')
        .delete()
        .eq('user_id', userId);

      if (socialError) {
        console.warn('Error deleting social media posts:', socialError);
      }

      // 7. Delete user's profile picture from storage
      console.log('Deleting profile picture...');
      await this.deleteProfilePicture(userId);

      // 8. Mark deletion request as completed
      const { error: updateRequestError } = await supabase
        .from('account_deletion_requests')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (updateRequestError) {
        console.warn('Error updating deletion request status:', updateRequestError);
      }

      // 9. Finally, delete the user profile
      console.log('Deleting user profile...');
      const { error: profileError } = await supabase
        .from('profiles')
        .delete()
        .eq('id', userId);

      if (profileError) {
        console.error('Error deleting user profile:', profileError);
        return { success: false, error: 'Failed to delete user profile' };
      }

      // 10. Delete from Supabase Auth (this should be the last step)
      console.log('Deleting from Supabase Auth...');
      const { error: authError } = await supabase.auth.admin.deleteUser(userId);

      if (authError) {
        console.error('Error deleting from auth:', authError);
        // Profile is already deleted, so we consider this a success with warning
        return { success: true, error: 'Account data deleted but auth deletion failed. Please contact support.' };
      }

      console.log('Account deletion completed successfully for user:', userId);
      return { success: true };

    } catch (error) {
      console.error('Unexpected error during account deletion:', error);
      return { success: false, error: 'An unexpected error occurred during account deletion' };
    }
  },

  async exportUserData(userId: string): Promise<{
    export_date: string;
    user_id: string;
    profile: Profile | null;
    hiel_profiles: HielProfile[];
    hiel_settings: HielSettings | null;
    chat_messages: ChatMessage[];
    team_applications: TeamApplication[];
    contact_submissions: Inquiry[];
    note: string;
  }> {
    try {
      console.log('Exporting user data for:', userId);

      // Get user profile
      const profile = await this.getProfile(userId);
      
      // Get HielLinks profiles
      const hielProfiles = await this.getUserHielProfiles(userId);
      
      // Get HielLinks settings
      const { data: settings } = await supabase
        .from('hiel_settings')
        .select('*')
        .eq('user_id', userId)
        .single();

      // Get chat messages (last 30 days for privacy)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const { data: chatMessages } = await supabase
        .from('chat_messages')
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', thirtyDaysAgo.toISOString());

      // Get team applications
      const { data: teamApplications } = await supabase
        .from('team_applications')
        .select('*')
        .eq('user_id', userId);

      // Get contact submissions
      const { data: contactSubmissions } = await supabase
        .from('contact_submissions')
        .select('*')
        .eq('user_id', userId);

      const exportData = {
        export_date: new Date().toISOString(),
        user_id: userId,
        profile: profile,
        hiel_profiles: hielProfiles,
        hiel_settings: settings,
        chat_messages: chatMessages || [],
        team_applications: teamApplications || [],
        contact_submissions: contactSubmissions || [],
        note: 'This export contains your personal data from Hiel Tech services. Chat messages are limited to the last 30 days for privacy.'
      };

      return exportData;
    } catch (error) {
      console.error('Error exporting user data:', error);
      throw error;
    }
  },

  async getGoogleProfilePicture(userId: string): Promise<string | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user?.id === userId && user?.app_metadata?.provider === 'google') {
        return user?.user_metadata?.avatar_url || user?.user_metadata?.picture || null;
      }
      return null;
    } catch {
      return null;
    }
  }
};

// Organization System Functions
class OrganizationService {
  // ====================================
  // ORGANIZATION MANAGEMENT
  // ====================================

  async createOrganization(orgData: {
    name: string;
    slug: string;
    description?: string;
    size?: 'startup' | 'small' | 'medium' | 'large' | 'enterprise';
    industry?: string;
    country?: string;
    timezone?: string;
    website?: string;
  }) {
    try {
      const { data, error } = await supabase.rpc('create_organization_with_owner', {
        org_name: orgData.name,
        org_slug: orgData.slug,
        org_description: orgData.description,
        org_size: orgData.size || 'startup'
      });

      if (error) throw error;

      // Update additional fields if provided
      if (orgData.industry || orgData.country || orgData.timezone || orgData.website) {
        const updateData: Record<string, string> = {};
        if (orgData.industry) updateData.industry = orgData.industry;
        if (orgData.country) updateData.country = orgData.country;
        if (orgData.timezone) updateData.timezone = orgData.timezone;
        if (orgData.website) updateData.website = orgData.website;

        await supabase
          .from('organizations')
          .update(updateData)
          .eq('id', data);
      }

      return data;
    } catch (error) {
      console.error('Error creating organization:', error);
      return null;
    }
  }

  async getUserOrganizations(userId: string) {
    try {
      const { data, error } = await supabase
        .from('organization_members')
        .select(`
          *,
          organization:organizations(*)
        `)
        .eq('user_id', userId)
        .eq('status', 'active');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching user organizations:', error);
      return [];
    }
  }

  async getOrganization(orgId: string) {
    try {
      const { data, error } = await supabase
        .from('organizations')
        .select('*')
        .eq('id', orgId)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching organization:', error);
      return null;
    }
  }

  async updateOrganization(orgId: string, updates: Partial<{
    name: string;
    description: string;
    industry: string;
    size: string;
    country: string;
    timezone: string;
    website: string;
    settings: Record<string, unknown>;
  }>) {
    try {
      const { data, error } = await supabase
        .from('organizations')
        .update(updates)
        .eq('id', orgId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating organization:', error);
      return null;
    }
  }

  // ====================================
  // TEAM MEMBER MANAGEMENT
  // ====================================

  async getOrganizationMembers(orgId: string) {
    try {
      const { data, error } = await supabase
        .from('organization_members')
        .select(`
          *,
          user:auth.users(*)
        `)
        .eq('organization_id', orgId)
        .order('joined_at', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching organization members:', error);
      return [];
    }
  }

  async inviteTeamMember(invitation: {
    organizationId: string;
    email: string;
    role: 'admin' | 'project_manager' | 'member' | 'viewer';
    message?: string;
  }) {
    try {
      const token = crypto.randomUUID();
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7); // 7 days expiry

      const { data, error } = await supabase
        .from('organization_invitations')
        .insert({
          organization_id: invitation.organizationId,
          email: invitation.email,
          role: invitation.role,
          message: invitation.message,
          token,
          expires_at: expiresAt.toISOString(),
          invited_by: (await supabase.auth.getUser()).data.user?.id
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error inviting team member:', error);
      return null;
    }
  }

  async updateMemberRole(memberId: string, role: string, permissions?: string[]) {
    try {
      const { data, error } = await supabase
        .from('organization_members')
        .update({ 
          role,
          permissions: permissions || []
        })
        .eq('id', memberId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating member role:', error);
      return null;
    }
  }

  async removeMember(memberId: string) {
    try {
      const { error } = await supabase
        .from('organization_members')
        .delete()
        .eq('id', memberId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error removing member:', error);
      return false;
    }
  }

  // ====================================
  // PROJECT MANAGEMENT
  // ====================================

  async getOrganizationProjects(orgId: string, options?: {
    status?: string;
    limit?: number;
    offset?: number;
  }) {
    try {
      let query = supabase
        .from('organization_projects')
        .select('*')
        .eq('organization_id', orgId)
        .order('created_at', { ascending: false });

      if (options?.status) {
        query = query.eq('status', options.status);
      }

      if (options?.limit) {
        query = query.limit(options.limit);
      }

      if (options?.offset) {
        query = query.range(options.offset, (options.offset || 0) + (options.limit || 10) - 1);
      }

      const { data, error } = await query;

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching organization projects:', error);
      return [];
    }
  }

  async createProject(projectData: {
    organizationId: string;
    name: string;
    description?: string;
    priority?: 'low' | 'medium' | 'high' | 'critical';
    visibility?: 'private' | 'organization' | 'public';
    startDate?: string;
    dueDate?: string;
    budget?: number;
    estimatedHours?: number;
    projectManagerId?: string;
    teamMembers?: string[];
    tags?: string[];
  }) {
    try {
      const { data, error } = await supabase
        .from('organization_projects')
        .insert({
          organization_id: projectData.organizationId,
          name: projectData.name,
          description: projectData.description,
          priority: projectData.priority || 'medium',
          visibility: projectData.visibility || 'organization',
          start_date: projectData.startDate,
          due_date: projectData.dueDate,
          budget: projectData.budget,
          estimated_hours: projectData.estimatedHours,
          project_manager_id: projectData.projectManagerId,
          team_members: projectData.teamMembers || [],
          tags: projectData.tags || [],
          created_by: (await supabase.auth.getUser()).data.user?.id
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating project:', error);
      return null;
    }
  }

  async updateProject(projectId: string, updates: Partial<{
    name: string;
    description: string;
    status: string;
    priority: string;
    visibility: string;
    start_date: string;
    due_date: string;
    budget: number;
    estimated_hours: number;
    project_manager_id: string;
    team_members: string[];
    tags: string[];
    progress_percentage: number;
  }>) {
    try {
      const { data, error } = await supabase
        .from('organization_projects')
        .update(updates)
        .eq('id', projectId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating project:', error);
      return null;
    }
  }

  async deleteProject(projectId: string) {
    try {
      const { error } = await supabase
        .from('organization_projects')
        .delete()
        .eq('id', projectId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error deleting project:', error);
      return false;
    }
  }

  // ====================================
  // TASK MANAGEMENT
  // ====================================

  async getOrganizationTasks(orgId: string, options?: {
    projectId?: string;
    assigneeId?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }) {
    try {
      let query = supabase
        .from('organization_tasks')
        .select('*')
        .eq('organization_id', orgId)
        .order('created_at', { ascending: false });

      if (options?.projectId) {
        query = query.eq('project_id', options.projectId);
      }

      if (options?.assigneeId) {
        query = query.eq('assignee_id', options.assigneeId);
      }

      if (options?.status) {
        query = query.eq('status', options.status);
      }

      if (options?.limit) {
        query = query.limit(options.limit);
      }

      if (options?.offset) {
        query = query.range(options.offset, (options.offset || 0) + (options.limit || 10) - 1);
      }

      const { data, error } = await query;

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching organization tasks:', error);
      return [];
    }
  }

  async createTask(taskData: {
    organizationId: string;
    projectId?: string;
    title: string;
    description?: string;
    priority?: 'low' | 'medium' | 'high' | 'urgent';
    assigneeId?: string;
    dueDate?: string;
    estimatedHours?: number;
    labels?: string[];
  }) {
    try {
      const { data, error } = await supabase
        .from('organization_tasks')
        .insert({
          organization_id: taskData.organizationId,
          project_id: taskData.projectId,
          title: taskData.title,
          description: taskData.description,
          priority: taskData.priority || 'medium',
          assignee_id: taskData.assigneeId,
          due_date: taskData.dueDate,
          estimated_hours: taskData.estimatedHours,
          labels: taskData.labels || [],
          reporter_id: (await supabase.auth.getUser()).data.user?.id,
          created_by: (await supabase.auth.getUser()).data.user?.id
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating task:', error);
      return null;
    }
  }

  async updateTask(taskId: string, updates: Partial<{
    title: string;
    description: string;
    status: string;
    priority: string;
    assignee_id: string;
    due_date: string;
    estimated_hours: number;
    actual_hours: number;
    labels: string[];
    custom_fields: Record<string, unknown>;
  }>) {
    try {
      const { data, error } = await supabase
        .from('organization_tasks')
        .update(updates)
        .eq('id', taskId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating task:', error);
      return null;
    }
  }

  async deleteTask(taskId: string) {
    try {
      const { error } = await supabase
        .from('organization_tasks')
        .delete()
        .eq('id', taskId);

      if (error) throw error;
      return true;
    } catch (error) {
      console.error('Error deleting task:', error);
      return false;
    }
  }

  // ====================================
  // CHAT SYSTEM
  // ====================================

  async getOrganizationChannels(orgId: string) {
    try {
      const { data, error } = await supabase
        .from('organization_channels')
        .select('*')
        .eq('organization_id', orgId)
        .eq('is_archived', false)
        .order('created_at', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching organization channels:', error);
      return [];
    }
  }

  async createChannel(channelData: {
    organizationId: string;
    name: string;
    description?: string;
    type?: 'public' | 'private' | 'project';
    projectId?: string;
    members?: string[];
  }) {
    try {
      const { data, error } = await supabase
        .from('organization_channels')
        .insert({
          organization_id: channelData.organizationId,
          name: channelData.name.toLowerCase().replace(/\s+/g, '-'),
          description: channelData.description,
          type: channelData.type || 'public',
          project_id: channelData.projectId,
          members: channelData.members || [],
          admins: [(await supabase.auth.getUser()).data.user?.id],
          created_by: (await supabase.auth.getUser()).data.user?.id
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating channel:', error);
      return null;
    }
  }

  async getChannelMessages(channelId: string, limit = 50, offset = 0) {
    try {
      const { data, error } = await supabase
        .from('organization_messages')
        .select('*')
        .eq('channel_id', channelId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) throw error;
      return (data || []).reverse(); // Return in chronological order
    } catch (error) {
      console.error('Error fetching channel messages:', error);
      return [];
    }
  }

  async sendMessage(messageData: {
    channelId: string;
    content: string;
    messageType?: 'text' | 'file' | 'system';
    mentions?: string[];
    threadId?: string;
  }) {
    try {
      const { data, error } = await supabase
        .from('organization_messages')
        .insert({
          channel_id: messageData.channelId,
          content: messageData.content,
          message_type: messageData.messageType || 'text',
          mentions: messageData.mentions || [],
          thread_id: messageData.threadId,
          sender_id: (await supabase.auth.getUser()).data.user?.id
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error sending message:', error);
      return null;
    }
  }

  // ====================================
  // ANALYTICS
  // ====================================

  async getOrganizationAnalytics(orgId: string, days = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data, error } = await supabase
        .from('organization_analytics')
        .select('*')
        .eq('organization_id', orgId)
        .gte('date', startDate.toISOString().split('T')[0])
        .order('date', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching organization analytics:', error);
      return [];
    }
  }

  async getProjectAnalytics(projectId: string) {
    try {
      // Get project with tasks
      const { data: project, error: projectError } = await supabase
        .from('organization_projects')
        .select('*')
        .eq('id', projectId)
        .single();

      if (projectError) throw projectError;

      // Get project tasks
      const { data: tasks, error: tasksError } = await supabase
        .from('organization_tasks')
        .select('*')
        .eq('project_id', projectId);

      if (tasksError) throw tasksError;

      // Calculate analytics
      const totalTasks = tasks?.length || 0;
      const completedTasks = tasks?.filter(t => t.status === 'done').length || 0;
      const overdueTasks = tasks?.filter(t => 
        t.due_date && new Date(t.due_date) < new Date() && t.status !== 'done'
      ).length || 0;

      const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;
      const onTimeRate = totalTasks > 0 ? ((totalTasks - overdueTasks) / totalTasks) * 100 : 100;

      return {
        project,
        tasks,
        analytics: {
          total_tasks: totalTasks,
          completed_tasks: completedTasks,
          overdue_tasks: overdueTasks,
          completion_rate: Math.round(completionRate),
          on_time_rate: Math.round(onTimeRate),
          progress_percentage: project?.progress_percentage || 0
        }
      };
    } catch (error) {
      console.error('Error fetching project analytics:', error);
      return null;
    }
  }

  // ====================================
  // SUBSCRIPTION MANAGEMENT
  // ====================================

  async getOrganizationSubscription(orgId: string) {
    try {
      const { data, error } = await supabase
        .from('organization_subscriptions')
        .select('*')
        .eq('organization_id', orgId)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching organization subscription:', error);
      return null;
    }
  }

  async updateSubscription(orgId: string, updates: Partial<{
    tier: string;
    status: string;
    limits: Record<string, unknown>;
    amount: number;
    currency: string;
    payment_method: string;
    billing_email: string;
  }>) {
    try {
      const { data, error } = await supabase
        .from('organization_subscriptions')
        .update(updates)
        .eq('organization_id', orgId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating subscription:', error);
      return null;
    }
  }
}

// Export the organization service
export const organizationService = new OrganizationService();

export default supabase;
