/**
 * Data Encryption Utility
 * Client-side utility to encrypt existing unencrypted data
 */

import { supabase } from '@/lib/supabase';
import { EncryptionService } from '@/lib/services/encryptionService';

export interface MigrationStats {
  total: number;
  processed: number;
  errors: number;
  table: string;
}

export class DataEncryptor {
  private static readonly BATCH_SIZE = 5; // Small batches for client-side processing

  /**
   * Encrypt all unencrypted profiles
   */
  static async encryptProfiles(): Promise<MigrationStats> {
    console.log('🔐 Starting profile encryption...');
    
    const stats: MigrationStats = {
      total: 0,
      processed: 0,
      errors: 0,
      table: 'profiles'
    };

    try {
      // Get unencrypted profiles
      const { data: profiles, error } = await supabase
        .from('profiles')
        .select('*')
        .or('encrypted_fields.is.null,encrypted_fields.eq.{}')
        .limit(this.BATCH_SIZE);

      if (error) {
        console.error('Error fetching profiles:', error);
        return stats;
      }

      if (!profiles || profiles.length === 0) {
        console.log('✅ All profiles are already encrypted');
        return stats;
      }

      stats.total = profiles.length;

      for (const profile of profiles) {
        try {
          const fieldsToEncrypt = ['email', 'display_name', 'bio', 'company', 'position'];
          const encryptedData = await EncryptionService.encryptFields(
            profile,
            fieldsToEncrypt,
            'profile_encryption_key'
          );

          const { error: updateError } = await supabase
            .from('profiles')
            .update({
              encrypted_fields: encryptedData.encrypted_fields,
              encryption_version: 1
            })
            .eq('id', profile.id);

          if (updateError) {
            console.error(`Failed to update profile ${profile.id}:`, updateError);
            stats.errors++;
          } else {
            stats.processed++;
            console.log(`✅ Encrypted profile ${profile.id}`);
          }
        } catch (error) {
          console.error(`Failed to encrypt profile ${profile.id}:`, error);
          stats.errors++;
        }
      }

      console.log(`📊 Profile encryption batch complete: ${stats.processed}/${stats.total} processed, ${stats.errors} errors`);
      return stats;
    } catch (error) {
      console.error('Profile encryption failed:', error);
      return stats;
    }
  }

  /**
   * Encrypt all unencrypted chat messages
   */
  static async encryptChatMessages(): Promise<MigrationStats> {
    console.log('💬 Starting chat message encryption...');
    
    const stats: MigrationStats = {
      total: 0,
      processed: 0,
      errors: 0,
      table: 'chat_messages'
    };

    try {
      // Get unencrypted messages
      const { data: messages, error } = await supabase
        .from('chat_messages')
        .select('*')
        .or('is_encrypted.is.null,is_encrypted.eq.false')
        .limit(this.BATCH_SIZE);

      if (error) {
        console.error('Error fetching messages:', error);
        return stats;
      }

      if (!messages || messages.length === 0) {
        console.log('✅ All chat messages are already encrypted');
        return stats;
      }

      stats.total = messages.length;

      for (const message of messages) {
        try {
          if (message.message) {
            const encryptedMessage = await EncryptionService.encrypt(
              message.message,
              'chat_encryption_key'
            );

            const { error: updateError } = await supabase
              .from('chat_messages')
              .update({
                message: encryptedMessage,
                is_encrypted: true,
                encryption_key_id: 'chat_encryption_key'
              })
              .eq('id', message.id);

            if (updateError) {
              console.error(`Failed to update message ${message.id}:`, updateError);
              stats.errors++;
            } else {
              stats.processed++;
              console.log(`✅ Encrypted message ${message.id}`);
            }
          }
        } catch (error) {
          console.error(`Failed to encrypt message ${message.id}:`, error);
          stats.errors++;
        }
      }

      console.log(`📊 Chat message encryption batch complete: ${stats.processed}/${stats.total} processed, ${stats.errors} errors`);
      return stats;
    } catch (error) {
      console.error('Chat message encryption failed:', error);
      return stats;
    }
  }

  /**
   * Encrypt all unencrypted inquiries
   */
  static async encryptInquiries(): Promise<MigrationStats> {
    console.log('📝 Starting inquiry encryption...');
    
    const stats: MigrationStats = {
      total: 0,
      processed: 0,
      errors: 0,
      table: 'inquiries'
    };

    try {
      const { data: inquiries, error } = await supabase
        .from('inquiries')
        .select('*')
        .or('encrypted_fields.is.null,encrypted_fields.eq.{}')
        .limit(this.BATCH_SIZE);

      if (error) {
        console.error('Error fetching inquiries:', error);
        return stats;
      }

      if (!inquiries || inquiries.length === 0) {
        console.log('✅ All inquiries are already encrypted');
        return stats;
      }

      stats.total = inquiries.length;

      for (const inquiry of inquiries) {
        try {
          const fieldsToEncrypt = ['email', 'phone', 'message', 'company'];
          const encryptedData = await EncryptionService.encryptFields(
            inquiry,
            fieldsToEncrypt,
            'profile_encryption_key'
          );

          const { error: updateError } = await supabase
            .from('inquiries')
            .update({
              encrypted_fields: encryptedData.encrypted_fields,
              encryption_version: 1
            })
            .eq('id', inquiry.id);

          if (updateError) {
            console.error(`Failed to update inquiry ${inquiry.id}:`, updateError);
            stats.errors++;
          } else {
            stats.processed++;
            console.log(`✅ Encrypted inquiry ${inquiry.id}`);
          }
        } catch (error) {
          console.error(`Failed to encrypt inquiry ${inquiry.id}:`, error);
          stats.errors++;
        }
      }

      console.log(`📊 Inquiry encryption batch complete: ${stats.processed}/${stats.total} processed, ${stats.errors} errors`);
      return stats;
    } catch (error) {
      console.error('Inquiry encryption failed:', error);
      return stats;
    }
  }

  /**
   * Run all encryption migrations in sequence
   */
  static async encryptAllData(): Promise<{
    profiles: MigrationStats;
    chatMessages: MigrationStats;
    inquiries: MigrationStats;
    summary: {
      totalProcessed: number;
      totalErrors: number;
    };
  }> {
    console.log('🚀 Starting comprehensive data encryption...');

    const profiles = await this.encryptProfiles();
    await new Promise(resolve => setTimeout(resolve, 1000)); // Brief pause

    const chatMessages = await this.encryptChatMessages();
    await new Promise(resolve => setTimeout(resolve, 1000)); // Brief pause

    const inquiries = await this.encryptInquiries();

    const summary = {
      totalProcessed: profiles.processed + chatMessages.processed + inquiries.processed,
      totalErrors: profiles.errors + chatMessages.errors + inquiries.errors
    };

    console.log('\n🎉 Data encryption completed!');
    console.log(`📊 Summary: ${summary.totalProcessed} records encrypted, ${summary.totalErrors} errors`);

    return {
      profiles,
      chatMessages,
      inquiries,
      summary
    };
  }

  /**
   * Check encryption status across all tables
   */
  static async getEncryptionStatus(): Promise<{
    profiles: { total: number; encrypted: number };
    chatMessages: { total: number; encrypted: number };
    inquiries: { total: number; encrypted: number };
  }> {
    const results = {
      profiles: { total: 0, encrypted: 0 },
      chatMessages: { total: 0, encrypted: 0 },
      inquiries: { total: 0, encrypted: 0 }
    };

    try {
      // Check profiles
      const { data: allProfiles } = await supabase
        .from('profiles')
        .select('id, encrypted_fields');
      
      if (allProfiles) {
        results.profiles.total = allProfiles.length;
        results.profiles.encrypted = allProfiles.filter(p => 
          p.encrypted_fields && Object.keys(p.encrypted_fields).length > 0
        ).length;
      }

      // Check chat messages
      const { data: allMessages } = await supabase
        .from('chat_messages')
        .select('id, is_encrypted');
      
      if (allMessages) {
        results.chatMessages.total = allMessages.length;
        results.chatMessages.encrypted = allMessages.filter(m => m.is_encrypted === true).length;
      }

      // Check inquiries
      const { data: allInquiries } = await supabase
        .from('inquiries')
        .select('id, encrypted_fields');
      
      if (allInquiries) {
        results.inquiries.total = allInquiries.length;
        results.inquiries.encrypted = allInquiries.filter(i => 
          i.encrypted_fields && Object.keys(i.encrypted_fields).length > 0
        ).length;
      }

    } catch (error) {
      console.error('Error checking encryption status:', error);
    }

    return results;
  }
} 