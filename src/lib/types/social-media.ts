// Social Media Planning Types

export interface SocialMediaAccount {
  id: string;
  user_id: string;
  platform: 'instagram' | 'facebook' | 'twitter' | 'linkedin';
  platform_user_id: string;
  username: string;
  display_name: string;
  avatar_url?: string;
  access_token: string;
  refresh_token?: string;
  token_expires_at?: string;
  is_active: boolean;
  scopes: string[];
  account_type: 'personal' | 'business' | 'creator';
  created_at: string;
  updated_at: string;
}

export interface SocialMediaPost {
  id: string;
  user_id: string;
  account_id: string;
  platform: 'instagram' | 'facebook' | 'twitter' | 'linkedin';
  post_type: 'image' | 'video' | 'carousel' | 'story' | 'reel' | 'text';
  status: 'draft' | 'scheduled' | 'published' | 'failed' | 'cancelled';
  
  // Content
  caption: string;
  media_urls: string[];
  hashtags: string[];
  mentions: string[];
  
  // Scheduling
  scheduled_at?: string;
  published_at?: string;
  
  // Platform-specific data
  platform_post_id?: string;
  platform_data?: Record<string, unknown>;
  
  // Metrics (populated after publishing)
  likes_count?: number;
  comments_count?: number;
  shares_count?: number;
  views_count?: number;
  engagement_rate?: number;
  
  created_at: string;
  updated_at: string;
}

export interface InstagramMediaObject {
  id: string;
  media_type: 'IMAGE' | 'VIDEO' | 'CAROUSEL_ALBUM';
  media_url: string;
  thumbnail_url?: string;
  caption?: string;
  permalink: string;
  timestamp: string;
  like_count?: number;
  comments_count?: number;
}

export interface FacebookPage {
  id: string;
  name: string;
  access_token: string;
  category: string;
  instagram_business_account?: {
    id: string;
    username: string;
  };
}

export interface MediaPlannerCalendarEvent {
  id: string;
  post_id: string;
  title: string;
  start: string;
  end: string;
  platform: 'instagram' | 'facebook' | 'twitter' | 'linkedin';
  status: 'draft' | 'scheduled' | 'published' | 'failed';
  account_username: string;
  media_preview?: string;
}

export interface SocialMediaAnalytics {
  account_id: string;
  platform: 'instagram' | 'facebook' | 'twitter' | 'linkedin';
  period: 'day' | 'week' | 'month' | 'year';
  metrics: {
    followers_count: number;
    posts_count: number;
    total_likes: number;
    total_comments: number;
    total_shares: number;
    engagement_rate: number;
    reach: number;
    impressions: number;
  };
  top_posts: Array<{
    id: string;
    caption: string;
    media_url: string;
    likes: number;
    comments: number;
    engagement_rate: number;
  }>;
  date: string;
}

// API Response Types
export interface InstagramBasicApiResponse {
  data: InstagramMediaObject[];
  paging?: {
    cursors: {
      before: string;
      after: string;
    };
    next?: string;
    previous?: string;
  };
}

export interface FacebookPagesApiResponse {
  data: FacebookPage[];
  paging?: {
    cursors: {
      before: string;
      after: string;
    };
  };
}

// Hook Types
export interface UseSocialMediaAccounts {
  accounts: SocialMediaAccount[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  connectAccount: (platform: string) => Promise<void>;
  disconnectAccount: (accountId: string) => Promise<void>;
}

export interface UseSocialMediaPosts {
  posts: SocialMediaPost[];
  loading: boolean;
  error: string | null;
  createPost: (postData: Partial<SocialMediaPost>) => Promise<SocialMediaPost | null>;
  updatePost: (postId: string, postData: Partial<SocialMediaPost>) => Promise<SocialMediaPost | null>;
  deletePost: (postId: string) => Promise<boolean>;
  schedulePost: (postId: string, scheduledAt: string) => Promise<boolean>;
  publishPost: (postId: string) => Promise<boolean>;
  refetch: () => Promise<void>;
}

// Form Types
export interface PostCreationFormData {
  platform: 'instagram' | 'facebook' | 'twitter' | 'linkedin';
  account_id: string;
  post_type: 'image' | 'video' | 'carousel' | 'story' | 'reel' | 'text';
  caption: string;
  media_files: File[];
  hashtags: string[];
  mentions: string[];
  scheduled_at?: string;
  is_draft: boolean;
}

export interface MediaPlannerSettings {
  default_hashtags: Record<string, string[]>; // platform -> hashtags
  auto_schedule_optimal_times: boolean;
  optimal_posting_times: Record<string, string[]>; // platform -> times
  content_approval_required: boolean;
  analytics_tracking_enabled: boolean;
} 