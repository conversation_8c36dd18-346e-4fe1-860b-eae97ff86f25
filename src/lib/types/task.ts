export interface Task {
  id: string;
  title: string;
  description?: string;
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high';
  due_date?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface KanbanColumn {
  id: string;
  title: string;
  tasks: Task[];
}

export interface CalendarEvent extends Task {
  allDay: boolean;
}

export interface GanttTask extends Task {
  dependencies: string[];
  progress: number;
  duration: number; // in days
}