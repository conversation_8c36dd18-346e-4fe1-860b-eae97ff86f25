'use client';

import { createContext, useContext, useEffect, useState, useRef } from 'react';
import { User } from '@supabase/supabase-js';
import { supabase, getSessionSafe, db, Profile } from '../supabase';

interface AuthContextType {
  user: User | null;
  profile: Profile | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  logout: () => Promise<void>;
  isAdmin: boolean;
}

const AuthContext = createContext<AuthContextType>({} as AuthContextType);

export const useAuth = () => {
  return useContext(AuthContext);
};

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  
  // Use refs to track initialization state and prevent duplicate calls
  const isInitialized = useRef(false);
  const lastAuthEvent = useRef<string>('');
  const currentUser = useRef<User | null>(null);
  const authStateSubscription = useRef<{ data: { subscription: { unsubscribe: () => void } } } | null>(null);

  // Quick initial check for existing session to reduce perceived loading time
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Skip auth initialization if we're on the auth callback page
      // Let the callback page handle the auth exchange to prevent race conditions
      if (window.location.pathname === '/auth/callback') {
        console.log('Skipping auth initialization on callback page...');
        setLoading(false);
        return;
      }

      // Check if we just came from a successful auth callback
      const callbackComplete = sessionStorage.getItem('auth-callback-complete');
      if (callbackComplete) {
        console.log('Detected completed auth callback, preparing for initialization...');
        sessionStorage.removeItem('auth-callback-complete');
        // Force re-initialization after callback
        isInitialized.current = false;
        // Don't set loading to false here - let the main effect handle it
      } else {
        const hasAuthToken = localStorage.getItem('supabase.auth.token');
        if (!hasAuthToken) {
          // No token found, set loading to false immediately for faster UX
          setLoading(false);
          setUser(null);
          setProfile(null);
          setIsAdmin(false);
          isInitialized.current = true;
        }
      }
    }
  }, []);

  const loadUserProfile = async (userId: string, isNewSignIn = false) => {
    try {
      const userProfile = await db.getProfile(userId);
      
      if (userProfile) {
        setProfile(userProfile);
        setIsAdmin(userProfile.role === 'admin'); // Set admin status based on profile
      } else if (isNewSignIn) {
        // Auto-create profile for new users
        const { data: userData } = await supabase.auth.getUser();
        if (userData.user?.email) {
          const newProfile = await db.createProfile(
            userId, 
            userData.user.email,
            userData.user.user_metadata?.full_name || userData.user.user_metadata?.name
          );
          setProfile(newProfile);
          setIsAdmin(newProfile?.role === 'admin');
        }
      }
      
      // Always set loading to false after profile loading attempt
      setLoading(false);
    } catch {
      setProfile(null);
      setIsAdmin(false);
      setLoading(false);
      throw new Error('Failed to load profile');
    }
  };

  useEffect(() => {
    let mounted = true;
    let retryCount = 0;
    const maxRetries = 1; // Reduced from 2 to 1 for faster experience

    const initializeAuth = async () => {
      // Skip auth initialization if we're on the auth callback page
      // Let the callback page handle the auth exchange to prevent race conditions
      if (typeof window !== 'undefined' && window.location.pathname === '/auth/callback') {
        console.log('Skipping AuthContext initialization on callback page...');
        setLoading(false);
        return;
      }

      if (isInitialized.current) {
        console.log('Auth already initialized, skipping...');
        return;
      }

      // Check if we just came from a callback and need to force initialization
      const callbackComplete = typeof window !== 'undefined' && sessionStorage.getItem('auth-callback-complete');
      if (callbackComplete) {
        console.log('Initializing auth after callback completion...');
        sessionStorage.removeItem('auth-callback-complete');
        isInitialized.current = false; // Force re-init
      }

      try {
        console.log('Initializing auth...');
        
        // Use the optimized session getter with faster timeout
        const { data: { session }, error } = await Promise.race([
          getSessionSafe(),
          new Promise<never>((_, reject) => 
            setTimeout(() => reject(new Error('Session timeout')), 8000) // Reduced from 20s to 8s
          )
        ]);

        if (error) {
          console.error('Error getting session:', error);

          // Retry logic for session retrieval only for network-related errors
          const errorMessage = error instanceof Error ? error.message : String(error);
          if (retryCount < maxRetries && 
              (errorMessage.includes('timeout') || errorMessage.includes('network') || errorMessage.includes('fetch'))) {
            retryCount++;
            console.log(`Auth error, retrying... (${retryCount}/${maxRetries}): ${errorMessage}`);
            setTimeout(() => initializeAuth(), 1000 * retryCount); // Reduced delay
            return;
          }
          
          console.log('Auth initialization failed, continuing without session');
          if (mounted) {
            setUser(null);
            currentUser.current = null;
            setProfile(null);
            setIsAdmin(false);
            setLoading(false);
            isInitialized.current = true;
          }
          return;
        }

        console.log('Session retrieved:', !!session?.user);

        if (mounted) {
          const newUser = session?.user ?? null;
          setUser(newUser);
          currentUser.current = newUser;

          if (session?.user) {
            // For initialization, also try to create profile if it doesn't exist
            await loadUserProfile(session.user.id, true);
          } else {
            setProfile(null);
            setIsAdmin(false);
            setLoading(false);
          }
          
          isInitialized.current = true;
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        
        // Retry for network-related errors only
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (retryCount < maxRetries && 
            (errorMessage.includes('timeout') || errorMessage.includes('network') || errorMessage.includes('fetch'))) {
          retryCount++;
          console.log(`Auth initialization failed, retrying... (${retryCount}/${maxRetries}): ${errorMessage}`);
          setTimeout(() => initializeAuth(), 1000 * retryCount); // Reduced delay
          return;
        }
        
        console.log('Auth initialization failed permanently, continuing without session');
        if (mounted) {
          setUser(null);
          currentUser.current = null;
          setProfile(null);
          setIsAdmin(false);
          setLoading(false);
          isInitialized.current = true;
        }
      }
    };

    // Initialize auth state
    initializeAuth();

    // Listen for auth callback completion with immediate initialization
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'auth-callback-complete' && e.newValue === 'true') {
        console.log('Detected auth callback completion via storage event');
        // Force immediate re-initialization
        isInitialized.current = false;
        
        // Wait a bit longer to ensure we're no longer on the callback page
        const checkAndInit = () => {
          if (typeof window !== 'undefined' && window.location.pathname !== '/auth/callback') {
            console.log('Initializing auth after callback completion...');
            initializeAuth();
          } else {
            // Still on callback page, wait a bit more
            setTimeout(checkAndInit, 100);
          }
        };
        
        setTimeout(checkAndInit, 200); // Give enough time for navigation
      }
    };

    const handleAuthCallbackComplete = () => {
      console.log('Detected auth callback completion via custom event');
      // Force immediate re-initialization
      isInitialized.current = false;
      
      // Wait a bit longer to ensure we're no longer on the callback page
      const checkAndInit = () => {
        if (typeof window !== 'undefined' && window.location.pathname !== '/auth/callback') {
          console.log('Initializing auth after callback completion...');
          initializeAuth();
        } else {
          // Still on callback page, wait a bit more
          setTimeout(checkAndInit, 100);
        }
      };
      
      setTimeout(checkAndInit, 200); // Give enough time for navigation
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('storage', handleStorageChange);
      window.addEventListener('auth-callback-complete', handleAuthCallbackComplete);
    }

    // Listen for auth changes with better error handling and deduplication
    authStateSubscription.current = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!mounted) return;

        // Skip auth state changes if we're on the auth callback page
        // Let the callback page handle its own auth flow
        if (typeof window !== 'undefined' && window.location.pathname === '/auth/callback') {
          return;
        }

        // Only process auth state changes after initialization is complete
        if (!isInitialized.current) {
          console.log('Skipping auth state change until initialization is complete');
          return;
        }

        // Prevent duplicate events
        const eventKey = `${event}_${session?.user?.id || 'null'}`;
        if (lastAuthEvent.current === eventKey && event !== 'TOKEN_REFRESHED') {
          return;
        }
        lastAuthEvent.current = eventKey;

        console.log('Processing auth state change:', event, !!session);

        // Handle specific auth events
        if (event === 'SIGNED_IN') {
          setUser(session?.user || null);
          setLoading(false);
          
          if (session?.user) {
            try {
              // For new sign-ins, especially Google OAuth, try to create profile if it doesn't exist
              const isNewSignIn = true; // Assume new sign-in for proper profile creation
              await loadUserProfile(session.user.id, isNewSignIn);
            } catch {
              // Profile loading failed, but user is still signed in
            }
          }
        } else if (event === 'SIGNED_OUT') {
          setUser(null);
          setProfile(null);
          setIsAdmin(false);
          setLoading(false);
        } else if (event === 'TOKEN_REFRESHED') {
          setUser(session?.user || null);
          if (session?.user && !profile) {
            try {
              await loadUserProfile(session.user.id);
            } catch {
              // Profile loading failed
            }
          }
        } else if (event === 'USER_UPDATED') {
          setUser(session?.user || null);
        }
      }
    );

    // Less aggressive auto-refresh - increased interval for better performance
    const refreshInterval = setInterval(async () => {
      if (mounted && !document.hidden && isInitialized.current && currentUser.current) {
        try {
          // Use optimized session getter
          const { data } = await getSessionSafe();
          if (data.session) {
            console.log('Session auto-refresh check: active');
          } else {
            console.log('Session auto-refresh check: no active session');
          }
        } catch (error) {
          console.warn('Session refresh check error:', error instanceof Error ? error.message : 'Unknown error');
        }
      }
    }, 30 * 60 * 1000); // Increased from 15 to 30 minutes for better performance

    return () => {
      mounted = false;
      if (authStateSubscription.current) {
        authStateSubscription.current.data.subscription.unsubscribe();
      }
      clearInterval(refreshInterval);
      if (typeof window !== 'undefined') {
        window.removeEventListener('storage', handleStorageChange);
        window.removeEventListener('auth-callback-complete', handleAuthCallbackComplete);
      }
    };
  }, [profile]);

  const signIn = async (email: string, password: string) => {
    try {
      if (!email || !password) {
        throw new Error('Email and password are required');
      }

      setLoading(true);

      const { data, error } = await supabase.auth.signInWithPassword({
        email: email.trim().toLowerCase(),
        password
      });

      if (error) {
        if (error.message.includes('Invalid login credentials')) {
          throw new Error('Invalid email or password. Please check your credentials and try again.');
        }

        if (error.message.includes('Email not confirmed')) {
          throw new Error('Please check your email and click the confirmation link before signing in.');
        }

        if (error.message.includes('Too many requests')) {
          throw new Error('Too many login attempts. Please wait a moment and try again.');
        }

        throw new Error(error.message || 'Failed to sign in');
      }

      if (!data.user) {
        throw new Error('Sign in failed. Please try again.');
      }

    } catch (error) {
      throw error as Error;
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email: string, password: string) => {
    try {
      if (!email || !password) {
        throw new Error('Email and password are required');
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        throw new Error('Please enter a valid email address');
      }

      if (password.length < 6) {
        throw new Error('Password must be at least 6 characters long');
      }

      setLoading(true);

      const { data, error } = await supabase.auth.signUp({
        email: email.trim().toLowerCase(),
        password,
        options: {
          emailRedirectTo: `${typeof window !== 'undefined' ? window.location.origin : 'https://hieltech-v-next.vercel.app'}/confirm-email`,
          data: { display_name: email.split('@')[0] }
        }
      });

      if (error) {
        if (error.message.includes('email address is already registered')) {
          throw new Error('An account with this email already exists. Please sign in instead.');
        }

        if (error.message.includes('Password should be at least 6 characters')) {
          throw new Error('Password must be at least 6 characters long');
        }

        if (error.message.includes('Invalid email')) {
          throw new Error('Please enter a valid email address');
        }

        if (error.message.includes('rate limit')) {
          throw new Error('Too many attempts. Please wait a moment and try again.');
        }

        throw new Error(error.message || 'Failed to create account');
      }

      if (data.user && !data.session) {
        // Email confirmation required
        throw new Error('CONFIRMATION_REQUIRED');
      }

    } catch (error) {
      throw error as Error;
    } finally {
      setLoading(false);
    }
  };

  // Google Sign In Implementation
  const signInWithGoogle = async () => {
    try {
      setLoading(true);
      console.log('Initiating Google OAuth...');

      const redirectUrl = `${typeof window !== 'undefined' ? window.location.origin : 'https://hieltech-v-next.vercel.app'}/auth/callback`;
      console.log('OAuth redirect URL:', redirectUrl);

      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: redirectUrl,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
          skipBrowserRedirect: false,
        }
      });

      if (error) {
        console.error('Google OAuth initiation error:', error);
        
        // Provide user-friendly error messages for Google auth
        if (error.message.includes('popup closed') || error.message.includes('cancelled')) {
          throw new Error('Google sign-in was cancelled. Please try again.');
        }

        if (error.message.includes('popup blocked')) {
          throw new Error('Pop-up was blocked. Please allow pop-ups and try again.');
        }

        if (error.message.includes('network')) {
          throw new Error('Network error. Please check your connection and try again.');
        }

        throw new Error(error.message || 'Failed to sign in with Google');
      }

      console.log('Google OAuth initiated successfully, redirecting...');
      // Success case - user will be redirected to Google OAuth
      
    } catch (error) {
      console.error('Google sign-in error:', error);
      throw error as Error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      console.log('Starting logout process...');
      setLoading(true);

      // Clear local state first for immediate UI update
      setUser(null);
      setProfile(null);
      setIsAdmin(false);
      currentUser.current = null;

      // Sign out from Supabase
      const { error } = await Promise.race([
        supabase.auth.signOut({ scope: 'global' }),
        new Promise<never>((_, reject) => 
          setTimeout(() => reject(new Error('Logout timeout')), 5000) // 5 second timeout
        )
      ]);
      
      if (error) {
        console.error('Supabase logout error:', error);
        // Continue with cleanup even if signOut fails
      }

      // Clear only Supabase auth data from localStorage
      if (typeof window !== 'undefined') {
        // Clear specific Supabase auth keys
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith('supabase.auth.')) {
            localStorage.removeItem(key);
          }
        });
      }

      console.log('Logout completed successfully');
      
    } catch (error) {
      console.error('Logout error:', error);
      
      // Force cleanup as fallback
      setUser(null);
      setProfile(null);
      setIsAdmin(false);
      currentUser.current = null;
      
      // Fallback localStorage cleanup (only Supabase keys)
      if (typeof window !== 'undefined') {
        try {
          Object.keys(localStorage).forEach(key => {
            if (key.startsWith('supabase.auth.')) {
              localStorage.removeItem(key);
            }
          });
        } catch (storageError) {
          console.error('Failed to clear localStorage:', storageError);
        }
      }
      
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthContext.Provider value={{ user, profile, loading, signIn, signUp, signInWithGoogle, logout, isAdmin }}>
      {children}
    </AuthContext.Provider>
  );
};