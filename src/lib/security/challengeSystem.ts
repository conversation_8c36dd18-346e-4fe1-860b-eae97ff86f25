import { supabase } from '../supabase';

export interface PuzzleChallenge {
  id: string;
  type: 'drag_drop' | 'slider' | 'pattern_match';
  targetPosition: { x: number; y: number };
  currentPosition: { x: number; y: number };
  tolerance: number;
  instructions: string;
  theme: 'light' | 'dark';
}

export interface ChallengeResult {
  success: boolean;
  challengeId: string;
  attemptsRemaining: number;
  message: string;
}

export class SecurityChallengeSystem {
  private static readonly CHALLENGE_EXPIRY_MINUTES = 10;
  private static readonly MAX_ATTEMPTS = 3;
  private static readonly TOLERANCE_PIXELS = 25; // Increased tolerance for better UX

  /**
   * Generate a new security challenge for the user
   */
  static async generateChallenge(userIP: string): Promise<PuzzleChallenge> {
    // Clean up any existing expired challenges for this IP
    await this.cleanupExpiredChallenges(userIP);

    // Generate puzzle data
    const challengeType = this.getRandomChallengeType();
    const puzzleData = this.generatePuzzleData(challengeType);
    
    const challenge: PuzzleChallenge = {
      id: crypto.randomUUID(),
      type: challengeType,
      targetPosition: puzzleData.target,
      currentPosition: puzzleData.start,
      tolerance: this.TOLERANCE_PIXELS,
      instructions: this.getInstructions(challengeType),
      theme: 'light'
    };

    // Store challenge in database
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + this.CHALLENGE_EXPIRY_MINUTES);

    const { error } = await supabase
      .from('security_challenges')
      .insert({
        id: challenge.id,
        user_ip: userIP,
        challenge_type: 'puzzle',
        challenge_data: {
          type: challenge.type,
          currentPosition: challenge.currentPosition,
          targetPosition: challenge.targetPosition, // Include target for UI hints
          tolerance: challenge.tolerance,
          instructions: challenge.instructions,
          theme: challenge.theme
        },
        solution_data: {
          targetPosition: challenge.targetPosition,
          tolerance: challenge.tolerance
        },
        max_attempts: this.MAX_ATTEMPTS,
        expires_at: expiresAt.toISOString()
      });

    if (error) {
      console.error('Error storing challenge:', error);
      throw new Error('Failed to generate security challenge');
    }

    return challenge;
  }

  /**
   * Verify a challenge solution
   */
  static async verifyChallenge(
    challengeId: string, 
    userIP: string, 
    submittedPosition: { x: number; y: number }
  ): Promise<ChallengeResult> {
    // Get challenge from database
    const { data: challenge, error } = await supabase
      .from('security_challenges')
      .select('*')
      .eq('id', challengeId)
      .eq('user_ip', userIP)
      .single();

    if (error || !challenge) {
      return {
        success: false,
        challengeId,
        attemptsRemaining: 0,
        message: 'Challenge not found or expired'
      };
    }

    // Check if challenge is expired
    if (new Date(challenge.expires_at) < new Date()) {
      await this.cleanupExpiredChallenges(userIP);
      return {
        success: false,
        challengeId,
        attemptsRemaining: 0,
        message: 'Challenge has expired. Please refresh and try again.'
      };
    }

    // Check if already solved
    if (challenge.is_solved) {
      return {
        success: true,
        challengeId,
        attemptsRemaining: challenge.max_attempts - challenge.attempts,
        message: 'Challenge already completed'
      };
    }

    // Check attempts
    if (challenge.attempts >= challenge.max_attempts) {
      await this.logSecurityEvent(userIP, 'challenge_failed', {
        challengeId,
        reason: 'max_attempts_exceeded',
        attempts: challenge.attempts
      }, 'medium');

      return {
        success: false,
        challengeId,
        attemptsRemaining: 0,
        message: 'Maximum attempts exceeded. Please refresh and try again.'
      };
    }

    // Verify solution
    const targetPosition = challenge.solution_data.targetPosition;
    const tolerance = challenge.solution_data.tolerance;
    
    const distance = Math.sqrt(
      Math.pow(submittedPosition.x - targetPosition.x, 2) + 
      Math.pow(submittedPosition.y - targetPosition.y, 2)
    );

    const isCorrect = distance <= tolerance;
    const newAttempts = challenge.attempts + 1;

    // Update challenge
    const { error: updateError } = await supabase
      .from('security_challenges')
      .update({
        attempts: newAttempts,
        is_solved: isCorrect,
        updated_at: new Date().toISOString()
      })
      .eq('id', challengeId);

    if (updateError) {
      console.error('Error updating challenge:', updateError);
    }

    // Log the attempt
    await this.logSecurityEvent(userIP, 'challenge_failed', {
      challengeId,
      success: isCorrect,
      attempts: newAttempts,
      distance: Math.round(distance),
      tolerance
    }, isCorrect ? 'low' : 'medium');

    return {
      success: isCorrect,
      challengeId,
      attemptsRemaining: challenge.max_attempts - newAttempts,
      message: isCorrect 
        ? 'Challenge completed successfully!' 
        : `Incorrect position. ${challenge.max_attempts - newAttempts} attempts remaining.`
    };
  }

  /**
   * Check if user has a valid solved challenge
   */
  static async hasValidChallenge(userIP: string): Promise<boolean> {
    const { data: challenges, error } = await supabase
      .from('security_challenges')
      .select('*')
      .eq('user_ip', userIP)
      .eq('is_solved', true)
      .gt('expires_at', new Date().toISOString())
      .order('created_at', { ascending: false })
      .limit(1);

    if (error) {
      console.error('Error checking valid challenge:', error);
      return false;
    }

    return challenges && challenges.length > 0;
  }

  private static getRandomChallengeType(): 'drag_drop' | 'slider' | 'pattern_match' {
    const types: ('drag_drop' | 'slider' | 'pattern_match')[] = ['drag_drop', 'slider', 'pattern_match'];
    return types[Math.floor(Math.random() * types.length)];
  }

  private static generatePuzzleData(type: string) {
    // Generate more predictable and user-friendly positions
    switch (type) {
      case 'drag_drop':
        return {
          start: { x: 50, y: 50 }, // Always start top-left
          target: { x: 300, y: 200 } // Target bottom-right area
        };
      case 'slider':
        return {
          start: { x: 20, y: 120 }, // Start left side
          target: { x: 320, y: 120 } // Target right side (horizontal slider)
        };
      case 'pattern_match':
        return {
          start: { x: 80, y: 80 }, // Start near top-left
          target: { x: 280, y: 180 } // Target bottom-right
        };
      default:
        return {
          start: { x: 50, y: 50 },
          target: { x: 300, y: 200 }
        };
    }
  }

  private static getInstructions(type: string): string {
    switch (type) {
      case 'drag_drop':
        return 'Drag the blue circle to the green target area';
      case 'slider':
        return 'Slide the blue element all the way to the right';
      case 'pattern_match':
        return 'Move the diamond to match the target position';
      default:
        return 'Drag the element to the target area';
    }
  }

  private static async cleanupExpiredChallenges(userIP: string): Promise<void> {
    const { error } = await supabase
      .from('security_challenges')
      .delete()
      .eq('user_ip', userIP)
      .lt('expires_at', new Date().toISOString());

    if (error) {
      console.error('Error cleaning up expired challenges:', error);
    }
  }

  private static async logSecurityEvent(
    userIP: string, 
    eventType: string, 
    eventData: any, 
    severity: 'low' | 'medium' | 'high' | 'critical'
  ): Promise<void> {
    const { error } = await supabase
      .from('security_logs')
      .insert({
        user_ip: userIP,
        event_type: eventType,
        event_data: eventData,
        severity: severity
      });

    if (error) {
      console.error('Error logging security event:', error);
    }
  }
}
