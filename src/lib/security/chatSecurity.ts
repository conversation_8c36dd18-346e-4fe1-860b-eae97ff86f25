import { supabase } from '../supabase';

// Rate limiting configuration
const RATE_LIMITS = {
  MESSAGES_PER_HOUR: 50,
  MESSAGES_PER_MINUTE: 10,
  MAX_MESSAGE_LENGTH: 1000,
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_FILE_TYPES: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'application/pdf',
    'text/plain',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  ]
};

// Message content sanitization
export function sanitizeMessageContent(content: string): string {
  if (!content || typeof content !== 'string') {
    return '';
  }

  // Remove HTML tags
  let sanitized = content.replace(/<[^>]*>/g, '');
  
  // Remove potentially dangerous characters but keep basic punctuation and unicode
  sanitized = sanitized.replace(/[<>'"&]/g, '');
  
  // Limit length
  sanitized = sanitized.substring(0, RATE_LIMITS.MAX_MESSAGE_LENGTH);
  
  // Trim whitespace
  return sanitized.trim();
}

// Rate limiting check
export async function checkMessageRateLimit(userId: string): Promise<boolean> {
  try {
    // Check messages in the last hour
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString();
    const { data: hourlyMessages, error: hourlyError } = await supabase
      .from('chat_messages')
      .select('id')
      .eq('sender_id', userId)
      .gte('created_at', oneHourAgo);

    if (hourlyError) {
      console.error('Error checking hourly rate limit:', hourlyError);
      return false;
    }

    if (hourlyMessages && hourlyMessages.length >= RATE_LIMITS.MESSAGES_PER_HOUR) {
      return false;
    }

    // Check messages in the last minute
    const oneMinuteAgo = new Date(Date.now() - 60 * 1000).toISOString();
    const { data: minutelyMessages, error: minutelyError } = await supabase
      .from('chat_messages')
      .select('id')
      .eq('sender_id', userId)
      .gte('created_at', oneMinuteAgo);

    if (minutelyError) {
      console.error('Error checking minute rate limit:', minutelyError);
      return false;
    }

    return !minutelyMessages || minutelyMessages.length < RATE_LIMITS.MESSAGES_PER_MINUTE;
  } catch (error) {
    console.error('Error in rate limit check:', error);
    return false;
  }
}

// File validation
export function validateFile(file: File): { valid: boolean; error?: string } {
  // Check file size
  if (file.size > RATE_LIMITS.MAX_FILE_SIZE) {
    return {
      valid: false,
      error: `File size must be less than ${RATE_LIMITS.MAX_FILE_SIZE / (1024 * 1024)}MB`
    };
  }

  // Check file type
  if (!RATE_LIMITS.ALLOWED_FILE_TYPES.includes(file.type)) {
    return {
      valid: false,
      error: 'File type not allowed. Please use images, PDFs, or documents.'
    };
  }

  // Check file name for suspicious patterns
  const suspiciousPatterns = [
    /\.exe$/i,
    /\.bat$/i,
    /\.cmd$/i,
    /\.scr$/i,
    /\.vbs$/i,
    /\.js$/i,
    /\.jar$/i
  ];

  if (suspiciousPatterns.some(pattern => pattern.test(file.name))) {
    return {
      valid: false,
      error: 'File type not allowed for security reasons.'
    };
  }

  return { valid: true };
}

// Permission validation
export async function validateChatPermission(
  userId: string, 
  roomId: string
): Promise<{ allowed: boolean; reason?: string }> {
  try {
    // Check if user is a participant in the room
    const { data: participant, error: participantError } = await supabase
      .from('chat_room_participants')
      .select('*')
      .eq('room_id', roomId)
      .eq('user_id', userId)
      .single();

    if (participantError && participantError.code !== 'PGRST116') {
      console.error('Error checking room participation:', participantError);
      return { allowed: false, reason: 'Permission check failed' };
    }

    if (!participant) {
      return { allowed: false, reason: 'Not a participant in this room' };
    }

    // Check if the room is active
    const { data: room, error: roomError } = await supabase
      .from('chat_rooms')
      .select('is_active, type')
      .eq('id', roomId)
      .single();

    if (roomError) {
      console.error('Error checking room status:', roomError);
      return { allowed: false, reason: 'Room check failed' };
    }

    if (!room?.is_active) {
      return { allowed: false, reason: 'Room is not active' };
    }

    // Additional check for admin-only rooms
    if (room.type === 'admin_only') {
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('role, email')
        .eq('id', userId)
        .single();

      if (profileError) {
        console.error('Error checking admin status:', profileError);
        return { allowed: false, reason: 'Admin check failed' };
      }

      if (profile?.role !== 'admin' && profile?.email !== '<EMAIL>') {
        return { allowed: false, reason: 'Admin access required' };
      }
    }

    return { allowed: true };
  } catch (error) {
    console.error('Error in permission validation:', error);
    return { allowed: false, reason: 'Permission validation failed' };
  }
}

// Audit logging
export async function logChatAction(
  action: string,
  details: Record<string, unknown>,
  userId?: string
): Promise<void> {
  try {
    // This would typically be sent to your audit logging service
    // For now, we'll use console logging in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Chat Audit Log:', {
        timestamp: new Date().toISOString(),
        action,
        userId,
        details
      });
    }

    // In production, you might want to send this to an external logging service
    // or store it in a dedicated audit table
  } catch (error) {
    console.error('Error logging chat action:', error);
  }
}

// Enhanced message encryption using EncryptionService
import { EncryptionService } from '../services/encryptionService';

export class MessageEncryption {
  /**
   * Encrypt a message using the chat encryption key
   */
  static async encryptMessage(message: string): Promise<string> {
    try {
      return await EncryptionService.encrypt(message, 'chat_encryption_key');
    } catch (error) {
      console.error('Error encrypting message:', error);
      throw new Error('Failed to encrypt message');
    }
  }

  /**
   * Decrypt a message using the chat encryption key
   */
  static async decryptMessage(encryptedMessage: string): Promise<string> {
    try {
      return await EncryptionService.decrypt(encryptedMessage, 'chat_encryption_key');
    } catch (error) {
      console.error('Error decrypting message:', error);
      throw new Error('Failed to decrypt message');
    }
  }
}

// Updated message sanitization with encryption support
export async function sanitizeAndEncryptMessage(
  content: string, 
  shouldEncrypt: boolean = true
): Promise<string> {
  // First sanitize the content
  const sanitized = sanitizeMessageContent(content);
  
  // Then encrypt if requested
  if (shouldEncrypt) {
    return await MessageEncryption.encryptMessage(sanitized);
  }
  
  return sanitized;
}

// Content moderation
export function moderateContent(content: string): { 
  approved: boolean; 
  reason?: string; 
  sanitized: string 
} {
  const sanitized = sanitizeMessageContent(content);
  
  // Basic profanity filter (extend as needed)
  const profanityPatterns = [
    /\b(spam|scam|phishing)\b/gi,
    // Add more patterns as needed
  ];

  const hasProfanity = profanityPatterns.some(pattern => pattern.test(sanitized));
  
  if (hasProfanity) {
    return {
      approved: false,
      reason: 'Content contains inappropriate language',
      sanitized
    };
  }

  // Check for suspicious links
  const linkPattern = /(https?:\/\/[^\s]+)/gi;
  const links = sanitized.match(linkPattern);
  
  if (links && links.length > 3) {
    return {
      approved: false,
      reason: 'Too many links in message',
      sanitized
    };
  }

  return {
    approved: true,
    sanitized
  };
}

export { RATE_LIMITS };
