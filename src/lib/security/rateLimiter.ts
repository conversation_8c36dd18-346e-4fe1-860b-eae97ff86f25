import { supabase } from '../supabase';

export interface RateLimitConfig {
  endpoint: string;
  maxRequests: number;
  windowMinutes: number;
  blockDurationMinutes?: number;
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: Date;
  isBlocked: boolean;
  blockUntil?: Date;
  message: string;
}

export class RateLimiter {
  // Default rate limit configurations
  private static readonly DEFAULT_CONFIGS: Record<string, RateLimitConfig> = {
    '/login': {
      endpoint: '/login',
      maxRequests: 5,
      windowMinutes: 15,
      blockDurationMinutes: 30
    },
    '/signup': {
      endpoint: '/signup',
      maxRequests: 3,
      windowMinutes: 60,
      blockDurationMinutes: 60
    },
    '/auth/callback': {
      endpoint: '/auth/callback',
      maxRequests: 10,
      windowMinutes: 5,
      blockDurationMinutes: 15
    },
    '/api/challenge': {
      endpoint: '/api/challenge',
      maxRequests: 10,
      windowMinutes: 10,
      blockDurationMinutes: 20
    },
    'default': {
      endpoint: 'default',
      maxRequests: 20,
      windowMinutes: 60,
      blockDurationMinutes: 30
    }
  };

  /**
   * Check if a request is allowed based on rate limiting rules
   */
  static async checkRateLimit(userIP: string, endpoint: string): Promise<RateLimitResult> {
    const config = this.DEFAULT_CONFIGS[endpoint] || this.DEFAULT_CONFIGS['default'];
    
    // Clean up old rate limit records first
    await this.cleanupOldRecords();

    // Check if IP is currently blocked
    const blockCheck = await this.checkIfBlocked(userIP, endpoint);
    if (blockCheck.isBlocked) {
      return blockCheck;
    }

    // Get or create rate limit record
    const rateLimitRecord = await this.getRateLimitRecord(userIP, endpoint, config);
    
    if (!rateLimitRecord) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: new Date(),
        isBlocked: false,
        message: 'Rate limit check failed'
      };
    }

    const now = new Date();
    const windowStart = new Date(rateLimitRecord.window_start);
    const windowEnd = new Date(windowStart.getTime() + (config.windowMinutes * 60 * 1000));

    // Check if we're still in the current window
    if (now <= windowEnd) {
      // We're in the current window
      if (rateLimitRecord.request_count >= config.maxRequests) {
        // Rate limit exceeded - block the IP
        await this.blockIP(userIP, endpoint, config);
        
        // Log security event
        await this.logSecurityEvent(userIP, 'rate_limit_exceeded', {
          endpoint,
          requestCount: rateLimitRecord.request_count,
          maxRequests: config.maxRequests,
          windowMinutes: config.windowMinutes
        }, 'high');

        return {
          allowed: false,
          remaining: 0,
          resetTime: windowEnd,
          isBlocked: true,
          blockUntil: new Date(now.getTime() + ((config.blockDurationMinutes || 30) * 60 * 1000)),
          message: `Rate limit exceeded. Try again in ${config.blockDurationMinutes || 30} minutes.`
        };
      } else {
        // Increment request count
        await this.incrementRequestCount(rateLimitRecord.id);
        
        return {
          allowed: true,
          remaining: config.maxRequests - (rateLimitRecord.request_count + 1),
          resetTime: windowEnd,
          isBlocked: false,
          message: 'Request allowed'
        };
      }
    } else {
      // Window has expired, create new window
      await this.createNewWindow(userIP, endpoint, config);
      
      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime: new Date(now.getTime() + (config.windowMinutes * 60 * 1000)),
        isBlocked: false,
        message: 'Request allowed'
      };
    }
  }

  /**
   * Manually block an IP for suspicious activity
   */
  static async blockIPManually(
    userIP: string, 
    endpoint: string, 
    durationMinutes: number = 60,
    reason: string = 'Manual block'
  ): Promise<void> {
    const now = new Date();
    const blockUntil = new Date(now.getTime() + (durationMinutes * 60 * 1000));

    const { error } = await supabase
      .from('rate_limits')
      .upsert({
        user_ip: userIP,
        endpoint: endpoint,
        request_count: 999,
        window_start: now.toISOString(),
        window_duration_minutes: durationMinutes,
        max_requests: 0,
        is_blocked: true,
        blocked_until: blockUntil.toISOString()
      }, {
        onConflict: 'user_ip,endpoint'
      });

    if (error) {
      console.error('Error manually blocking IP:', error);
    }

    // Log the manual block
    await this.logSecurityEvent(userIP, 'suspicious_activity', {
      endpoint,
      reason,
      blockDuration: durationMinutes,
      manualBlock: true
    }, 'critical');
  }

  /**
   * Unblock an IP address
   */
  static async unblockIP(userIP: string, endpoint?: string): Promise<void> {
    const query = supabase
      .from('rate_limits')
      .update({
        is_blocked: false,
        blocked_until: null
      })
      .eq('user_ip', userIP);

    if (endpoint) {
      query.eq('endpoint', endpoint);
    }

    const { error } = await query;

    if (error) {
      console.error('Error unblocking IP:', error);
    }
  }

  /**
   * Get rate limit status for an IP
   */
  static async getRateLimitStatus(userIP: string, endpoint: string): Promise<RateLimitResult | null> {
    const config = this.DEFAULT_CONFIGS[endpoint] || this.DEFAULT_CONFIGS['default'];
    
    const { data: record, error } = await supabase
      .from('rate_limits')
      .select('*')
      .eq('user_ip', userIP)
      .eq('endpoint', endpoint)
      .single();

    if (error || !record) {
      return null;
    }

    const now = new Date();
    const windowStart = new Date(record.window_start);
    const windowEnd = new Date(windowStart.getTime() + (record.window_duration_minutes * 60 * 1000));

    return {
      allowed: !record.is_blocked && record.request_count < record.max_requests,
      remaining: Math.max(0, record.max_requests - record.request_count),
      resetTime: windowEnd,
      isBlocked: record.is_blocked,
      blockUntil: record.blocked_until ? new Date(record.blocked_until) : undefined,
      message: record.is_blocked ? 'IP is blocked' : 'Status retrieved'
    };
  }

  private static async checkIfBlocked(userIP: string, endpoint: string): Promise<RateLimitResult> {
    const { data: record, error } = await supabase
      .from('rate_limits')
      .select('*')
      .eq('user_ip', userIP)
      .eq('endpoint', endpoint)
      .eq('is_blocked', true)
      .single();

    if (error || !record) {
      return {
        allowed: true,
        remaining: 0,
        resetTime: new Date(),
        isBlocked: false,
        message: 'Not blocked'
      };
    }

    const now = new Date();
    const blockUntil = record.blocked_until ? new Date(record.blocked_until) : null;

    if (blockUntil && now >= blockUntil) {
      // Block has expired, unblock the IP
      await this.unblockIP(userIP, endpoint);
      return {
        allowed: true,
        remaining: 0,
        resetTime: new Date(),
        isBlocked: false,
        message: 'Block expired'
      };
    }

    return {
      allowed: false,
      remaining: 0,
      resetTime: new Date(),
      isBlocked: true,
      blockUntil: blockUntil || undefined,
      message: `IP blocked until ${blockUntil?.toLocaleString() || 'indefinitely'}`
    };
  }

  private static async getRateLimitRecord(userIP: string, endpoint: string, config: RateLimitConfig) {
    const { data: record, error } = await supabase
      .from('rate_limits')
      .select('*')
      .eq('user_ip', userIP)
      .eq('endpoint', endpoint)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error getting rate limit record:', error);
      return null;
    }

    if (!record) {
      // Create new record
      const { data: newRecord, error: insertError } = await supabase
        .from('rate_limits')
        .insert({
          user_ip: userIP,
          endpoint: endpoint,
          request_count: 1,
          window_start: new Date().toISOString(),
          window_duration_minutes: config.windowMinutes,
          max_requests: config.maxRequests,
          is_blocked: false
        })
        .select()
        .single();

      if (insertError) {
        console.error('Error creating rate limit record:', insertError);
        return null;
      }

      return newRecord;
    }

    return record;
  }

  private static async incrementRequestCount(recordId: string): Promise<void> {
    const { error } = await supabase
      .from('rate_limits')
      .update({
        request_count: supabase.rpc('increment_request_count', { record_id: recordId })
      })
      .eq('id', recordId);

    if (error) {
      console.error('Error incrementing request count:', error);
    }
  }

  private static async createNewWindow(userIP: string, endpoint: string, config: RateLimitConfig): Promise<void> {
    const { error } = await supabase
      .from('rate_limits')
      .upsert({
        user_ip: userIP,
        endpoint: endpoint,
        request_count: 1,
        window_start: new Date().toISOString(),
        window_duration_minutes: config.windowMinutes,
        max_requests: config.maxRequests,
        is_blocked: false,
        blocked_until: null
      }, {
        onConflict: 'user_ip,endpoint'
      });

    if (error) {
      console.error('Error creating new window:', error);
    }
  }

  private static async blockIP(userIP: string, endpoint: string, config: RateLimitConfig): Promise<void> {
    const blockDuration = config.blockDurationMinutes || 30;
    const blockUntil = new Date(Date.now() + (blockDuration * 60 * 1000));

    const { error } = await supabase
      .from('rate_limits')
      .update({
        is_blocked: true,
        blocked_until: blockUntil.toISOString()
      })
      .eq('user_ip', userIP)
      .eq('endpoint', endpoint);

    if (error) {
      console.error('Error blocking IP:', error);
    }
  }

  private static async cleanupOldRecords(): Promise<void> {
    const cutoffTime = new Date(Date.now() - (24 * 60 * 60 * 1000)); // 24 hours ago

    const { error } = await supabase
      .from('rate_limits')
      .delete()
      .lt('window_start', cutoffTime.toISOString())
      .eq('is_blocked', false);

    if (error) {
      console.error('Error cleaning up old rate limit records:', error);
    }
  }

  private static async logSecurityEvent(
    userIP: string, 
    eventType: string, 
    eventData: any, 
    severity: 'low' | 'medium' | 'high' | 'critical'
  ): Promise<void> {
    const { error } = await supabase
      .from('security_logs')
      .insert({
        user_ip: userIP,
        event_type: eventType,
        event_data: eventData,
        severity: severity
      });

    if (error) {
      console.error('Error logging security event:', error);
    }
  }
}
