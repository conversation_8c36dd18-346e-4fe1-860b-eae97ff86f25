export interface SecurityConfig {
  // Challenge settings
  challengeEnabled: boolean;
  challengeTypes: ('drag_drop' | 'slider' | 'pattern_match')[];
  challengeExpiryMinutes: number;
  maxChallengeAttempts: number;
  challengeTolerance: number;

  // Rate limiting settings
  rateLimitEnabled: boolean;
  loginRateLimit: {
    maxRequests: number;
    windowMinutes: number;
    blockDurationMinutes: number;
  };
  signupRateLimit: {
    maxRequests: number;
    windowMinutes: number;
    blockDurationMinutes: number;
  };
  apiRateLimit: {
    maxRequests: number;
    windowMinutes: number;
    blockDurationMinutes: number;
  };

  // Security monitoring
  logSecurityEvents: boolean;
  alertOnSuspiciousActivity: boolean;
  autoBlockSuspiciousIPs: boolean;
  suspiciousActivityThreshold: number;

  // IP whitelist/blacklist
  ipWhitelist: string[];
  ipBlacklist: string[];
  
  // Admin settings
  adminBypassSecurity: boolean;
  adminEmails: string[];
}

export const DEFAULT_SECURITY_CONFIG: SecurityConfig = {
  // Challenge settings
  challengeEnabled: true,
  challengeTypes: ['drag_drop', 'slider', 'pattern_match'],
  challengeExpiryMinutes: 10,
  maxChallengeAttempts: 3,
  challengeTolerance: 25,

  // Rate limiting settings
  rateLimitEnabled: true,
  loginRateLimit: {
    maxRequests: 5,
    windowMinutes: 15,
    blockDurationMinutes: 30
  },
  signupRateLimit: {
    maxRequests: 3,
    windowMinutes: 60,
    blockDurationMinutes: 60
  },
  apiRateLimit: {
    maxRequests: 10,
    windowMinutes: 10,
    blockDurationMinutes: 20
  },

  // Security monitoring
  logSecurityEvents: true,
  alertOnSuspiciousActivity: true,
  autoBlockSuspiciousIPs: true,
  suspiciousActivityThreshold: 10,

  // IP whitelist/blacklist
  ipWhitelist: [],
  ipBlacklist: [],
  
  // Admin settings
  adminBypassSecurity: false,
  adminEmails: ['<EMAIL>']
};

export class SecurityConfigManager {
  private static config: SecurityConfig = DEFAULT_SECURITY_CONFIG;

  /**
   * Get current security configuration
   */
  static getConfig(): SecurityConfig {
    return { ...this.config };
  }

  /**
   * Update security configuration
   */
  static updateConfig(updates: Partial<SecurityConfig>): SecurityConfig {
    this.config = { ...this.config, ...updates };
    return this.getConfig();
  }

  /**
   * Reset to default configuration
   */
  static resetToDefaults(): SecurityConfig {
    this.config = { ...DEFAULT_SECURITY_CONFIG };
    return this.getConfig();
  }

  /**
   * Check if IP is whitelisted
   */
  static isIPWhitelisted(ip: string): boolean {
    return this.config.ipWhitelist.includes(ip);
  }

  /**
   * Check if IP is blacklisted
   */
  static isIPBlacklisted(ip: string): boolean {
    return this.config.ipBlacklist.includes(ip);
  }

  /**
   * Check if user is admin and can bypass security
   */
  static canBypassSecurity(email?: string): boolean {
    if (!this.config.adminBypassSecurity || !email) {
      return false;
    }
    return this.config.adminEmails.includes(email);
  }

  /**
   * Add IP to whitelist
   */
  static addToWhitelist(ip: string): void {
    if (!this.config.ipWhitelist.includes(ip)) {
      this.config.ipWhitelist.push(ip);
    }
  }

  /**
   * Remove IP from whitelist
   */
  static removeFromWhitelist(ip: string): void {
    this.config.ipWhitelist = this.config.ipWhitelist.filter(whitelistedIP => whitelistedIP !== ip);
  }

  /**
   * Add IP to blacklist
   */
  static addToBlacklist(ip: string): void {
    if (!this.config.ipBlacklist.includes(ip)) {
      this.config.ipBlacklist.push(ip);
    }
  }

  /**
   * Remove IP from blacklist
   */
  static removeFromBlacklist(ip: string): void {
    this.config.ipBlacklist = this.config.ipBlacklist.filter(blacklistedIP => blacklistedIP !== ip);
  }

  /**
   * Get rate limit config for specific endpoint
   */
  static getRateLimitConfig(endpoint: string) {
    switch (endpoint) {
      case '/login':
        return this.config.loginRateLimit;
      case '/signup':
        return this.config.signupRateLimit;
      default:
        return this.config.apiRateLimit;
    }
  }

  /**
   * Validate security configuration
   */
  static validateConfig(config: Partial<SecurityConfig>): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (config.challengeExpiryMinutes !== undefined && config.challengeExpiryMinutes < 1) {
      errors.push('Challenge expiry must be at least 1 minute');
    }

    if (config.maxChallengeAttempts !== undefined && config.maxChallengeAttempts < 1) {
      errors.push('Max challenge attempts must be at least 1');
    }

    if (config.challengeTolerance !== undefined && (config.challengeTolerance < 5 || config.challengeTolerance > 50)) {
      errors.push('Challenge tolerance must be between 5 and 50 pixels');
    }

    if (config.loginRateLimit) {
      if (config.loginRateLimit.maxRequests < 1) {
        errors.push('Login rate limit max requests must be at least 1');
      }
      if (config.loginRateLimit.windowMinutes < 1) {
        errors.push('Login rate limit window must be at least 1 minute');
      }
    }

    if (config.signupRateLimit) {
      if (config.signupRateLimit.maxRequests < 1) {
        errors.push('Signup rate limit max requests must be at least 1');
      }
      if (config.signupRateLimit.windowMinutes < 1) {
        errors.push('Signup rate limit window must be at least 1 minute');
      }
    }

    if (config.suspiciousActivityThreshold !== undefined && config.suspiciousActivityThreshold < 1) {
      errors.push('Suspicious activity threshold must be at least 1');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Export configuration as JSON
   */
  static exportConfig(): string {
    return JSON.stringify(this.config, null, 2);
  }

  /**
   * Import configuration from JSON
   */
  static importConfig(configJson: string): { success: boolean; error?: string } {
    try {
      const importedConfig = JSON.parse(configJson);
      const validation = this.validateConfig(importedConfig);
      
      if (!validation.valid) {
        return {
          success: false,
          error: `Invalid configuration: ${validation.errors.join(', ')}`
        };
      }

      this.config = { ...DEFAULT_SECURITY_CONFIG, ...importedConfig };
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: 'Invalid JSON format'
      };
    }
  }

  /**
   * Get security statistics
   */
  static getSecurityStats() {
    return {
      challengeEnabled: this.config.challengeEnabled,
      rateLimitEnabled: this.config.rateLimitEnabled,
      whitelistedIPs: this.config.ipWhitelist.length,
      blacklistedIPs: this.config.ipBlacklist.length,
      adminEmails: this.config.adminEmails.length,
      challengeTypes: this.config.challengeTypes.length
    };
  }
}
