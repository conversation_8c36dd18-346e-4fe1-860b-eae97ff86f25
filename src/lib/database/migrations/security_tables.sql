-- Security Challenge System Tables
-- Run this in your Supabase SQL editor to create the security tables

-- Table for security challenges (puzzles, captchas, etc.)
CREATE TABLE IF NOT EXISTS security_challenges (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_ip INET NOT NULL,
    challenge_type VARCHAR(50) NOT NULL CHECK (challenge_type IN ('puzzle', 'captcha')),
    challenge_data JSONB NOT NULL,
    solution_data JSONB NOT NULL,
    is_solved BOOLEAN DEFAULT FALSE,
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for rate limiting
CREATE TABLE IF NOT EXISTS rate_limits (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_ip INET NOT NULL,
    endpoint VARCHAR(255) NOT NULL,
    request_count INTEGER DEFAULT 1,
    window_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    window_duration_minutes INTEGER DEFAULT 60,
    max_requests INTEGER DEFAULT 10,
    is_blocked BOOLEAN DEFAULT FALSE,
    blocked_until TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table for security logs
CREATE TABLE IF NOT EXISTS security_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_ip INET NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    event_type VARCHAR(100) NOT NULL CHECK (event_type IN ('login_attempt', 'challenge_failed', 'rate_limit_exceeded', 'suspicious_activity')),
    event_data JSONB,
    severity VARCHAR(20) DEFAULT 'low' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_security_challenges_ip_type ON security_challenges(user_ip, challenge_type);
CREATE INDEX IF NOT EXISTS idx_security_challenges_expires ON security_challenges(expires_at);
CREATE INDEX IF NOT EXISTS idx_rate_limits_ip_endpoint ON rate_limits(user_ip, endpoint);
CREATE INDEX IF NOT EXISTS idx_rate_limits_window ON rate_limits(window_start);
CREATE INDEX IF NOT EXISTS idx_security_logs_ip_time ON security_logs(user_ip, created_at);
CREATE INDEX IF NOT EXISTS idx_security_logs_event_type ON security_logs(event_type);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_security_challenges_updated_at 
    BEFORE UPDATE ON security_challenges 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rate_limits_updated_at 
    BEFORE UPDATE ON rate_limits 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to clean up expired challenges
CREATE OR REPLACE FUNCTION cleanup_expired_challenges()
RETURNS void AS $$
BEGIN
    DELETE FROM security_challenges 
    WHERE expires_at < NOW() - INTERVAL '1 hour';
    
    DELETE FROM rate_limits 
    WHERE window_start < NOW() - INTERVAL '24 hours';
    
    DELETE FROM security_logs 
    WHERE created_at < NOW() - INTERVAL '30 days';
END;
$$ language 'plpgsql';

-- Row Level Security (RLS) policies
ALTER TABLE security_challenges ENABLE ROW LEVEL SECURITY;
ALTER TABLE rate_limits ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_logs ENABLE ROW LEVEL SECURITY;

-- Policies for security_challenges
CREATE POLICY "Users can view their own challenges" ON security_challenges
    FOR SELECT USING (true); -- Allow reading for security middleware

CREATE POLICY "System can manage challenges" ON security_challenges
    FOR ALL USING (true); -- Allow full access for system operations

-- Policies for rate_limits
CREATE POLICY "System can manage rate limits" ON rate_limits
    FOR ALL USING (true);

-- Policies for security_logs (admin only)
CREATE POLICY "Admins can view security logs" ON security_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND (profiles.role = 'admin' OR profiles.email = '<EMAIL>')
        )
    );

CREATE POLICY "System can create security logs" ON security_logs
    FOR INSERT WITH CHECK (true);
