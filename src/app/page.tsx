import Link from "next/link";
import FeatureShowcase from "@/components/ui/FeatureShowcase";
import PricingCalculator from "@/components/ui/PricingCalculator";
import FeatureMap from "@/components/ui/FeatureMap";
import FloatingActionButton from "@/components/ui/FloatingActionButton";

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 md:py-28 lg:py-36 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold tracking-tight animate-fade-in">
              Transforming Ideas into <span className="text-blue-600 dark:text-blue-400">Digital Reality</span>
            </h1>
            <p className="mt-6 text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto animate-slide-up">
              Hiel Tech specializes in crafting exceptional software and websites that drive business growth and enhance user experiences.
            </p>
            <div className="mt-10 flex flex-col sm:flex-row gap-4 justify-center animate-slide-up" style={{ animationDelay: "0.2s" }}>
              <Link 
                href="/services" 
                className="px-8 py-3 rounded-full bg-blue-600 text-white font-medium hover:bg-blue-700 transition-colors"
              >
                Our Services
              </Link>
              <Link 
                href="/projects" 
                className="px-8 py-3 rounded-full border border-gray-300 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              >
                View Projects
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* SaaS Features Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 relative overflow-hidden">
        {/* Background Animation */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 left-10 w-20 h-20 bg-blue-500 rounded-full animate-pulse"></div>
          <div className="absolute top-40 right-20 w-16 h-16 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: "1s" }}></div>
          <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-indigo-500 rounded-full animate-pulse" style={{ animationDelay: "2s" }}></div>
          <div className="absolute bottom-40 right-1/3 w-8 h-8 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: "0.5s" }}></div>
        </div>

        <div className="container mx-auto relative z-10">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-900/30 rounded-full text-blue-600 dark:text-blue-400 text-sm font-medium mb-6">
              🚀 SaaS Platform Features
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Powerful Tools for Modern Businesses
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Everything you need to manage your digital presence, grow your business, and connect with your audience — all in one powerful platform.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {/* HielLinks Feature - Core User Feature */}
            <div className="group bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100 dark:border-gray-700 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-600/5 to-purple-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative z-10">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-2xl">🔗</span>
                </div>
                <h3 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">HielLinks</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                  Create beautiful, customizable link-in-bio pages with advanced analytics, QR codes, and location mapping. Perfect for social media optimization.
                </p>
                <div className="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                    Custom themes & branding
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                    Real-time analytics dashboard
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                    QR code generation
                  </div>
                </div>
              </div>
            </div>

            {/* User Analytics Dashboard - Coming Q2 2025 */}
            <div className="group bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100 dark:border-gray-700 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-green-600/5 to-emerald-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="absolute top-4 right-4 bg-gradient-to-r from-blue-500 to-indigo-500 text-white text-xs px-3 py-1 rounded-full font-medium">
                Q2 2025
              </div>
              <div className="relative z-10">
                <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-2xl">📊</span>
                </div>
                <h3 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">User Analytics</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                  Comprehensive analytics dashboard for your HielLinks profiles with detailed visitor insights, click tracking, and performance metrics.
                </p>
                <div className="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                    Real-time visitor tracking
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                    Click analytics & heatmaps
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                    Performance insights
                  </div>
                </div>
              </div>
            </div>

            {/* Community Platform - Coming Q3 2025 */}
            <div className="group bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100 dark:border-gray-700 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-600/5 to-pink-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="absolute top-4 right-4 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs px-3 py-1 rounded-full font-medium">
                Q3 2025
              </div>
              <div className="relative z-10">
                <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-2xl">💬</span>
                </div>
                <h3 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Community Platform</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                  Connect with other users through our community platform. Create discussion threads, share tips, and collaborate with fellow creators.
                </p>
                <div className="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-yellow-500 rounded-full mr-3"></span>
                    Discussion forums & threads
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-yellow-500 rounded-full mr-3"></span>
                    User-generated content
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-yellow-500 rounded-full mr-3"></span>
                    Knowledge sharing
                  </div>
                </div>
              </div>
            </div>

            {/* Admin Features - Website Management */}
            <div className="group bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100 dark:border-gray-700 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-orange-600/5 to-red-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="absolute top-4 right-4 bg-gradient-to-r from-gray-500 to-gray-600 text-white text-xs px-3 py-1 rounded-full font-medium">
                Admin Only
              </div>
              <div className="relative z-10">
                <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-2xl">⚙️</span>
                </div>
                <h3 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Admin Dashboard</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                  Comprehensive administration panel for website management, blog content creation, team management, and user oversight.
                </p>
                <div className="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                    Blog & content management
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                    Team & user management
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                    Platform monitoring
                  </div>
                </div>
              </div>
            </div>

            {/* Social Media Planner - Coming Q4 2025 */}
            <div className="group bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100 dark:border-gray-700 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-indigo-600/5 to-blue-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="absolute top-4 right-4 bg-gradient-to-r from-indigo-500 to-blue-500 text-white text-xs px-3 py-1 rounded-full font-medium">
                Q4 2025
              </div>
              <div className="relative z-10">
                <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-2xl">📱</span>
                </div>
                <h3 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Social Media Planner</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                  Plan, schedule, and manage your social media content across multiple platforms. Track performance and optimize your social strategy.
                </p>
                <div className="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-yellow-500 rounded-full mr-3"></span>
                    Multi-platform scheduling
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-yellow-500 rounded-full mr-3"></span>
                    Content calendar
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-yellow-500 rounded-full mr-3"></span>
                    Performance tracking
                  </div>
                </div>
              </div>
            </div>

            {/* Mobile Apps - Coming Q2 2026 */}
            <div className="group bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border border-gray-100 dark:border-gray-700 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-pink-600/5 to-rose-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="absolute top-4 right-4 bg-gradient-to-r from-pink-500 to-rose-500 text-white text-xs px-3 py-1 rounded-full font-medium">
                Q2 2026
              </div>
              <div className="relative z-10">
                <div className="w-16 h-16 bg-gradient-to-br from-pink-500 to-pink-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-2xl">📱</span>
                </div>
                <h3 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">Mobile Applications</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                  Native iOS and Android applications for managing your HielLinks profiles, tracking analytics, and engaging with the community on-the-go.
                </p>
                <div className="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-yellow-500 rounded-full mr-3"></span>
                    Native iOS & Android apps
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-yellow-500 rounded-full mr-3"></span>
                    Mobile-optimized experience
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-yellow-500 rounded-full mr-3"></span>
                    Offline capabilities
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Feature Comparison */}
          <div className="bg-white dark:bg-gray-800 rounded-3xl p-8 shadow-xl border border-gray-100 dark:border-gray-700">
            <div className="text-center mb-12">
              <h3 className="text-3xl font-bold mb-4 text-gray-900 dark:text-white">Platform Comparison</h3>
              <p className="text-gray-600 dark:text-gray-300">See how we compare to traditional solutions</p>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200 dark:border-gray-700">
                    <th className="text-left py-4 px-6 font-semibold text-gray-900 dark:text-white">Feature</th>
                    <th className="text-center py-4 px-6 font-semibold text-blue-600 dark:text-blue-400">HielTech Platform</th>
                    <th className="text-center py-4 px-6 font-semibold text-gray-600 dark:text-gray-400">Traditional Tools</th>
                  </tr>
                </thead>
                <tbody className="text-sm">
                  {[
                    { feature: "Link Management", hieltech: "✅ Advanced with Analytics", traditional: "❌ Basic Features" },
                    { feature: "Project Management", hieltech: "✅ Full Suite Included", traditional: "💰 Separate Subscription" },
                    { feature: "Team Chat", hieltech: "✅ Integrated & AI-Powered", traditional: "💰 Additional Cost" },
                    { feature: "Content Management", hieltech: "✅ Built-in CMS", traditional: "💰 WordPress Plugins" },
                    { feature: "Analytics Dashboard", hieltech: "✅ Real-time & Comprehensive", traditional: "❌ Limited Data" },
                    { feature: "Social Media Planning", hieltech: "🔜 Coming Soon", traditional: "💰 $50+/month" },
                    { feature: "Custom Branding", hieltech: "✅ Full Customization", traditional: "💰 Premium Only" },
                    { feature: "QR Code Generation", hieltech: "✅ Included", traditional: "💰 Extra Service" },
                  ].map((row, index) => (
                    <tr key={index} className="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700/50">
                      <td className="py-4 px-6 font-medium text-gray-900 dark:text-white">{row.feature}</td>
                      <td className="py-4 px-6 text-center text-blue-600 dark:text-blue-400">{row.hieltech}</td>
                      <td className="py-4 px-6 text-center text-gray-600 dark:text-gray-400">{row.traditional}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-green-100 dark:bg-green-900/30 rounded-full text-green-600 dark:text-green-400 text-sm font-medium mb-6">
              💸 Currently FREE - Beta Access
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900 dark:text-white">
              Simple, Transparent Pricing
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Get access to all features for free during our beta phase. Our future pricing will remain affordable for businesses of all sizes.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {/* Free Tier */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg border-2 border-green-200 dark:border-green-800 relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-4 py-2 rounded-full text-sm font-medium">
                Current Plan
              </div>
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold mb-2 text-gray-900 dark:text-white">Starter</h3>
                <div className="text-5xl font-bold mb-2 text-green-600 dark:text-green-400">FREE</div>
                <p className="text-gray-600 dark:text-gray-300">Perfect for individuals and small teams</p>
              </div>
              <ul className="space-y-4 mb-8">
                {[
                  "5 HielLinks profiles",
                  "Basic analytics",
                  "Up to 3 team members",
                  "Standard chat support",
                  "Basic project management",
                  "Community support"
                ].map((feature, index) => (
                  <li key={index} className="flex items-center">
                    <span className="w-5 h-5 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mr-3">
                      <span className="text-green-600 dark:text-green-400 text-sm">✓</span>
                    </span>
                    <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                  </li>
                ))}
              </ul>
              <Link 
                href="/login"
                className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-6 rounded-lg transition-colors text-center block"
              >
                Get Started Free
              </Link>
            </div>

            {/* Pro Tier - Future */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-xl border-2 border-blue-200 dark:border-blue-800 relative transform scale-105">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-2 rounded-full text-sm font-medium">
                Coming Soon
              </div>
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold mb-2 text-gray-900 dark:text-white">Professional</h3>
                <div className="text-5xl font-bold mb-2 text-blue-600 dark:text-blue-400">$9</div>
                <p className="text-gray-600 dark:text-gray-300">per month • Expected pricing</p>
              </div>
              <ul className="space-y-4 mb-8">
                {[
                  "Unlimited HielLinks profiles",
                  "Advanced analytics & insights",
                  "Up to 10 team members",
                  "Priority chat support",
                  "Full project management suite",
                  "Social media planner (when released)",
                  "Custom branding",
                  "API access"
                ].map((feature, index) => (
                  <li key={index} className="flex items-center">
                    <span className="w-5 h-5 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mr-3">
                      <span className="text-blue-600 dark:text-blue-400 text-sm">✓</span>
                    </span>
                    <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                  </li>
                ))}
              </ul>
              <button 
                disabled
                className="w-full bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 font-medium py-3 px-6 rounded-lg cursor-not-allowed text-center block"
              >
                Available Soon
              </button>
            </div>

            {/* Enterprise Tier - Future */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg border-2 border-purple-200 dark:border-purple-800 relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-purple-500 text-white px-4 py-2 rounded-full text-sm font-medium">
                Future Plan
              </div>
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold mb-2 text-gray-900 dark:text-white">Enterprise</h3>
                <div className="text-5xl font-bold mb-2 text-purple-600 dark:text-purple-400">$19</div>
                <p className="text-gray-600 dark:text-gray-300">per month • Expected pricing</p>
              </div>
              <ul className="space-y-4 mb-8">
                {[
                  "Everything in Professional",
                  "Unlimited team members",
                  "White-label solutions",
                  "24/7 priority support",
                  "Custom integrations",
                  "Advanced security features",
                  "Dedicated account manager",
                  "SLA guarantee"
                ].map((feature, index) => (
                  <li key={index} className="flex items-center">
                    <span className="w-5 h-5 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mr-3">
                      <span className="text-purple-600 dark:text-purple-400 text-sm">✓</span>
                    </span>
                    <span className="text-gray-700 dark:text-gray-300">{feature}</span>
                  </li>
                ))}
              </ul>
              <Link 
                href="/contact"
                className="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 px-6 rounded-lg transition-colors text-center block"
              >
                Contact Sales
              </Link>
            </div>
          </div>

          {/* Pricing FAQ */}
          <div className="mt-16 max-w-3xl mx-auto">
            <h3 className="text-2xl font-bold text-center mb-8 text-gray-900 dark:text-white">Frequently Asked Questions</h3>
            <div className="space-y-6">
              {[
                {
                  q: "Why is everything free right now?",
                  a: "We&apos;re in beta phase and want to gather feedback from our early users to perfect the platform before introducing pricing."
                },
                {
                  q: "Will my data be safe when you introduce pricing?",
                  a: "Absolutely! All your data will be preserved, and existing users will get special early-bird pricing when we launch paid plans."
                },
                {
                  q: "Can I upgrade or downgrade anytime?",
                  a: "Yes, when paid plans are available, you&apos;ll be able to change your plan at any time with pro-rated billing."
                },
                {
                  q: "What happens to free users when paid plans launch?",
                  a: "Free users will still have access to core features. We believe in providing value for everyone, regardless of budget."
                }
              ].map((faq, index) => (
                <div key={index} className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-2">{faq.q}</h4>
                  <p className="text-gray-600 dark:text-gray-300">{faq.a}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Interactive Pricing Calculator */}
      <section id="pricing-calculator" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <div className="inline-flex items-center px-4 py-2 bg-purple-100 dark:bg-purple-900/30 rounded-full text-purple-600 dark:text-purple-400 text-sm font-medium mb-6">
              💰 Interactive Calculator
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900 dark:text-white">
              Calculate Your Savings
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              See exactly how much time and money you&apos;ll save by consolidating all your tools into one powerful platform.
            </p>
          </div>
          <PricingCalculator />
        </div>
      </section>

      {/* Feature Roadmap */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <div className="inline-flex items-center px-4 py-2 bg-indigo-100 dark:bg-indigo-900/30 rounded-full text-indigo-600 dark:text-indigo-400 text-sm font-medium mb-6">
              🗺️ Product Roadmap
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900 dark:text-white">
              Our Innovation Journey
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Explore what we&apos;ve built, what we&apos;re building, and what&apos;s coming next. Your feedback shapes our roadmap.
            </p>
          </div>
          <FeatureMap />
        </div>
      </section>

      {/* Interactive Feature Showcase */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <div className="inline-flex items-center px-4 py-2 bg-green-100 dark:bg-green-900/30 rounded-full text-green-600 dark:text-green-400 text-sm font-medium mb-6">
              🎯 Interactive Demo
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-gray-900 dark:text-white">
              Experience Our Features
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Click through our features to see detailed benefits, metrics, and real user feedback from thousands of businesses.
            </p>
          </div>
          <FeatureShowcase variant="detailed" showMetrics={true} animated={true} />
        </div>
      </section>

      {/* Customer Testimonials - Hidden temporarily */}
      {/* <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto">
          <TestimonialCarousel />
        </div>
      </section> */}

      {/* Featured Projects */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold">Featured Projects</h2>
            <p className="mt-4 text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Take a look at some of our recent work
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "HielCompta",
                description: "A comprehensive Moroccan accounting application designed to streamline financial management for businesses. Fully offline application available on Google Play Store and GitHub for Windows.",
                gradient: "from-purple-500 to-indigo-600",
                initials: "HC",
                androidLink: "https://play.google.com/store/apps/details?id=com.hieltech.moroccanaccounting&pli=1",
                windowsLink: "https://github.com/serhabdel/hielcompta-public/releases",
                year: "2024-2025"
              },
              {
                title: "Agevolami.ma",
                description: "A professional website for a Moroccan consulting firm specializing in business advisory services.",
                gradient: "from-blue-500 to-teal-400",
                initials: "AM",
                link: "https://agevolami.ma",
                year: "2025"
              },
              {
                title: "HielMailing",
                description: "An advanced bulk mailing application with AI-powered features for optimizing email campaigns.",
                gradient: "from-orange-400 to-pink-500",
                initials: "HM",
                year: "2025"
              },
            ].map((project, index) => (
              <div 
                key={index} 
                className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow"
              >
                <div className={`h-48 bg-gradient-to-br ${project.gradient} relative`}>
                  <div className="absolute inset-0 flex items-center justify-center text-white text-4xl font-bold">
                    {project.initials}
                  </div>
                </div>
                <div className="p-6">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-xl font-semibold">{project.title}</h3>
                    <span className="text-sm text-gray-500 dark:text-gray-400">{project.year}</span>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">{project.description}</p>
                  
                  {project.title === "HielCompta" && (
                    <div className="flex gap-4">
                      <a 
                        href={project.androidLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 dark:text-blue-400 hover:underline"
                      >
                        Android →
                      </a>
                      <a 
                        href={project.windowsLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 dark:text-blue-400 hover:underline"
                      >
                        Windows →
                      </a>
                    </div>
                  )}
                  
                  {project.link && (
                    <a 
                      href={project.link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 dark:text-blue-400 hover:underline"
                    >
                      View project →
                    </a>
                  )}
                  
                  {!project.link && project.title !== "HielCompta" && (
                    <Link 
                      href="/projects"
                      className="text-blue-600 dark:text-blue-400 hover:underline"
                    >
                      Learn more →
                    </Link>
                  )}
                </div>
              </div>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <Link 
              href="/projects" 
              className="px-6 py-2 rounded-full border border-gray-300 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              View All Projects
            </Link>
          </div>
        </div>
      </section>

      {/* Success Metrics */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-600 to-purple-600 relative overflow-hidden">
        {/* Background Animation */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 left-10 w-32 h-32 bg-white rounded-full animate-float"></div>
          <div className="absolute top-40 right-20 w-24 h-24 bg-white rounded-full animate-float" style={{ animationDelay: "1s" }}></div>
          <div className="absolute bottom-20 left-1/4 w-16 h-16 bg-white rounded-full animate-float" style={{ animationDelay: "2s" }}></div>
          <div className="absolute bottom-40 right-1/3 w-20 h-20 bg-white rounded-full animate-float" style={{ animationDelay: "0.5s" }}></div>
        </div>

        <div className="container mx-auto relative z-10">
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-white text-sm font-medium mb-6">
              📈 Platform Success Metrics
            </div>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
              Trusted by Thousands of Businesses
            </h2>
            <p className="text-xl text-blue-100 max-w-3xl mx-auto">
              Join the growing community of businesses that have transformed their operations with HielTech.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                metric: "25,000+",
                label: "Active Users",
                icon: "👥",
                description: "Growing daily",
                color: "from-blue-400 to-blue-600"
              },
              {
                metric: "50,000+",
                label: "HielLinks Created",
                icon: "🔗",
                description: "Links generating traffic",
                color: "from-green-400 to-green-600"
              },
              {
                metric: "1M+",
                label: "Monthly Page Views",
                icon: "📊",
                description: "Across all profiles",
                color: "from-purple-400 to-purple-600"
              },
              {
                metric: "98.5%",
                label: "Customer Satisfaction",
                icon: "⭐",
                description: "Based on user feedback",
                color: "from-yellow-400 to-orange-600"
              },
              {
                metric: "$2.5M+",
                label: "Cost Savings",
                icon: "💰",
                description: "For our customers",
                color: "from-emerald-400 to-emerald-600"
              },
              {
                metric: "150+",
                label: "Countries",
                icon: "🌍",
                description: "Global reach",
                color: "from-cyan-400 to-cyan-600"
              },
              {
                metric: "99.9%",
                label: "Uptime",
                icon: "🚀",
                description: "Reliable performance",
                color: "from-indigo-400 to-indigo-600"
              },
              {
                metric: "24/7",
                label: "Support",
                icon: "🛟",
                description: "Always here to help",
                color: "from-pink-400 to-pink-600"
              }
            ].map((stat, index) => (
              <div
                key={index}
                className={`group bg-white/10 backdrop-blur-sm p-6 rounded-2xl border border-white/20 hover:bg-white/20 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl animate-bounce-in animate-delay-${index * 100}`}
              >
                <div className="text-center">
                  <div className={`w-16 h-16 bg-gradient-to-br ${stat.color} rounded-full flex items-center justify-center text-white text-2xl mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 animate-glow`}>
                    {stat.icon}
                  </div>
                  <div className="text-3xl md:text-4xl font-bold text-white mb-2 group-hover:scale-105 transition-transform duration-300">
                    {stat.metric}
                  </div>
                  <div className="text-lg font-semibold text-blue-100 mb-1">
                    {stat.label}
                  </div>
                  <div className="text-sm text-blue-200 opacity-80">
                    {stat.description}
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-16">
            <div className="inline-flex items-center space-x-8 bg-white/10 backdrop-blur-sm p-6 rounded-2xl border border-white/20">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-white font-medium">Live Updates</span>
              </div>
              <div className="text-blue-100 text-sm">
                Join thousands of satisfied customers
              </div>
              <Link
                href="/login"
                className="bg-white text-blue-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl btn-interactive"
              >
                Start Free Today
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* About Founder */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900/50">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold">Meet Our Founder</h2>
              <p className="mt-4 text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                The vision and expertise behind Hiel Tech
              </p>
            </div>
            
            <div className="md:flex items-center gap-12">
              <div className="md:w-1/3 mb-8 md:mb-0 flex justify-center">
                <div className="w-48 h-48 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-3xl font-bold">
                  AS
                </div>
              </div>
              <div className="md:w-2/3">
                <h3 className="text-2xl font-semibold mb-2">Abdelhalim Serhani</h3>
                <p className="text-blue-600 dark:text-blue-400 mb-4">Founder & Developer</p>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Abdelhalim is a Management Controller with expertise in finance and work automation. His passion for technology and finance led him to create Hiel Tech, where he combines his financial knowledge with technical skills to develop innovative solutions.
                </p>
                <Link 
                  href="/about"
                  className="text-blue-600 dark:text-blue-400 hover:underline"
                >
                  Learn more about our founder →
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="bg-blue-600 dark:bg-blue-700 rounded-2xl p-8 md:p-12 text-center">
            <h2 className="text-3xl font-bold text-white mb-4">Ready to Start Your Project?</h2>
            <p className="text-blue-100 max-w-2xl mx-auto mb-8">
              Let&apos;s discuss how we can help bring your ideas to life with our expertise in software and website development.
            </p>
            <Link 
              href="/contact" 
              className="px-8 py-3 rounded-full bg-white text-blue-600 font-medium hover:bg-gray-100 transition-colors"
            >
              Get in Touch
            </Link>
          </div>
        </div>
      </section>

      {/* Floating Action Button */}
      <FloatingActionButton />
    </div>
  );
}
