import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import ThemeProvider from "@/components/layout/ThemeProvider";
import StarBackground from "@/components/animations/StarBackground";
import { AuthProvider } from "@/lib/auth/AuthContext";
import ConditionalLayout from "@/components/layout/ConditionalLayout";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Hiel Tech - Software & Website Development",
  description: "Hiel Tech specializes in software and website development and maintenance services for businesses of all sizes.",
  keywords: ["software development", "website development", "web applications", "mobile apps", "cloud solutions"],
  authors: [{ name: "Hiel Tech" }],
  creator: "<PERSON><PERSON> Tech",
  publisher: "Hiel Tech",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  viewport: {
    width: "device-width",
    initialScale: 1,
    maximumScale: 1,
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://hieltech.vercel.app",
    siteName: "Hiel Tech",
    title: "Hiel Tech - Software & Website Development",
    description: "Hiel Tech specializes in software and website development and maintenance services for businesses of all sizes.",
  },
  twitter: {
    card: "summary_large_image",
    title: "Hiel Tech - Software & Website Development",
    description: "Hiel Tech specializes in software and website development and maintenance services for businesses of all sizes.",
    creator: "@hieltech",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${geistSans.variable} ${geistMono.variable} font-sans antialiased`}>
        <ThemeProvider>
          <AuthProvider>
            <StarBackground />
            <ConditionalLayout>
              {children}
            </ConditionalLayout>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
