import { NextRequest, NextResponse } from 'next/server';
import { SecurityChallengeSystem } from '@/lib/security/challengeSystem';

// Helper function to get client IP from request
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  if (cfConnectingIP) {
    return cfConnectingIP;
  }
  
  return request.ip || '127.0.0.1';
}

// GET /api/challenge - Generate a new security challenge
export async function GET(request: NextRequest) {
  try {
    const userIP = getClientIP(request);
    
    // Check if user already has a valid challenge
    const hasValid = await SecurityChallengeSystem.hasValidChallenge(userIP);
    
    if (hasValid) {
      return NextResponse.json({
        error: 'Active challenge exists',
        message: 'Please complete your current challenge before requesting a new one'
      }, { status: 400 });
    }

    // Generate new challenge
    const challenge = await SecurityChallengeSystem.generateChallenge(userIP);
    
    // Include target position for better UX (it's not sensitive since tolerance makes it secure)
    const publicChallenge = {
      id: challenge.id,
      type: challenge.type,
      currentPosition: challenge.currentPosition,
      targetPosition: challenge.targetPosition,
      tolerance: challenge.tolerance,
      instructions: challenge.instructions,
      theme: challenge.theme
    };

    return NextResponse.json({
      success: true,
      challenge: publicChallenge
    });

  } catch (error) {
    console.error('Error generating challenge:', error);
    return NextResponse.json({
      error: 'Failed to generate challenge',
      message: 'Please try again later'
    }, { status: 500 });
  }
}

// POST /api/challenge - Verify a challenge solution
export async function POST(request: NextRequest) {
  try {
    const userIP = getClientIP(request);
    const body = await request.json();
    
    const { challengeId, position } = body;
    
    if (!challengeId || !position || typeof position.x !== 'number' || typeof position.y !== 'number') {
      return NextResponse.json({
        error: 'Invalid request',
        message: 'Challenge ID and position coordinates are required'
      }, { status: 400 });
    }

    // Verify the challenge
    const result = await SecurityChallengeSystem.verifyChallenge(
      challengeId,
      userIP,
      { x: position.x, y: position.y }
    );

    return NextResponse.json({
      success: result.success,
      message: result.message,
      attemptsRemaining: result.attemptsRemaining,
      challengeId: result.challengeId
    });

  } catch (error) {
    console.error('Error verifying challenge:', error);
    return NextResponse.json({
      error: 'Failed to verify challenge',
      message: 'Please try again later'
    }, { status: 500 });
  }
}

// DELETE /api/challenge - Clear expired challenges (cleanup endpoint)
export async function DELETE(request: NextRequest) {
  try {
    const userIP = getClientIP(request);
    
    // This endpoint can be called to clean up expired challenges
    // The cleanup is also done automatically in the challenge system
    
    return NextResponse.json({
      success: true,
      message: 'Cleanup completed'
    });

  } catch (error) {
    console.error('Error during cleanup:', error);
    return NextResponse.json({
      error: 'Cleanup failed',
      message: 'Please try again later'
    }, { status: 500 });
  }
}
