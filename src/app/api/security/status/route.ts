import { NextRequest, NextResponse } from 'next/server';
import { SecurityChallengeSystem } from '@/lib/security/challengeSystem';
import { RateLimiter } from '@/lib/security/rateLimiter';

// Helper function to get client IP from request
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  if (cfConnectingIP) {
    return cfConnectingIP;
  }
  
  return request.ip || '127.0.0.1';
}

// GET /api/security/status - Get security status for current IP
export async function GET(request: NextRequest) {
  try {
    const userIP = getClientIP(request);
    const { searchParams } = new URL(request.url);
    const endpoint = searchParams.get('endpoint') || '/login';
    
    // Check challenge status
    const hasValidChallenge = await SecurityChallengeSystem.hasValidChallenge(userIP);
    
    // Check rate limit status
    const rateLimitStatus = await RateLimiter.getRateLimitStatus(userIP, endpoint);
    
    return NextResponse.json({
      success: true,
      security: {
        ip: userIP,
        hasValidChallenge,
        rateLimit: rateLimitStatus ? {
          allowed: rateLimitStatus.allowed,
          remaining: rateLimitStatus.remaining,
          resetTime: rateLimitStatus.resetTime,
          isBlocked: rateLimitStatus.isBlocked,
          blockUntil: rateLimitStatus.blockUntil
        } : null,
        requiresChallenge: !hasValidChallenge
      }
    });

  } catch (error) {
    console.error('Error getting security status:', error);
    return NextResponse.json({
      error: 'Failed to get security status',
      message: 'Please try again later'
    }, { status: 500 });
  }
}
