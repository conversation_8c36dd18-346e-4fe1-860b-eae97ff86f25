'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import { supabase } from '@/lib/supabase';
import styles from '@/components/animations/auth.module.css';

// Component that uses useSearchParams - needs to be wrapped in Suspense
function ConfirmEmailContent() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error' | 'expired' | 'invalid'>('loading');
  const [message, setMessage] = useState('Confirming your email...');
  const [redirectCountdown, setRedirectCountdown] = useState(0);
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleEmailConfirmation = async () => {
      try {
        // Add a timeout to prevent infinite loading
        const confirmationPromise = new Promise<void>(async (resolve, reject) => {
          try {
            // Check URL fragments first (newer Supabase format)
            const hashParams = new URLSearchParams(window.location.hash.substring(1));
            const accessToken = hashParams.get('access_token');
            const refreshToken = hashParams.get('refresh_token');
            const type = hashParams.get('type');
            const errorParam = hashParams.get('error');
            const errorDescription = hashParams.get('error_description');

            // Handle errors from URL
            if (errorParam) {
              if (errorParam === 'access_denied') {
                throw new Error('expired');
              } else {
                throw new Error(errorDescription || errorParam);
              }
            }

            // Also check URL search params as fallback
            const token = searchParams.get('token');
            const tokenHash = searchParams.get('token_hash');
            const errorCode = searchParams.get('error');
            const errorMsg = searchParams.get('error_description');

            if (errorCode) {
              if (errorCode === 'access_denied') {
                throw new Error('expired');
              } else {
                throw new Error(errorMsg || errorCode);
              }
            }

            let confirmationResult;

            if (accessToken && refreshToken && type === 'signup') {
              // Handle URL fragment-based confirmation (newer Supabase)
              console.log('Attempting fragment-based confirmation...');
              confirmationResult = await supabase.auth.setSession({
                access_token: accessToken,
                refresh_token: refreshToken
              });
            } else if (token || tokenHash) {
              // Handle token-based confirmation (fallback)
              console.log('Attempting token-based confirmation...');
              confirmationResult = await supabase.auth.verifyOtp({
                token_hash: tokenHash || token || '',
                type: 'signup'
              });
            } else {
              throw new Error('invalid');
            }

            if (confirmationResult?.error) {
              throw confirmationResult.error;
            }

            if (confirmationResult?.data?.user) {
              console.log('Email confirmation successful');
              setStatus('success');
              setMessage('Email confirmed successfully! Welcome to Hiel Tech!');
              
              // Start countdown for redirect
              setRedirectCountdown(5);
              const countdownInterval = setInterval(() => {
                setRedirectCountdown((prev) => {
                  if (prev <= 1) {
                    clearInterval(countdownInterval);
                    router.push('/profile');
                    return 0;
                  }
                  return prev - 1;
                });
              }, 1000);
            } else {
              throw new Error('No user data received after confirmation');
            }

            resolve();
          } catch (error) {
            reject(error);
          }
        });

        // Set a timeout for confirmation process
        timeoutId = setTimeout(() => {
          setStatus('error');
          setMessage('Email confirmation is taking longer than expected. Please try again.');
        }, 15000); // 15 second timeout

        await confirmationPromise;
        clearTimeout(timeoutId);

      } catch (error: unknown) {
        clearTimeout(timeoutId);
        console.error('Email confirmation error:', error);

        const errorMessage = error instanceof Error ? error.message : String(error);
        
        if (errorMessage === 'expired' || errorMessage.includes('expired') || errorMessage.includes('access_denied')) {
          setStatus('expired');
          setMessage('This confirmation link has expired. Please sign up again to receive a new confirmation email.');
        } else if (errorMessage === 'invalid' || errorMessage.includes('invalid') || errorMessage.includes('malformed')) {
          setStatus('invalid');
          setMessage('This confirmation link is invalid. Please check your email for the correct link or sign up again.');
        } else if (errorMessage.includes('already confirmed') || errorMessage.includes('already verified')) {
          setStatus('success');
          setMessage('Your email has already been confirmed! You can now sign in to your account.');
          // Redirect to login instead of profile for already confirmed
          setTimeout(() => {
            router.push('/login');
          }, 3000);
        } else {
          setStatus('error');
          setMessage('Unable to confirm your email. Please try clicking the link again or sign up for a new account.');
        }
      }
    };

    // Add a small delay to prevent flash of loading state
    const initDelay = setTimeout(() => {
      handleEmailConfirmation();
    }, 500);

    return () => {
      clearTimeout(initDelay);
      clearTimeout(timeoutId);
    };
  }, [searchParams, router]);

  const handleReturnToSignup = () => {
    router.push('/login?mode=signup');
  };

  const handleGoToLogin = () => {
    router.push('/login');
  };

  const handleGoToProfile = () => {
    router.push('/profile');
  };

  const getStatusConfig = () => {
    switch (status) {
      case 'success':
        return {
          color: 'from-green-500 to-emerald-600',
          textColor: 'from-green-500 to-emerald-500',
          icon: (
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          ),
          title: 'Email Confirmed!'
        };
      case 'expired':
        return {
          color: 'from-yellow-500 to-orange-600',
          textColor: 'from-yellow-500 to-orange-500',
          icon: (
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          ),
          title: 'Link Expired'
        };
      case 'invalid':
        return {
          color: 'from-red-500 to-pink-600',
          textColor: 'from-red-500 to-pink-500',
          icon: (
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 14.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          ),
          title: 'Invalid Link'
        };
      case 'error':
        return {
          color: 'from-red-500 to-pink-600',
          textColor: 'from-red-500 to-pink-500',
          icon: (
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          ),
          title: 'Confirmation Failed'
        };
      default:
        return {
          color: 'from-blue-500 to-purple-600',
          textColor: 'from-indigo-500 to-purple-500',
          icon: (
            <svg className="w-8 h-8 text-white animate-spin" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          ),
          title: 'Confirming Email...'
        };
    }
  };

  const config = getStatusConfig();

  return (
    <div className="min-h-screen py-12 bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className={`max-w-md mx-auto p-8 rounded-xl shadow-2xl border border-white/20 dark:border-gray-700/30 transition-all duration-300 ${styles['glass-morphism']} bg-white/10 dark:bg-gray-800/30`}
      >
        {/* Status Icon */}
        <div className="text-center mb-6">
          <div className={`mx-auto w-16 h-16 rounded-full flex items-center justify-center mb-4 bg-gradient-to-r ${config.color}`}>
            {config.icon}
          </div>
          
          <h2 className={`text-3xl font-bold bg-gradient-to-r ${config.textColor} bg-clip-text text-transparent mb-4 ${styles['animate-fade-in']}`}>
            {config.title}
          </h2>
        </div>

        {/* Message */}
        <div className="text-center mb-6">
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            {message}
          </p>
          
          {/* Countdown for success */}
          {status === 'success' && redirectCountdown > 0 && (
            <p className="text-sm text-green-600 dark:text-green-400">
              Redirecting to your profile in {redirectCountdown} seconds...
            </p>
          )}
        </div>

        {/* Action Buttons */}
        <div className="space-y-4">
          {status === 'success' && (
            <button
              onClick={handleGoToProfile}
              className="w-full py-3 px-4 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-lg font-medium hover:from-green-700 hover:to-emerald-700 transform hover:scale-[1.02] transition-all duration-200"
            >
              Go to Profile Now
            </button>
          )}
          
          {(status === 'expired' || status === 'invalid') && (
            <button
              onClick={handleReturnToSignup}
              className="w-full py-3 px-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transform hover:scale-[1.02] transition-all duration-200"
            >
              Sign Up Again
            </button>
          )}
          
          {status === 'error' && (
            <>
              <button
                onClick={() => window.location.reload()}
                className="w-full py-3 px-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transform hover:scale-[1.02] transition-all duration-200"
              >
                Try Again
              </button>
              <button
                onClick={handleReturnToSignup}
                className="w-full py-2 px-4 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
              >
                Sign Up with New Account
              </button>
            </>
          )}
          
          {status === 'loading' && (
            <div className="w-full py-3 px-4 bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 rounded-lg font-medium text-center">
              Please wait...
            </div>
          )}

          {/* Go to Login Button */}
          {status !== 'loading' && (
            <button
              onClick={handleGoToLogin}
              className="w-full py-2 px-4 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200"
            >
              ← Back to Sign In
            </button>
          )}
        </div>

        {/* Help Text */}
        {(status === 'error' || status === 'expired' || status === 'invalid') && (
          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
              Need help?
            </h4>
            <ul className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
              <li>• Make sure you clicked the link from your most recent email</li>
              <li>• Check if you might have already confirmed your email</li>
              <li>• Confirmation links expire after 24 hours</li>
              <li>• Try signing up again if the link is old</li>
              <li>• Contact support if you continue having issues</li>
            </ul>
          </div>
        )}
      </motion.div>
    </div>
  );
}

// Loading component for Suspense fallback
function ConfirmEmailLoading() {
  return (
    <div className="min-h-screen py-12 bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className={`max-w-md mx-auto p-8 rounded-xl shadow-2xl border border-white/20 dark:border-gray-700/30 transition-all duration-300 ${styles['glass-morphism']} bg-white/10 dark:bg-gray-800/30`}
      >
        <div className="text-center mb-6">
          <div className="mx-auto w-16 h-16 rounded-full flex items-center justify-center mb-4 bg-gradient-to-r from-blue-500 to-purple-600">
            <svg className="w-8 h-8 text-white animate-spin" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
          <h2 className="text-3xl font-bold bg-gradient-to-r from-indigo-500 to-purple-500 bg-clip-text text-transparent mb-4">
            Loading...
          </h2>
        </div>
        <div className="text-center mb-6">
          <p className="text-gray-600 dark:text-gray-300">
            Preparing email confirmation...
          </p>
        </div>
      </motion.div>
    </div>
  );
}

// Main export component with Suspense boundary
export default function ConfirmEmailPage() {
  return (
    <Suspense fallback={<ConfirmEmailLoading />}>
      <ConfirmEmailContent />
    </Suspense>
  );
}
