'use client';

import { useState } from 'react';
import SecurityChallenge from '@/components/auth/SecurityChallenge';

export default function TestSecurityPage() {
  const [completed, setCompleted] = useState(false);
  const [error, setError] = useState('');
  const [challengeId, setChallengeId] = useState('');

  const handleComplete = (id: string) => {
    setCompleted(true);
    setChallengeId(id);
    setError('');
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
    setCompleted(false);
  };

  const resetChallenge = () => {
    setCompleted(false);
    setError('');
    setChallengeId('');
    // Force component remount by changing key
    window.location.reload();
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Security Challenge Test
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Test the security challenge component functionality
          </p>
        </div>

        {completed ? (
          <div className="max-w-md mx-auto">
            <div className="bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800 rounded-xl p-8 text-center">
              <div className="w-16 h-16 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h2 className="text-xl font-bold text-green-800 dark:text-green-200 mb-2">
                Challenge Completed! 🎉
              </h2>
              <p className="text-green-700 dark:text-green-300 mb-4">
                Challenge ID: <code className="bg-green-100 dark:bg-green-800 px-2 py-1 rounded text-sm">{challengeId}</code>
              </p>
              <button
                onClick={resetChallenge}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                Try Another Challenge
              </button>
            </div>
          </div>
        ) : error ? (
          <div className="max-w-md mx-auto">
            <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-xl p-8 text-center mb-6">
              <div className="w-16 h-16 bg-red-100 dark:bg-red-900/50 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <h2 className="text-xl font-bold text-red-800 dark:text-red-200 mb-2">
                Challenge Failed
              </h2>
              <p className="text-red-700 dark:text-red-300 mb-4">
                {error}
              </p>
              <button
                onClick={resetChallenge}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                Try Again
              </button>
            </div>
            <SecurityChallenge
              onComplete={handleComplete}
              onError={handleError}
            />
          </div>
        ) : (
          <SecurityChallenge
            onComplete={handleComplete}
            onError={handleError}
          />
        )}

        {/* Instructions */}
        <div className="max-w-2xl mx-auto mt-12 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            How to Complete the Challenge:
          </h3>
          <div className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-start">
              <span className="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-xs font-bold mr-3 mt-0.5">1</span>
              <p>Look for the <strong className="text-blue-600 dark:text-blue-400">blue element</strong> (circle, rectangle, or diamond)</p>
            </div>
            <div className="flex items-start">
              <span className="flex-shrink-0 w-6 h-6 bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 rounded-full flex items-center justify-center text-xs font-bold mr-3 mt-0.5">2</span>
              <p>Find the <strong className="text-green-600 dark:text-green-400">green target area</strong> with the 🎯 symbol</p>
            </div>
            <div className="flex items-start">
              <span className="flex-shrink-0 w-6 h-6 bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400 rounded-full flex items-center justify-center text-xs font-bold mr-3 mt-0.5">3</span>
              <p><strong>Click and drag</strong> the blue element to the green target area</p>
            </div>
            <div className="flex items-start">
              <span className="flex-shrink-0 w-6 h-6 bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-400 rounded-full flex items-center justify-center text-xs font-bold mr-3 mt-0.5">4</span>
              <p>When you're close, you'll see <strong className="text-green-600 dark:text-green-400">"Very close! Drop it here!"</strong> message</p>
            </div>
            <div className="flex items-start">
              <span className="flex-shrink-0 w-6 h-6 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full flex items-center justify-center text-xs font-bold mr-3 mt-0.5">5</span>
              <p>Release the mouse button or lift your finger to complete the challenge</p>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <div className="flex items-start">
              <svg className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
              <div>
                <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                  💡 Pro Tips:
                </p>
                <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                  <li>• You have 3 attempts to complete the challenge</li>
                  <li>• The tolerance is generous - you don't need pixel-perfect accuracy</li>
                  <li>• Watch for visual feedback like color changes and animations</li>
                  <li>• On mobile, use touch and drag gestures</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
