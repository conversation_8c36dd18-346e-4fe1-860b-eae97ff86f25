'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { motion } from 'framer-motion';

function AuthCallbackContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing authentication...');

  useEffect(() => {
    let isProcessing = false; // Guard to prevent multiple executions
    let authCompleted = false; // Track if auth is complete

    const handleAuthCallback = async () => {
      if (isProcessing) {
        console.log('Auth callback already processing, skipping...');
        return;
      }
      
      isProcessing = true;
      
      try {
        console.log('Auth callback initiated...');
        
        // Check for OAuth errors first
        const queryParams = new URLSearchParams(window.location.search);
        const error = queryParams.get('error');
        const errorDescription = queryParams.get('error_description');

        if (error) {
          console.error('OAuth error:', error, errorDescription);
          setStatus('error');
          setMessage(errorDescription || 'Authentication failed. Please try again.');
          // Clean up URL even on error
          window.history.replaceState({}, document.title, window.location.pathname);
          setTimeout(() => router.push('/login'), 3000);
          return;
        }

        // Check for authorization code in URL (PKCE flow)
        const code = queryParams.get('code');
        if (code) {
          console.log('Authorization code found, exchanging for session...');
          
          try {
            // Let Supabase handle the code exchange automatically
            const { data, error: exchangeError } = await supabase.auth.exchangeCodeForSession(code);
            
            console.log('Code exchange result:', { 
              hasSession: !!data?.session, 
              hasUser: !!data?.session?.user, 
              error: exchangeError ? exchangeError.message : 'none' 
            });
            
            if (exchangeError) {
              console.error('Code exchange error:', exchangeError);
              
              // Handle specific exchange errors
              if (exchangeError.message.includes('invalid_grant') || exchangeError.message.includes('expired')) {
                setStatus('error');
                setMessage('Authentication link has expired. Please try signing in again.');
              } else {
                setStatus('error');
                setMessage('Failed to complete authentication. Please try again.');
              }
              setTimeout(() => router.push('/login'), 3000);
              return;
            }

            if (data.session?.user) {
              console.log('Session established via code exchange:', data.session.user.id);
              // Clean up URL immediately
              window.history.replaceState({}, document.title, window.location.pathname);
              await handleSuccessfulAuth(data.session.user);
              return;
            } else {
              console.warn('Code exchange succeeded but no session/user returned:', data);
              // Continue to fallback methods below
            }
          } catch (exchangeError) {
            console.error('Error during code exchange:', exchangeError);
            
            // Check if method doesn't exist (fallback to older method)
            if (exchangeError instanceof Error && exchangeError.message.includes('exchangeCodeForSession')) {
              console.log('Falling back to legacy session detection...');
              // Continue to fallback method below
            } else {
              setStatus('error');
              setMessage('Authentication failed. Please try again.');
              setTimeout(() => router.push('/login'), 3000);
              return;
            }
          }
        }

        // Only try fallback methods if we haven't already completed auth
        if (!authCompleted) {
          // Fallback: Try manual session detection since detectSessionInUrl is disabled
          console.log('Trying manual session detection...');
          
          // Create a temporary client with detectSessionInUrl enabled for this callback
          const { createClient } = await import('@supabase/supabase-js');
          const tempSupabase = createClient(
            process.env.NEXT_PUBLIC_SUPABASE_URL!,
            process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
            {
              auth: {
                detectSessionInUrl: true,
                persistSession: true,
                storage: typeof window !== 'undefined' ? window.localStorage : undefined,
                flowType: 'pkce',
              }
            }
          );

          // Try to get session with the temporary client
          const { data: { session }, error: sessionError } = await tempSupabase.auth.getSession();
          
          if (sessionError) {
            console.error('Session error:', sessionError);
          } else if (session?.user && !authCompleted) {
            console.log('Session detected via manual detection:', session.user.id);
            // Transfer the session to our main client
            await supabase.auth.setSession({
              access_token: session.access_token,
              refresh_token: session.refresh_token
            });
            await handleSuccessfulAuth(session.user);
            return;
          }

          // Final fallback: Check our main client session
          if (!authCompleted) {
            console.log('Checking main client session...');
            const { data: { session: mainSession }, error: mainSessionError } = await supabase.auth.getSession();
            
            if (mainSessionError) {
              console.error('Main session error:', mainSessionError);
            } else if (mainSession?.user && !authCompleted) {
              console.log('Session found in main client:', mainSession.user.id);
              await handleSuccessfulAuth(mainSession.user);
              return;
            }
          }
        }

        // Last resort: Wait for auth state change with shorter timeout
        if (!authCompleted) {
          console.log('No session found, setting up auth state listener...');
          let isResolved = false;
          
          const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
            if (isResolved || authCompleted) return;
            
            console.log('Auth state change:', event, !!session);
            
            if (event === 'SIGNED_IN' && session?.user) {
              isResolved = true;
              subscription.unsubscribe();
              await handleSuccessfulAuth(session.user);
            } else if (event === 'SIGNED_OUT' || (event === 'TOKEN_REFRESHED' && !session)) {
              isResolved = true;
              subscription.unsubscribe();
              setStatus('error');
              setMessage('Authentication failed. Please try again.');
              setTimeout(() => router.push('/login'), 3000);
            }
          });

          // Set a shorter timeout for faster failure feedback
          setTimeout(() => {
            if (!isResolved && !authCompleted) {
              isResolved = true;
              subscription.unsubscribe();
              setStatus('error');
              setMessage('Authentication took too long. Please try signing in again.');
              setTimeout(() => router.push('/login'), 3000);
            }
          }, 3000); // Reduced from 4s to 3s for faster feedback
        }

      } catch (error) {
        console.error('Auth callback error:', error);
        setStatus('error');
        setMessage('An unexpected error occurred during authentication. Please try again.');
        setTimeout(() => router.push('/login'), 3000);
      }
    };

    const handleSuccessfulAuth = async (user: { id: string; email?: string }) => {
      // Prevent multiple calls
      if (authCompleted) {
        console.log('Auth already completed, skipping duplicate call');
        return;
      }
      
      authCompleted = true;
      
      console.log('handleSuccessfulAuth called with user:', { id: user.id, email: user.email });
      setStatus('success');
      setMessage('Authentication successful! Setting up your profile...');
      
      try {
        console.log('Importing database module...');
        const { db } = await import('@/lib/supabase');
        console.log('Database module imported successfully');
        
        // Ensure profile exists for the user (especially important for Google OAuth)
        console.log('Getting user profile...');
        let profile = await db.getProfile(user.id);
        console.log('Profile fetch result:', !!profile);
        
        if (!profile && user.email) {
          console.log('Creating profile for new Google user with auto profile picture...');
          profile = await db.createProfile(user.id, user.email);
          if (profile) {
            console.log('Profile created successfully with Google profile picture:', !!profile.avatar_url);
          }
        }
        
        // Check if user has a pending team application to link
        if (user.email) {
          try {
            console.log('Checking for pending team application...');
            const application = await db.getApplicationByEmail(user.email);
            if (application && !application.account_created) {
              await db.markAccountCreated(application.id, user.id);
              console.log('Linked OAuth account to existing team application');
            }
          } catch (linkError) {
            console.error('Error linking application to OAuth account:', linkError);
            // Don't fail the auth flow for this
          }
        }
        
        setMessage('Profile setup complete! Redirecting...');
        console.log('Profile setup completed successfully');
      } catch (profileError) {
        console.error('Error setting up profile:', profileError);
        // Don't fail the auth flow, just log the error
        setMessage('Authentication successful! Redirecting...');
      }
      
      // Signal that auth callback is complete by setting a flag
      // This allows the AuthContext to know when it's safe to initialize
      console.log('Setting auth callback complete flag...');
      if (typeof window !== 'undefined') {
        sessionStorage.setItem('auth-callback-complete', 'true');
        // Also dispatch a custom event for immediate detection
        window.dispatchEvent(new CustomEvent('auth-callback-complete'));
      }
      
      // Clear the URL immediately to prevent any conflicts
      console.log('Cleaning up URL...');
      window.history.replaceState({}, document.title, window.location.pathname);
      
      // Wait a moment for the auth state to propagate, then redirect
      console.log('Scheduling redirect to profile page...');
      setTimeout(() => {
        console.log('Redirecting to profile page...');
        router.push('/profile');
      }, 1500); // Increased slightly to ensure state propagation
    };

    handleAuthCallback();

    // Cleanup function
    return () => {
      isProcessing = false;
      authCompleted = false;
    };
  }, [router, searchParams]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 px-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-md w-full text-center"
      >
        <div className="bg-white/10 dark:bg-gray-800/30 rounded-xl p-8 backdrop-blur-xl border border-white/20 dark:border-gray-700/30">
          {/* Loading Spinner */}
          {status === 'loading' && (
            <div className="mb-6">
              <div className="w-16 h-16 mx-auto border-4 border-blue-200 dark:border-blue-800 border-t-blue-600 dark:border-t-blue-400 rounded-full animate-spin"></div>
            </div>
          )}

          {/* Success Icon */}
          {status === 'success' && (
            <div className="mb-6">
              <div className="w-16 h-16 mx-auto bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            </div>
          )}

          {/* Error Icon */}
          {status === 'error' && (
            <div className="mb-6">
              <div className="w-16 h-16 mx-auto bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
            </div>
          )}

          {/* Status Message */}
          <h2 className={`text-xl font-semibold mb-2 ${
            status === 'success' 
              ? 'text-green-600 dark:text-green-400' 
              : status === 'error' 
                ? 'text-red-600 dark:text-red-400' 
                : 'text-gray-900 dark:text-white'
          }`}>
            {status === 'loading' && 'Authenticating...'}
            {status === 'success' && 'Welcome to Hiel Tech!'}
            {status === 'error' && 'Authentication Failed'}
          </h2>

          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {message}
          </p>

          {/* Manual redirect button for errors */}
          {status === 'error' && (
            <button
              onClick={() => router.push('/login')}
              className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              Back to Sign In
            </button>
          )}

          {/* Progress indicator */}
          {status !== 'error' && (
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {status === 'loading' ? 'Please wait...' : 'Redirecting in a moment...'}
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
}

export default function AuthCallback() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 px-4">
        <div className="max-w-md w-full text-center">
          <div className="bg-white/10 dark:bg-gray-800/30 rounded-xl p-8 backdrop-blur-xl border border-white/20 dark:border-gray-700/30">
            <div className="mb-6">
              <div className="w-16 h-16 mx-auto border-4 border-blue-200 dark:border-blue-800 border-t-blue-600 dark:border-t-blue-400 rounded-full animate-spin"></div>
            </div>
            <h2 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">
              Loading...
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Preparing authentication...
            </p>
          </div>
        </div>
      </div>
    }>
      <AuthCallbackContent />
    </Suspense>
  );
} 