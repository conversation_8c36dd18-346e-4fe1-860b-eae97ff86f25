'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { db, Project } from '@/lib/supabase';

// YouTube embed component
const YouTubeEmbed = ({ videoId }: { videoId: string }) => (
  <div className="relative w-full h-0 pb-[56.25%] rounded-lg overflow-hidden">
    <iframe
      src={`https://www.youtube.com/embed/${videoId}?rel=0&modestbranding=1`}
      title="YouTube video player"
      frameBorder="0"
      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
      allowFullScreen
      className="absolute top-0 left-0 w-full h-full"
    />
  </div>
);

// Image carousel component
const ImageCarousel = ({ images }: { images: string[] }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isModalOpen, setIsModalOpen] = useState(false);

  if (!images || images.length === 0) return null;

  const nextImage = () => {
    setCurrentIndex((prev) => (prev + 1) % images.length);
  };

  const prevImage = () => {
    setCurrentIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  return (
    <>
      {/* Carousel */}
      <div className="relative">
        <div className="relative h-96 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800">
          <Image
            src={images[currentIndex]}
            alt={`Project image ${currentIndex + 1}`}
            fill
            className="object-cover cursor-pointer"
            onClick={() => setIsModalOpen(true)}
          />
          
          {images.length > 1 && (
            <>
              <button
                onClick={prevImage}
                className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all"
              >
                ←
              </button>
              <button
                onClick={nextImage}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all"
              >
                →
              </button>
            </>
          )}
        </div>

        {/* Thumbnails */}
        {images.length > 1 && (
          <div className="flex gap-2 mt-4 overflow-x-auto pb-2">
            {images.map((image, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`relative w-20 h-20 rounded-lg overflow-hidden flex-shrink-0 border-2 transition-all ${
                  index === currentIndex
                    ? 'border-blue-500 ring-2 ring-blue-200'
                    : 'border-gray-200 dark:border-gray-600 hover:border-gray-300'
                }`}
              >
                <Image
                  src={image}
                  alt={`Thumbnail ${index + 1}`}
                  fill
                  className="object-cover"
                />
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center p-4">
          <div className="relative max-w-7xl max-h-full">
            <button
              onClick={() => setIsModalOpen(false)}
              className="absolute -top-12 right-0 text-white text-2xl hover:text-gray-300"
            >
              ✕
            </button>
            <Image
              src={images[currentIndex]}
              alt="Full size project image"
              width={1200}
              height={800}
              className="max-w-full max-h-full object-contain"
            />
          </div>
        </div>
      )}
    </>
  );
};

export default function ProjectDetailPage() {
  const params = useParams();
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    if (params.slug) {
      loadProject(params.slug as string);
    }
  }, [params.slug]);

  const loadProject = async (slug: string) => {
    try {
      setLoading(true);
      const projectData = await db.getProjectBySlug(slug);
      if (projectData) {
        setProject(projectData);
      } else {
        setError('Project not found');
      }
    } catch (error) {
      console.error('Error loading project:', error);
      setError('Failed to load project');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading project...</p>
        </div>
      </div>
    );
  }

  if (error || !project) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">😕</div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            {error || 'Project Not Found'}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            The project you&apos;re looking for doesn&apos;t exist or has been removed.
          </p>
          <Link 
            href="/projects"
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Projects
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            {/* Breadcrumb */}
            <nav className="mb-8">
              <ol className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                <li><Link href="/" className="hover:text-blue-600">Home</Link></li>
                <li className="before:content-['/'] before:mx-2">
                  <Link href="/projects" className="hover:text-blue-600">Projects</Link>
                </li>
                <li className="before:content-['/'] before:mx-2 text-gray-900 dark:text-white">
                  {project.title}
                </li>
              </ol>
            </nav>

            {/* Project Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center mb-12"
            >
              {project.logo_url && (
                <div className="mb-6">
                  <Image
                    src={project.logo_url}
                    alt={`${project.title} logo`}
                    width={120}
                    height={120}
                    className="mx-auto rounded-lg"
                  />
                </div>
              )}
              
              <h1 className="text-4xl sm:text-5xl font-bold text-gray-900 dark:text-white mb-4">
                {project.title}
              </h1>
              
              <div className="flex items-center justify-center gap-4 mb-6">
                <span className={`px-3 py-1 text-sm rounded-full ${
                  project.status === "completed" 
                    ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" 
                    : project.status === "in-progress" 
                    ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200" 
                    : "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                }`}>
                  {project.status === "completed" 
                    ? "Completed" 
                    : project.status === "in-progress" 
                    ? "In Progress" 
                    : "Upcoming"}
                </span>
                <span className="text-gray-600 dark:text-gray-400">{project.year}</span>
                {project.client_name && (
                  <span className="text-gray-600 dark:text-gray-400">
                    Client: {project.client_name}
                  </span>
                )}
              </div>

              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                {project.description}
              </p>

              {/* Project Links */}
              <div className="flex flex-wrap justify-center gap-4 mt-8">
                {project.link && (
                  <a
                    href={project.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    🌐 Visit Website
                  </a>
                )}
                {project.android_link && (
                  <a
                    href={project.android_link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
                  >
                    📱 Android App
                  </a>
                )}
                {project.windows_link && (
                  <a
                    href={project.windows_link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    🖥️ Windows App
                  </a>
                )}
                {project.project_url && (
                  <a
                    href={project.project_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors"
                  >
                    🔗 Project Demo
                  </a>
                )}
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
              {/* Main Content */}
              <div className="lg:col-span-2 space-y-12">
                {/* YouTube Video */}
                {project.youtube_video_id && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.1 }}
                  >
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                      Project Demo
                    </h2>
                    <YouTubeEmbed videoId={project.youtube_video_id} />
                  </motion.div>
                )}

                {/* Image Carousel */}
                {project.images && project.images.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                      Project Gallery
                    </h2>
                    <ImageCarousel images={project.images} />
                  </motion.div>
                )}

                {/* Project Details */}
                {project.project_details?.overview && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="bg-white dark:bg-gray-800 rounded-lg p-8"
                  >
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                      Project Overview
                    </h2>
                    <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                      {project.project_details.overview}
                    </p>
                  </motion.div>
                )}

                {/* Technical Details */}
                {(project.tech_details || project.challenges || project.solutions) && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                    className="bg-white dark:bg-gray-800 rounded-lg p-8"
                  >
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                      Technical Implementation
                    </h2>
                    
                    {project.tech_details && (
                      <div className="mb-6">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                          Technical Details
                        </h3>
                        <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                          {project.tech_details}
                        </p>
                      </div>
                    )}

                    {project.challenges && (
                      <div className="mb-6">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                          Challenges
                        </h3>
                        <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                          {project.challenges}
                        </p>
                      </div>
                    )}

                    {project.solutions && (
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                          Solutions
                        </h3>
                        <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                          {project.solutions}
                        </p>
                      </div>
                    )}
                  </motion.div>
                )}

                {/* Results */}
                {project.results && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                    className="bg-white dark:bg-gray-800 rounded-lg p-8"
                  >
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                      Results & Impact
                    </h2>
                    <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                      {project.results}
                    </p>
                  </motion.div>
                )}
              </div>

              {/* Sidebar */}
              <div className="space-y-8">
                {/* Technologies */}
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 }}
                  className="bg-white dark:bg-gray-800 rounded-lg p-6"
                >
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Technologies Used
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {project.technologies.map((tech, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm rounded-lg"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </motion.div>

                {/* Project Info */}
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.4 }}
                  className="bg-white dark:bg-gray-800 rounded-lg p-6"
                >
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                    Project Information
                  </h3>
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Year:</span>
                      <span className="text-gray-900 dark:text-white font-medium">{project.year}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Status:</span>
                      <span className="text-gray-900 dark:text-white font-medium">
                        {project.status === "completed" 
                          ? "Completed" 
                          : project.status === "in-progress" 
                          ? "In Progress" 
                          : "Upcoming"}
                      </span>
                    </div>
                    {project.project_duration && (
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Duration:</span>
                        <span className="text-gray-900 dark:text-white font-medium">{project.project_duration}</span>
                      </div>
                    )}
                    {project.client_name && (
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-400">Client:</span>
                        <span className="text-gray-900 dark:text-white font-medium">{project.client_name}</span>
                      </div>
                    )}
                  </div>
                </motion.div>

                {/* Team Members */}
                {project.team_members && project.team_members.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.5 }}
                    className="bg-white dark:bg-gray-800 rounded-lg p-6"
                  >
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      Team Members
                    </h3>
                    <div className="space-y-2">
                      {project.team_members.map((member, index) => (
                        <div key={index} className="text-gray-600 dark:text-gray-300">
                          {member}
                        </div>
                      ))}
                    </div>
                  </motion.div>
                )}

                {/* Features */}
                {project.project_details?.features && project.project_details.features.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.6 }}
                    className="bg-white dark:bg-gray-800 rounded-lg p-6"
                  >
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      Key Features
                    </h3>
                    <ul className="space-y-2">
                      {project.project_details.features.map((feature, index) => (
                        <li key={index} className="flex items-start">
                          <span className="text-blue-600 mr-2">✓</span>
                          <span className="text-gray-600 dark:text-gray-300">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </motion.div>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white dark:bg-gray-800">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Interested in working with us?
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
              Let&apos;s discuss how we can help bring your ideas to life with our expertise.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <Link 
                href="/contact"
                className="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Start a Project
              </Link>
              <Link 
                href="/projects"
                className="bg-gray-600 text-white px-8 py-3 rounded-lg hover:bg-gray-700 transition-colors"
              >
                View All Projects
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
} 