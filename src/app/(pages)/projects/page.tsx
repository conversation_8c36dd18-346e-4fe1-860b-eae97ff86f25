"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { db, Project } from "@/lib/supabase";

interface ProjectCardProps {
  title: string;
  description: string;
  technologies: string[];
  gradient: string;
  androidLink?: string;
  windowsLink?: string;
  link?: string;
  status: "completed" | "in-progress" | "upcoming";
  year: string;
  slug: string;
  logo_url?: string;
  featured_image_url?: string;
}

const ProjectCard: React.FC<ProjectCardProps> = ({
  title,
  description,
  technologies,
  gradient,
  androidLink,
  windowsLink,
  link,
  status,
  year,
  slug,
  logo_url,
  featured_image_url
}) => {
  // Get initials for the placeholder
  const initials = title
    .split(" ")
    .map((word) => word[0])
    .join("")
    .toUpperCase();

  return (
    <Link href={`/projects/${slug}`} className="block group">
      <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all transform group-hover:scale-105">
        {/* Image or Gradient Header */}
        <div className={`h-56 relative overflow-hidden ${
          featured_image_url || logo_url 
            ? 'bg-gray-100 dark:bg-gray-700' 
            : `bg-gradient-to-br ${gradient}`
        }`}>
          {featured_image_url ? (
            <Image
              src={featured_image_url}
              alt={title}
              fill
              className="object-cover"
            />
          ) : logo_url ? (
            <div className="absolute inset-0 flex items-center justify-center bg-gray-50 dark:bg-gray-800">
              <Image
                src={logo_url}
                alt={`${title} logo`}
                width={120}
                height={120}
                className="object-contain"
              />
            </div>
          ) : (
            <div className="absolute inset-0 flex items-center justify-center text-white text-4xl font-bold">
              {initials}
            </div>
          )}
          
          {/* Overlay on hover */}
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center">
            <span className="text-white opacity-0 group-hover:opacity-100 transition-opacity text-lg font-medium">
              View Details →
            </span>
          </div>
        </div>
        
        <div className="p-6">
          <div className="flex justify-between items-start mb-2">
            <h3 className="text-xl font-semibold group-hover:text-blue-600 transition-colors">{title}</h3>
            <span className="text-sm text-gray-500 dark:text-gray-400">{year}</span>
          </div>
          <div className="mb-4">
            <span className={`inline-block px-2 py-1 text-xs rounded-full ${
              status === "completed" 
                ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" 
                : status === "in-progress" 
                ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200" 
                : "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
            }`}>
              {status === "completed" 
                ? "Completed" 
                : status === "in-progress" 
                ? "In Progress" 
                : "Upcoming"}
            </span>
          </div>
          <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
            {description}
          </p>
          <div className="mb-4">
            <div className="flex flex-wrap gap-2">
              {technologies.slice(0, 4).map((tech, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-md text-xs"
                >
                  {tech}
                </span>
              ))}
              {technologies.length > 4 && (
                <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-md text-xs">
                  +{technologies.length - 4} more
                </span>
              )}
            </div>
          </div>
          
          {/* External Links */}
          {(androidLink || windowsLink || link) && (
            <div className="flex gap-4 mb-4" onClick={(e) => e.stopPropagation()}>
              {androidLink && (
                <a
                  href={androidLink}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 dark:text-blue-400 hover:underline text-sm font-medium"
                  onClick={(e) => e.stopPropagation()}
                >
                  📱 Android
                </a>
              )}
              {windowsLink && (
                <a
                  href={windowsLink}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 dark:text-blue-400 hover:underline text-sm font-medium"
                  onClick={(e) => e.stopPropagation()}
                >
                  🖥️ Windows
                </a>
              )}
              {link && !androidLink && !windowsLink && (
                <a
                  href={link}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 dark:text-blue-400 hover:underline text-sm font-medium"
                  onClick={(e) => e.stopPropagation()}
                >
                  🌐 Website
                </a>
              )}
            </div>
          )}
        </div>
      </div>
    </Link>
  );
};

export default function ProjectsPage() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  const categories = ["All", "Web", "Mobile", "Design", "AI"];
  const [activeCategory, setActiveCategory] = React.useState("All");

  useEffect(() => {
    loadProjects();
  }, []);

  const loadProjects = async () => {
    try {
      setLoading(true);
      const projectsData = await db.getProjects(true);
      setProjects(projectsData);
    } catch (error) {
      console.error('Error loading projects:', error);
      setError('Failed to load projects');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col min-h-screen">
        <section className="relative py-20 md:py-28 px-4 sm:px-6 lg:px-8">
          <div className="container mx-auto">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl sm:text-5xl font-bold tracking-tight animate-fade-in">
                Our <span className="text-blue-600 dark:text-blue-400">Projects</span>
              </h1>
              <div className="flex items-center justify-center mt-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-3 text-gray-600 dark:text-gray-400">Loading projects...</span>
              </div>
            </div>
          </div>
        </section>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col min-h-screen">
        <section className="relative py-20 md:py-28 px-4 sm:px-6 lg:px-8">
          <div className="container mx-auto">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-4xl sm:text-5xl font-bold tracking-tight animate-fade-in">
                Our <span className="text-blue-600 dark:text-blue-400">Projects</span>
              </h1>
              <div className="mt-8 text-red-600 dark:text-red-400">
                {error}
              </div>
            </div>
          </div>
        </section>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 md:py-28 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl sm:text-5xl font-bold tracking-tight animate-fade-in">
              Our <span className="text-blue-600 dark:text-blue-400">Projects</span>
            </h1>
            <p className="mt-6 text-lg md:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto animate-slide-up">
              Explore our portfolio of innovative solutions across various technologies and industries
            </p>
          </div>
        </div>
      </section>

      {/* Category Filter */}
      <section className="py-8 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="max-w-5xl mx-auto">
            <div className="flex flex-wrap justify-center gap-2 mb-8">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setActiveCategory(category)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                    activeCategory === category
                      ? "bg-blue-600 text-white"
                      : "bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="py-8 px-4 sm:px-6 lg:px-8 mb-16">
        <div className="container mx-auto">
          <div className="max-w-6xl mx-auto">
            {projects.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-600 dark:text-gray-400">No projects found.</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {projects.map((project) => (
                  <ProjectCard 
                    key={project.id}
                    title={project.title}
                    description={project.description}
                    technologies={project.technologies}
                    gradient={project.gradient}
                    androidLink={project.android_link}
                    windowsLink={project.windows_link}
                    link={project.link}
                    status={project.status}
                    year={project.year}
                    slug={project.slug}
                    logo_url={project.logo_url}
                    featured_image_url={project.featured_image_url}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900/50 mt-auto">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4">Have a Project in Mind?</h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
              Let&apos;s discuss how we can help bring your ideas to life with our expertise in software and website development.
            </p>
            <Link 
              href="/contact" 
              className="px-8 py-3 rounded-full bg-blue-600 text-white font-medium hover:bg-blue-700 transition-colors"
            >
              Start a Project
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
