'use client';

import { useState } from 'react';
import LoginForm from '@/components/auth/LoginForm';
import { useAuth } from '@/lib/auth/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect, Suspense } from 'react';
import Head from 'next/head';

function LoginPageContent() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [isRedirecting, setIsRedirecting] = useState(false);

  useEffect(() => {
    // If user is authenticated, redirect to profile page
    if (user && !authLoading) {
      setIsRedirecting(true);
      router.push('/profile');
    }
  }, [user, authLoading, router]);

  // Show loading state while auth is being checked or during redirect
  if (authLoading || isRedirecting) {
    return (
      <div className="min-h-screen py-12 bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-300">
            {isRedirecting ? 'Redirecting...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen py-12 bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4">
        <LoginForm />
      </div>
    </div>
  );
}

function LoginPageLoading() {
  return (
    <div className="min-h-screen py-12 bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-4"></div>
        <p className="text-gray-600 dark:text-gray-300">Loading...</p>
      </div>
    </div>
  );
}

export default function LoginPage() {
  return (
    <>
      <Head>
        <title>Sign In - Hiel Tech</title>
        <meta name="description" content="Sign in to your Hiel Tech account" />
        <meta name="robots" content="noindex" />
      </Head>
      <Suspense fallback={<LoginPageLoading />}>
        <LoginPageContent />
      </Suspense>
    </>
  );
}