import { Metadata } from 'next';
import SocialMediaPlanner from '@/components/social-media/SocialMediaPlanner';

export const metadata: Metadata = {
  title: 'Social Media Planner | Hiel Tech',
  description: 'Plan, schedule, and manage your Instagram and Facebook content with our powerful social media planning tools.',
  keywords: ['social media planner', 'Instagram scheduler', 'Facebook posts', 'content calendar', 'social media management'],
};

export default function SocialMediaPlannerPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Social Media Planner
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Streamline your social media strategy with our integrated Instagram and Facebook planning tools. 
            Create, schedule, and analyze your content all in one place.
          </p>
          <div className="mt-6 flex justify-center">
            <div className="inline-flex items-center space-x-4 bg-white dark:bg-gray-800 rounded-full px-6 py-3 shadow-lg">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-pink-500 rounded-full"></div>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Instagram</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Facebook</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-purple-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-purple-600 dark:text-purple-400">Beta</span>
              </div>
            </div>
          </div>
        </div>

        {/* Social Media Planner Component */}
        <SocialMediaPlanner className="max-w-7xl mx-auto" />

        {/* Feature Highlights */}
        <div className="mt-16 max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              Powerful Features for Social Media Success
            </h2>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Everything you need to create engaging content and grow your social media presence
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Instagram Integration */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
              <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-purple-500 rounded-lg flex items-center justify-center mb-4">
                <span className="text-white text-xl">📸</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                Instagram Business API
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Full integration with Instagram Business API for posting, analytics, and audience insights.
              </p>
              <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
                <li>• Post images, videos, and carousels</li>
                <li>• Instagram Stories support</li>
                <li>• Real-time analytics</li>
                <li>• Hashtag suggestions</li>
              </ul>
            </div>

            {/* Facebook Integration */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center mb-4">
                <span className="text-white text-xl">📱</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                Facebook Graph API
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Connect your Facebook pages and manage your content with advanced scheduling features.
              </p>
              <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
                <li>• Multi-page management</li>
                <li>• Scheduled publishing</li>
                <li>• Page insights & metrics</li>
                <li>• Cross-platform posting</li>
              </ul>
            </div>

            {/* Content Calendar */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg border border-gray-200 dark:border-gray-700">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center mb-4">
                <span className="text-white text-xl">📅</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                Smart Content Calendar
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Plan your content strategy with our intelligent calendar that suggests optimal posting times.
              </p>
              <ul className="text-sm text-gray-500 dark:text-gray-400 space-y-1">
                <li>• Drag-and-drop scheduling</li>
                <li>• Optimal timing suggestions</li>
                <li>• Content templates</li>
                <li>• Bulk operations</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Beta Notice */}
        <div className="mt-16 max-w-4xl mx-auto">
          <div className="bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-8 border border-purple-200 dark:border-purple-800">
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl">🚀</span>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                Welcome to the Beta!
              </h3>
                             <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-2xl mx-auto">
                 You&apos;re experiencing the early version of our Social Media Planner. We&apos;ve integrated the foundation 
                 for Instagram and Facebook Graph APIs, and we&apos;re actively building the complete feature set.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-md">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-2">✅ Available Now</h4>
                  <ul className="text-sm text-gray-600 dark:text-gray-400 text-left">
                    <li>• Facebook OAuth integration</li>
                    <li>• Instagram API foundation</li>
                    <li>• Account connection UI</li>
                    <li>• Base dashboard structure</li>
                  </ul>
                </div>
                <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-md">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-2">🔜 Coming Soon</h4>
                  <ul className="text-sm text-gray-600 dark:text-gray-400 text-left">
                    <li>• Full posting capabilities</li>
                    <li>• Advanced scheduling</li>
                    <li>• Analytics dashboard</li>
                    <li>• Content templates</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 