import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Data Deletion | Hiel Tech',
  description: 'Request deletion of your personal data from Hiel Tech services. Required for Facebook integration compliance.',
  keywords: ['data deletion', 'delete account', 'privacy rights', 'GDPR', 'data removal'],
};

export default function DataDeletionPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Data Deletion Request
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-400">
              Request deletion of your personal data from our systems
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-4">
              This page is required for Facebook integration compliance
            </p>
          </div>

          {/* Content */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 space-y-8">
            
            {/* Important Notice */}
            <section>
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mt-0.5">
                    <span className="text-red-600 dark:text-red-400 text-xs">⚠️</span>
                  </div>
                  <div>
                    <h2 className="text-lg font-semibold text-red-900 dark:text-red-300 mb-2">
                      Important Notice
                    </h2>
                    <p className="text-red-800 dark:text-red-200 text-sm">
                      Data deletion is permanent and cannot be undone. Please ensure you have exported any data you wish to keep before proceeding with deletion.
                    </p>
                  </div>
                </div>
              </div>
            </section>

            {/* What Data We Delete */}
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                What Data Will Be Deleted
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-blue-900 dark:text-blue-300 mb-2">
                    Account Information
                  </h3>
                  <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                    <li>• Personal profile data</li>
                    <li>• Email address and contact info</li>
                    <li>• Authentication credentials</li>
                    <li>• Account preferences</li>
                  </ul>
                </div>

                <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-green-900 dark:text-green-300 mb-2">
                    Content Data
                  </h3>
                  <ul className="text-sm text-green-800 dark:text-green-200 space-y-1">
                    <li>• HielLinks profile content</li>
                    <li>• Social media posts and drafts</li>
                    <li>• Uploaded media files</li>
                    <li>• Custom themes and settings</li>
                  </ul>
                </div>

                <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-purple-900 dark:text-purple-300 mb-2">
                    Usage Data
                  </h3>
                  <ul className="text-sm text-purple-800 dark:text-purple-200 space-y-1">
                    <li>• Analytics and usage statistics</li>
                    <li>• Click and interaction data</li>
                    <li>• Session logs</li>
                    <li>• Device and browser information</li>
                  </ul>
                </div>

                <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4">
                  <h3 className="text-lg font-medium text-orange-900 dark:text-orange-300 mb-2">
                    Communication Data
                  </h3>
                  <ul className="text-sm text-orange-800 dark:text-orange-200 space-y-1">
                    <li>• Support chat history</li>
                    <li>• Email correspondence</li>
                    <li>• Feedback and survey responses</li>
                    <li>• Notification preferences</li>
                  </ul>
                </div>
              </div>
            </section>

            {/* Data Retention */}
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                Data Retention Policy
              </h2>
              
              <div className="space-y-4">
                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                  <h3 className="font-medium text-yellow-900 dark:text-yellow-300 mb-2">Legal Requirements</h3>
                  <p className="text-yellow-800 dark:text-yellow-200 text-sm">
                    Some data may be retained for legal compliance, fraud prevention, or legitimate business purposes as required by applicable laws. This includes:
                  </p>
                  <ul className="text-yellow-800 dark:text-yellow-200 text-sm mt-2 space-y-1">
                    <li>• Financial transaction records (7 years)</li>
                    <li>• Legal correspondence and disputes</li>
                    <li>• Fraud prevention and security logs</li>
                    <li>• Tax and accounting records</li>
                  </ul>
                </div>

                <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                  <h3 className="font-medium text-green-900 dark:text-green-300 mb-2">Complete Deletion</h3>
                  <p className="text-green-800 dark:text-green-200 text-sm">
                    All other personal data will be permanently deleted within 30 days of your request, including:
                  </p>
                  <ul className="text-green-800 dark:text-green-200 text-sm mt-2 space-y-1">
                    <li>• All profile and account information</li>
                    <li>• User-generated content and media</li>
                    <li>• Marketing and communication preferences</li>
                    <li>• Analytics data linked to your identity</li>
                  </ul>
                </div>
              </div>
            </section>

            {/* Deletion Process */}
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                How to Request Data Deletion
              </h2>
              
              <div className="space-y-6">
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Option 1: Account Settings (Recommended)</h3>
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 dark:text-blue-400 text-sm font-medium">1</span>
                      </div>
                    </div>
                    <div className="flex-1">
                      <p className="text-gray-600 dark:text-gray-300 text-sm">
                        Log into your account and navigate to Settings → Privacy → Delete Account
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-4 mt-3">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 dark:text-blue-400 text-sm font-medium">2</span>
                      </div>
                    </div>
                    <div className="flex-1">
                      <p className="text-gray-600 dark:text-gray-300 text-sm">
                        Confirm your identity and follow the deletion process
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-4 mt-3">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                        <span className="text-blue-600 dark:text-blue-400 text-sm font-medium">3</span>
                      </div>
                    </div>
                    <div className="flex-1">
                      <p className="text-gray-600 dark:text-gray-300 text-sm">
                        Receive confirmation email and final deletion notice
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Option 2: Email Request</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    If you cannot access your account, send an email to our privacy team:
                  </p>
                  <div className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                    <p className="text-blue-600 dark:text-blue-400 font-medium"><EMAIL></p>
                    <p className="text-gray-600 dark:text-gray-300 text-sm mt-2">
                      Include: Your full name, email address associated with the account, and reason for deletion request
                    </p>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Option 3: Facebook Users</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    If you signed up using Facebook Login, you can also request deletion through Facebook&apos;s privacy settings or by contacting us directly.
                  </p>
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
                    <p className="text-blue-800 dark:text-blue-200 text-sm">
                      <strong>Note:</strong> This will also disconnect your Facebook account from our services.
                    </p>
                  </div>
                </div>
              </div>
            </section>

            {/* Timeline */}
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                Deletion Timeline
              </h2>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 dark:text-blue-400 font-medium">24h</span>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white">Request Acknowledgment</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">We confirm receipt of your deletion request</p>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center">
                    <span className="text-yellow-600 dark:text-yellow-400 font-medium">7d</span>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white">Identity Verification</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">We verify your identity and process the request</p>
                  </div>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                    <span className="text-green-600 dark:text-green-400 font-medium">30d</span>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white">Complete Deletion</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">All eligible data is permanently deleted from our systems</p>
                  </div>
                </div>
              </div>
            </section>

            {/* Before You Delete */}
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                Before You Delete Your Data
              </h2>
              
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
                <h3 className="font-medium text-blue-900 dark:text-blue-300 mb-3">Consider These Alternatives</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-2">
                    <li>• Export your data first (available in account settings)</li>
                    <li>• Temporarily deactivate your account instead</li>
                    <li>• Update your privacy settings to limit data collection</li>
                  </ul>
                  <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-2">
                    <li>• Contact support to discuss specific concerns</li>
                    <li>• Review our privacy policy for data usage details</li>
                    <li>• Consider keeping data for business continuity</li>
                  </ul>
                </div>
              </div>
            </section>

            {/* Contact Information */}
            <section>
              <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                Contact Information
              </h2>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  For questions about data deletion or privacy rights:
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">Privacy Officer</h4>
                    <p className="text-blue-600 dark:text-blue-400"><EMAIL></p>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">Responds within 24-48 hours</p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-white mb-2">General Support</h4>
                    <p className="text-blue-600 dark:text-blue-400"><EMAIL></p>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">For general account questions</p>
                  </div>
                </div>
              </div>
            </section>

          </div>

          {/* Footer */}
          <div className="text-center mt-12">
            <p className="text-gray-500 dark:text-gray-400 text-sm">
              This data deletion process complies with GDPR, CCPA, and Facebook&apos;s data deletion requirements.
            </p>
            <p className="text-gray-500 dark:text-gray-400 text-sm mt-2">
              Last updated: {new Date().toLocaleDateString('en-US', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
} 