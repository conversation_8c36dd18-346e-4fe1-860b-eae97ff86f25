'use client';

import { useState } from 'react';
import Link from 'next/link';

export default function Features() {
  const [activeFeature, setActiveFeature] = useState(0);

  const features = [
    {
      id: 'hiellinks',
      title: 'HielLinks',
      icon: '🔗',
      description: 'Create beautiful, customizable link-in-bio pages that convert visitors into customers.',
      longDescription: 'HielLinks is our flagship feature that allows you to create stunning, fully customizable link-in-bio pages. Perfect for social media creators, businesses, and influencers who want to showcase multiple links in a beautiful, cohesive design.',
      benefits: [
        'Unlimited custom links with advanced analytics',
        'Real-time click tracking and visitor insights',
        'QR code generation for offline marketing',
        'Google Maps integration for location-based businesses',
        'Custom themes and branding options',
        'Mobile-first responsive design',
        'SEO optimized for better discoverability'
      ],
      color: 'blue',
      gradient: 'from-blue-500 to-blue-600',
      status: 'available',
      demoUrl: '/profile/hiellinks'
    },
    {
      id: 'project-management',
      title: 'Project Management',
      icon: '📋',
      description: 'Comprehensive project management suite with Kanban boards, Gantt charts, and collaboration tools.',
      longDescription: 'Our integrated project management system helps teams stay organized and productive. From small personal projects to large enterprise initiatives, we provide all the tools you need to succeed.',
      benefits: [
        'Multiple view options: Kanban, Gantt, Calendar, and List',
        'Real-time team collaboration and comments',
        'Time tracking and productivity analytics',
        'File attachments and document management',
        'Custom workflows and automation',
        'Integration with chat system for seamless communication',
        'Advanced reporting and project insights'
      ],
      color: 'green',
      gradient: 'from-green-500 to-green-600',
      status: 'available',
      demoUrl: '/admin'
    },
    {
      id: 'smart-chat',
      title: 'Smart Chat System',
      icon: '💬',
      description: 'AI-powered customer support and team communication platform.',
      longDescription: 'Connect with your customers and team members through our intelligent chat system. Featuring AI-powered responses, file sharing, and advanced moderation tools.',
      benefits: [
        'Real-time messaging with instant notifications',
        'AI-powered auto-responses and support',
        'File and media sharing capabilities',
        'Advanced security and moderation features',
        'Multi-channel support (web, mobile, API)',
        'Conversation history and search',
        'Integration with team management system'
      ],
      color: 'purple',
      gradient: 'from-purple-500 to-purple-600',
      status: 'available',
      demoUrl: '/admin'
    },
    {
      id: 'content-management',
      title: 'Content Management',
      icon: '📝',
      description: 'Full-featured blog and content management system with SEO optimization.',
      longDescription: 'Create, manage, and publish content with our powerful CMS. Built for content creators, marketers, and businesses who want to establish thought leadership.',
      benefits: [
        'Rich text editor with advanced formatting',
        'Multiple content templates and layouts',
        'SEO optimization and meta management',
        'Media library with compression and optimization',
        'Content scheduling and publishing workflows',
        'Categories, tags, and content organization',
        'Analytics and performance tracking'
      ],
      color: 'orange',
      gradient: 'from-orange-500 to-orange-600',
      status: 'available',
      demoUrl: '/blog'
    },
    {
      id: 'team-management',
      title: 'Team Management',
      icon: '👥',
      description: 'Complete team management solution with role-based access and workflows.',
      longDescription: 'Manage your team effectively with our comprehensive team management system. Handle applications, assign roles, and collaborate seamlessly.',
      benefits: [
        'Role-based access control and permissions',
        'Team application and onboarding workflows',
        'Employee profiles and skill tracking',
        'Performance metrics and team analytics',
        'Integration with project management',
        'Communication and collaboration tools',
        'Advanced security and compliance features'
      ],
      color: 'indigo',
      gradient: 'from-indigo-500 to-indigo-600',
      status: 'available',
      demoUrl: '/team'
    },
    {
      id: 'social-media-planner',
      title: 'Social Media Planner',
      icon: '📱',
      description: 'AI-powered social media scheduling and planning tool.',
      longDescription: 'Coming soon! Our AI-powered social media planner will help you create, schedule, and optimize your social media content across multiple platforms.',
      benefits: [
        'AI-powered content suggestions and optimization',
        'Multi-platform posting and scheduling',
        'Optimal timing analysis for maximum engagement',
        'Content calendar and planning tools',
        'Hashtag research and recommendations',
        'Performance analytics and insights',
        'Team collaboration for content creation'
      ],
      color: 'pink',
      gradient: 'from-pink-500 to-pink-600',
      status: 'coming-soon',
      demoUrl: null
    }
  ];

  const upcomingFeatures = [
    {
      title: 'AI Content Generator',
      description: 'Generate high-quality content using advanced AI models.',
      icon: '🤖',
      eta: 'Q3 2025'
    },
    {
      title: 'Advanced Analytics',
      description: 'Deep insights with predictive analytics and custom dashboards.',
      icon: '📊',
      eta: 'Q4 2025'
    },
    {
      title: 'API Integrations',
      description: 'Connect with your favorite tools through our comprehensive API.',
      icon: '🔌',
      eta: 'Q2 2025'
    },
    {
      title: 'White-label Solutions',
      description: 'Brand the platform with your own logo and colors.',
      icon: '🏷️',
      eta: 'Q4 2025'
    }
  ];

  const testimonials = [
    {
      quote: "HielTech&apos;s platform has transformed how we manage our digital presence. The HielLinks feature alone has increased our conversion rate by 300%!",
      author: "Sarah Johnson",
      position: "Marketing Director",
      company: "TechStartup Inc.",
      avatar: "SJ"
    },
    {
      quote: "The project management tools are incredibly intuitive. Our team productivity has improved significantly since we started using HielTech.",
      author: "Ahmed Benali",
      position: "Project Manager",
      company: "Digital Agency",
      avatar: "AB"
    },
    {
      quote: "As a content creator, HielLinks has made it so easy to share all my platforms in one beautiful page. The analytics are amazing!",
      author: "Maria Garcia",
      position: "Content Creator",
      company: "@MariaCreates",
      avatar: "MG"
    }
  ];

  return (
    <div className="flex flex-col min-h-screen pt-20">
      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 relative overflow-hidden">
        {/* Background Animation */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 left-10 w-20 h-20 bg-blue-500 rounded-full animate-pulse"></div>
          <div className="absolute top-40 right-20 w-16 h-16 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: "1s" }}></div>
          <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-indigo-500 rounded-full animate-pulse" style={{ animationDelay: "2s" }}></div>
          <div className="absolute bottom-40 right-1/3 w-8 h-8 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: "0.5s" }}></div>
        </div>

        <div className="container mx-auto relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <div className="inline-flex items-center px-4 py-2 bg-blue-100 dark:bg-blue-900/30 rounded-full text-blue-600 dark:text-blue-400 text-sm font-medium mb-6">
              🚀 Comprehensive Feature Suite
            </div>
            <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Everything You Need to Succeed
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
              From link management to project coordination, our platform provides all the tools modern businesses need to thrive in the digital age.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/login"
                className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-medium rounded-lg hover:from-blue-700 hover:to-purple-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                Start Free Trial
              </Link>
              <Link
                href="/contact"
                className="px-8 py-4 border border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-300 font-medium rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              >
                Schedule Demo
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Interactive Features Showcase */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-6 text-gray-900 dark:text-white">
              Explore Our Features
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Click on any feature below to learn more about how it can help your business grow.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Feature Navigation */}
            <div className="space-y-4">
              {features.map((feature, index) => (
                <div
                  key={feature.id}
                  className={`cursor-pointer p-6 rounded-2xl border-2 transition-all duration-300 ${
                    activeFeature === index
                      ? `border-${feature.color}-500 bg-${feature.color}-50 dark:bg-${feature.color}-900/20 shadow-lg transform scale-105`
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600 bg-white dark:bg-gray-800'
                  }`}
                  onClick={() => setActiveFeature(index)}
                >
                  <div className="flex items-start space-x-4">
                    <div className={`w-12 h-12 bg-gradient-to-br ${feature.gradient} rounded-xl flex items-center justify-center text-white text-xl flex-shrink-0`}>
                      {feature.icon}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                          {feature.title}
                        </h3>
                        {feature.status === 'coming-soon' && (
                          <span className="bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 text-xs px-2 py-1 rounded-full font-medium">
                            Coming Soon
                          </span>
                        )}
                      </div>
                      <p className="text-gray-600 dark:text-gray-300">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Feature Details */}
            <div className="bg-white dark:bg-gray-800 rounded-3xl p-8 shadow-xl border border-gray-200 dark:border-gray-700 sticky top-8">
              <div className="mb-6">
                <div className={`w-16 h-16 bg-gradient-to-br ${features[activeFeature].gradient} rounded-2xl flex items-center justify-center text-white text-2xl mb-4`}>
                  {features[activeFeature].icon}
                </div>
                <h3 className="text-3xl font-bold mb-4 text-gray-900 dark:text-white">
                  {features[activeFeature].title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed mb-6">
                  {features[activeFeature].longDescription}
                </p>
              </div>

              <div className="mb-8">
                <h4 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
                  Key Benefits
                </h4>
                <ul className="space-y-3">
                  {features[activeFeature].benefits.map((benefit, index) => (
                    <li key={index} className="flex items-start">
                      <span className={`w-2 h-2 bg-${features[activeFeature].color}-500 rounded-full mt-2 mr-3 flex-shrink-0`}></span>
                      <span className="text-gray-600 dark:text-gray-300">{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {features[activeFeature].status === 'available' && features[activeFeature].demoUrl && (
                <Link
                  href={features[activeFeature].demoUrl}
                  className={`w-full bg-gradient-to-r ${features[activeFeature].gradient} text-white font-medium py-3 px-6 rounded-lg hover:shadow-lg transform hover:scale-105 transition-all duration-300 text-center block`}
                >
                  Try {features[activeFeature].title}
                </Link>
              )}

              {features[activeFeature].status === 'coming-soon' && (
                <button
                  disabled
                  className="w-full bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 font-medium py-3 px-6 rounded-lg cursor-not-allowed text-center block"
                >
                  Coming Soon
                </button>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Upcoming Features */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-6 text-gray-900 dark:text-white">
              What&apos;s Coming Next
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              We&apos;re constantly innovating and adding new features to help you stay ahead of the competition.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {upcomingFeatures.map((feature, index) => (
              <div
                key={index}
                className="bg-white dark:bg-gray-800 p-6 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
              >
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-bold mb-2 text-gray-900 dark:text-white">
                  {feature.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {feature.description}
                </p>
                <div className="bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 text-sm px-3 py-1 rounded-full font-medium inline-block">
                  ETA: {feature.eta}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-6 text-gray-900 dark:text-white">
              What Our Users Say
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Don&apos;t just take our word for it. Here&apos;s what real users are saying about our platform.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div
                key={index}
                className="bg-white dark:bg-gray-800 p-8 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300"
              >
                <div className="mb-6">
                  <svg className="w-8 h-8 text-blue-600 dark:text-blue-400 mb-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609L24 4.266c-4.266 1.171-6.584 4.266-6.584 8.171V21h-3.399zM0 21v-7.391c0-5.704 3.748-9.57 9-10.609L9.983 4.266C5.717 5.437 3.4 8.532 3.4 12.875V21H0z"/>
                  </svg>
                                  <p className="text-gray-600 dark:text-gray-300 text-lg leading-relaxed italic">
                  &ldquo;{testimonial.quote}&rdquo;
                </p>
                </div>
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold mr-4">
                    {testimonial.avatar}
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900 dark:text-white">
                      {testimonial.author}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {testimonial.position} at {testimonial.company}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="container mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Ready to Get Started?
          </h2>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto mb-8">
            Join thousands of businesses already using HielTech to grow their digital presence. Start your free trial today!
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/login"
              className="px-8 py-4 bg-white text-blue-600 font-medium rounded-lg hover:bg-gray-100 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              Start Free Trial
            </Link>
            <Link
              href="/contact"
              className="px-8 py-4 border-2 border-white text-white font-medium rounded-lg hover:bg-white hover:text-blue-600 transition-all duration-300"
            >
              Contact Sales
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
} 