@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #f3f4f6;
  --secondary-foreground: #1f2937;
  --muted: #f3f4f6;
  --muted-foreground: #6b7280;
  --accent: #f3f4f6;
  --accent-foreground: #1f2937;
  --card: #ffffff;
  --card-foreground: #171717;
  --border: #e5e7eb;
  
  /* Theme variables */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-border: var(--border);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --primary: #60a5fa;
    --primary-foreground: #ffffff;
    --secondary: #1f2937;
    --secondary-foreground: #f3f4f6;
    --muted: #1f2937;
    --muted-foreground: #9ca3af;
    --accent: #1f2937;
    --accent-foreground: #f3f4f6;
    --card: #111827;
    --card-foreground: #f3f4f6;
    --border: #374151;
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
  color: rgb(var(--foreground-rgb));
  font-family: var(--font-sans);
}

.animate-fade-in {
  animation: fadeIn 1s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

/* Additional fantastic animations for features */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.3); }
  50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.6), 0 0 30px rgba(59, 130, 246, 0.4); }
}

@keyframes sparkle {
  0%, 20%, 50%, 80%, 100% { opacity: 0; transform: scale(0.5) rotate(0deg); }
  40% { opacity: 1; transform: scale(1) rotate(180deg); }
  60% { opacity: 1; transform: scale(1) rotate(360deg); }
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

@keyframes bounceIn {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes slideInLeft {
  from { transform: translateX(-50px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes zoomIn {
  from { transform: scale(0.8); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes rotateIn {
  from { transform: rotate(-200deg); opacity: 0; }
  to { transform: rotate(0); opacity: 1; }
}

@keyframes heartbeat {
  0%, 14%, 28%, 42%, 70% { transform: scale(1); }
  7%, 21%, 35% { transform: scale(1.1); }
}

/* Animation classes */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.animate-sparkle {
  animation: sparkle 1.5s linear infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

.dark .animate-shimmer {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200px 100%;
}

.animate-bounce-in {
  animation: bounceIn 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-zoom-in {
  animation: zoomIn 0.5s ease-out;
}

.animate-rotate-in {
  animation: rotateIn 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite;
}

/* Staggered animation delays */
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-400 { animation-delay: 0.4s; }
.animate-delay-500 { animation-delay: 0.5s; }
.animate-delay-700 { animation-delay: 0.7s; }
.animate-delay-1000 { animation-delay: 1s; }

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.dark .hover-lift:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.hover-glow {
  transition: all 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
  transform: scale(1.02);
}

/* Gradient text animations */
.gradient-text {
  background: linear-gradient(-45deg, #3b82f6, #8b5cf6, #06b6d4, #10b981);
  background-size: 400% 400%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientShift 4s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Interactive button effects */
.btn-interactive {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-interactive::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-interactive:hover::before {
  left: 100%;
}

/* Particle effect background */
.particle-bg {
  position: relative;
  overflow: hidden;
}

.particle-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(circle, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: particleMove 20s linear infinite;
  pointer-events: none;
}

@keyframes particleMove {
  0% { transform: translateY(0) translateX(0); }
  100% { transform: translateY(-50px) translateX(-50px); }
}

/* Tiptap Editor Styles */
.ProseMirror {
  outline: none;
  padding: 1rem;
  min-height: 300px;
}

.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

.ProseMirror table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  overflow: hidden;
}

.ProseMirror table td,
.ProseMirror table th {
  min-width: 1em;
  border: 2px solid #ced4da;
  padding: 3px 5px;
  vertical-align: top;
  box-sizing: border-box;
  position: relative;
}

.ProseMirror table th {
  font-weight: bold;
  text-align: left;
  background-color: #f1f3f4;
}

.ProseMirror table .selectedCell:after {
  z-index: 2;
  position: absolute;
  content: "";
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(200, 200, 255, 0.4);
  pointer-events: none;
}

.ProseMirror table .column-resize-handle {
  position: absolute;
  right: -2px;
  top: 0;
  bottom: -2px;
  width: 4px;
  background-color: #adf;
  pointer-events: none;
}

.ProseMirror table p {
  margin: 0;
}

.ProseMirror .tableWrapper {
  padding: 1rem 0;
  overflow-x: auto;
}

.ProseMirror .resize-cursor {
  cursor: ew-resize;
  cursor: col-resize;
}

/* Code block styles */
.ProseMirror pre {
  background: #0d1117;
  color: #c9d1d9;
  font-family: 'JetBrainsMono', 'SFMono-Regular', 'SF Mono', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
}

.ProseMirror pre code {
  color: inherit;
  padding: 0;
  background: none;
  font-size: 0.8rem;
}

.ProseMirror code {
  background-color: rgba(97, 97, 97, 0.1);
  border-radius: 0.25rem;
  color: #c7254e;
  font-size: 0.85em;
  padding: 0.25em 0.4em;
}

/* Dark mode adjustments */
.dark .ProseMirror table th {
  background-color: #374151;
  color: #f9fafb;
}

.dark .ProseMirror table td,
.dark .ProseMirror table th {
  border-color: #4b5563;
}

.dark .ProseMirror code {
  background-color: rgba(156, 163, 175, 0.1);
  color: #f472b6;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  /* Hide scrollbar for mobile navigation */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }
  .scrollbar-hide::-webkit-scrollbar { 
    display: none;  /* Safari and Chrome */
  }

  /* Mobile-optimized touch targets */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Better mobile table handling */
  .mobile-table-cell {
    @apply px-2 py-3 text-sm;
  }

  /* Improved focus styles for mobile */
  .focus-mobile {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  }

  /* Line clamp utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  
  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Mobile-friendly animations */
  @media (prefers-reduced-motion: reduce) {
    .motion-reduce {
      animation: none;
      transition: none;
    }
  }

  /* Better touch feedback */
  .touch-feedback {
    @apply active:scale-95 transition-transform duration-150;
  }

  /* Mobile responsive text */
  .text-responsive {
    @apply text-sm sm:text-base lg:text-lg;
  }

  .text-responsive-sm {
    @apply text-xs sm:text-sm lg:text-base;
  }

  /* Safe area handling for mobile devices */
  .safe-area-inset {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Better mobile card shadows */
  .card-shadow-mobile {
    @apply shadow-sm hover:shadow-md transition-shadow duration-200;
  }

  /* Mobile-optimized spacing */
  .mobile-padding {
    @apply px-4 py-3 sm:px-6 sm:py-4;
  }

  .mobile-margin {
    @apply mx-4 my-3 sm:mx-6 sm:my-4;
  }
}
