// Organization Management Types
export interface Organization {
  id: string;
  name: string;
  slug: string; // for custom URLs like org.hieltech.com
  description?: string;
  logo_url?: string;
  domain?: string; // for email domain verification
  industry?: string;
  size: 'startup' | 'small' | 'medium' | 'large' | 'enterprise';
  subscription_tier: 'free' | 'pro' | 'enterprise';
  country?: string;
  timezone?: string;
  website?: string;
  phone?: string;
  address?: string;
  settings: OrganizationSettings;
  stats: OrganizationStats;
  is_active: boolean;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface OrganizationSettings {
  // Project Management Settings
  default_project_visibility: 'private' | 'organization' | 'public';
  enable_time_tracking: boolean;
  enable_budget_tracking: boolean;
  default_task_priority: 'low' | 'medium' | 'high' | 'urgent';
  
  // Communication Settings
  enable_public_chat: boolean;
  enable_guest_access: boolean;
  chat_retention_days: number;
  
  // Security Settings
  require_2fa: boolean;
  allowed_domains: string[];
  session_timeout_minutes: number;
  
  // Feature Access
  features: {
    advanced_analytics: boolean;
    custom_branding: boolean;
    api_access: boolean;
    integrations: boolean;
    custom_fields: boolean;
    advanced_permissions: boolean;
  };
  
  // Customization
  brand_colors: {
    primary: string;
    secondary: string;
    accent: string;
  };
  custom_domain?: string;
}

export interface OrganizationStats {
  total_members: number;
  active_projects: number;
  completed_projects: number;
  total_tasks: number;
  completed_tasks: number;
  storage_used_mb: number;
  storage_limit_mb: number;
}

export interface OrganizationMember {
  id: string;
  organization_id: string;
  user_id: string;
  role: 'owner' | 'admin' | 'project_manager' | 'member' | 'viewer' | 'guest';
  permissions: OrganizationPermission[];
  department?: string;
  title?: string;
  employee_id?: string;
  hourly_rate?: number;
  joined_at: string;
  invited_by?: string;
  invitation_token?: string;
  invitation_expires_at?: string;
  status: 'active' | 'inactive' | 'pending_invitation' | 'suspended';
  last_activity_at?: string;
  user?: {
    id: string;
    display_name?: string;
    email: string;
    avatar_url?: string;
  };
}

export type OrganizationPermission = 
  | 'organization.manage'
  | 'organization.settings'
  | 'organization.billing'
  | 'members.invite'
  | 'members.manage'
  | 'members.remove'
  | 'projects.create'
  | 'projects.edit'
  | 'projects.delete'
  | 'projects.view_all'
  | 'tasks.create'
  | 'tasks.edit'
  | 'tasks.delete'
  | 'tasks.assign'
  | 'channels.create'
  | 'channels.manage'
  | 'channels.moderate'
  | 'analytics.view'
  | 'billing.manage';

export interface OrganizationInvitation {
  id: string;
  organization_id: string;
  email: string;
  role: OrganizationMember['role'];
  permissions: OrganizationPermission[];
  invited_by: string;
  expires_at: string;
  status: 'pending' | 'accepted' | 'expired' | 'revoked';
  token: string;
  message?: string;
  created_at: string;
}

// Enhanced Project Types for Organizations
export interface OrganizationProject {
  id: string;
  organization_id: string;
  name: string;
  description?: string;
  status: 'planning' | 'active' | 'on_hold' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'critical';
  visibility: 'private' | 'organization' | 'public';
  
  // Project Details
  start_date?: string;
  due_date?: string;
  budget?: number;
  estimated_hours?: number;
  actual_hours?: number;
  
  // Team Assignment
  project_manager_id?: string;
  team_members: string[]; // user_ids
  client_info?: {
    name?: string;
    email?: string;
    company?: string;
  };
  
  // Project Structure
  tags: string[];
  custom_fields: Record<string, string | number | boolean>;
  
  // Analytics
  progress_percentage: number;
  tasks_total: number;
  tasks_completed: number;
  
  created_by: string;
  created_at: string;
  updated_at: string;
}

// Enhanced Task Types for Organizations
export interface OrganizationTask {
  id: string;
  organization_id: string;
  project_id?: string;
  
  title: string;
  description?: string;
  status: 'todo' | 'in_progress' | 'review' | 'done' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  
  // Assignment
  assignee_id?: string;
  assignee?: OrganizationMember['user'];
  reporter_id: string;
  
  // Time Management
  due_date?: string;
  estimated_hours?: number;
  actual_hours?: number;
  time_entries: TimeEntry[];
  
  // Dependencies
  dependencies: string[]; // task_ids
  subtasks: string[]; // task_ids
  parent_task_id?: string;
  
  // Labels and Organization
  labels: string[];
  custom_fields: Record<string, string | number | boolean>;
  
  // Comments and Activity
  comments_count: number;
  attachments_count: number;
  
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface TimeEntry {
  id: string;
  task_id: string;
  user_id: string;
  description?: string;
  hours: number;
  date: string;
  is_billable: boolean;
  created_at: string;
}

// Enhanced Chat Types for Organizations
export interface OrganizationChannel {
  id: string;
  organization_id: string;
  name: string;
  description?: string;
  type: 'public' | 'private' | 'direct' | 'project';
  
  // Channel Settings
  is_archived: boolean;
  is_readonly: boolean;
  message_retention_days?: number;
  
  // Project Integration
  project_id?: string;
  
  // Members
  members: string[]; // user_ids
  admins: string[]; // user_ids
  
  // Analytics
  message_count: number;
  last_activity_at?: string;
  
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface OrganizationMessage {
  id: string;
  channel_id: string;
  sender_id: string;
  sender?: OrganizationMember['user'];
  
  content: string;
  message_type: 'text' | 'file' | 'system' | 'task_update' | 'project_update';
  
  // Rich Content
  attachments: MessageAttachment[];
  mentions: string[]; // user_ids
  reactions: MessageReaction[];
  
  // Threading
  thread_id?: string;
  reply_count: number;
  
  // Editing
  is_edited: boolean;
  edit_history: MessageEdit[];
  
  // System Messages
  system_data?: {
    type: 'member_joined' | 'member_left' | 'project_created' | 'task_completed';
    data: Record<string, unknown>;
  };
  
  created_at: string;
  updated_at: string;
}

export interface MessageAttachment {
  id: string;
  filename: string;
  file_size: number;
  mime_type: string;
  url: string;
  thumbnail_url?: string;
}

export interface MessageReaction {
  emoji: string;
  users: string[]; // user_ids
  count: number;
}

export interface MessageEdit {
  previous_content: string;
  edited_at: string;
  edited_by: string;
}

// Organization Analytics
export interface OrganizationAnalytics {
  id: string;
  organization_id: string;
  date: string;
  
  // Activity Metrics
  active_users: number;
  messages_sent: number;
  tasks_created: number;
  tasks_completed: number;
  projects_created: number;
  
  // Performance Metrics
  avg_task_completion_time: number;
  on_time_delivery_rate: number;
  team_productivity_score: number;
  
  // Communication Metrics
  channels_active: number;
  avg_response_time_minutes: number;
  collaboration_score: number;
  
  created_at: string;
}

// Subscription and Billing
export interface OrganizationSubscription {
  id: string;
  organization_id: string;
  tier: 'free' | 'pro' | 'enterprise';
  status: 'active' | 'cancelled' | 'past_due' | 'trialing';
  
  // Limits based on tier
  limits: {
    max_members: number;
    max_projects: number;
    max_storage_gb: number;
    max_integrations: number;
  };
  
  // Billing
  current_period_start: string;
  current_period_end: string;
  next_billing_date?: string;
  amount: number;
  currency: string;
  
  // Payment
  payment_method?: string;
  billing_email?: string;
  
  created_at: string;
  updated_at: string;
}

export interface OrganizationNotification {
  id: string;
  organization_id: string;
  user_id: string;
  type: 'project_assigned' | 'task_assigned' | 'task_completed' | 'mention' | 'invitation' | 'system';
  title: string;
  message: string;
  data: Record<string, unknown>; // Additional context data
  is_read: boolean;
  created_at: string;
}

export interface OrganizationActivity {
  id: string;
  organization_id: string;
  user_id: string;
  action: 'created' | 'updated' | 'deleted' | 'completed' | 'assigned';
  resource_type: 'project' | 'task' | 'member' | 'channel' | 'message';
  resource_id: string;
  details: Record<string, unknown>;
  created_at: string;
}

// Analytics types
export interface ProjectAnalytics {
  project_id: string;
  completion_rate: number;
  average_task_time: number;
  budget_utilization: number;
  team_productivity_score: number;
  on_time_delivery_rate: number;
  risk_score: number;
  timeline_data: TimelinePoint[];
  task_distribution: TaskDistribution[];
}

export interface TimelinePoint {
  date: string;
  planned_progress: number;
  actual_progress: number;
  budget_spent: number;
  tasks_completed: number;
}

export interface TaskDistribution {
  status: OrganizationTask['status'];
  count: number;
  percentage: number;
}

export interface TeamAnalytics {
  organization_id: string;
  period: 'week' | 'month' | 'quarter' | 'year';
  total_productivity_score: number;
  member_productivity: MemberProductivity[];
  project_performance: ProjectPerformance[];
  collaboration_metrics: CollaborationMetrics;
}

export interface MemberProductivity {
  user_id: string;
  display_name: string;
  tasks_completed: number;
  hours_logged: number;
  productivity_score: number;
  projects_contributed: number;
}

export interface ProjectPerformance {
  project_id: string;
  name: string;
  completion_percentage: number;
  days_behind_schedule: number;
  budget_variance_percentage: number;
  team_satisfaction_score: number;
}

export interface CollaborationMetrics {
  messages_sent: number;
  files_shared: number;
  meetings_held: number;
  cross_department_projects: number;
  knowledge_sharing_score: number;
}

// Form types for creating/editing
export interface CreateOrganizationData {
  name: string;
  slug: string;
  description?: string;
  industry?: string;
  size: Organization['size'];
  country?: string;
  timezone?: string;
  website?: string;
  subscription_tier: Organization['subscription_tier'];
}

export interface CreateProjectData {
  name: string;
  description?: string;
  priority: OrganizationProject['priority'];
  visibility: OrganizationProject['visibility'];
  start_date?: string;
  due_date?: string;
  budget?: number;
  estimated_hours?: number;
  project_manager_id?: string;
  team_members: string[];
  tags: string[];
}

export interface CreateTaskData {
  title: string;
  description?: string;
  priority: OrganizationTask['priority'];
  project_id?: string;
  assignee_id?: string;
  due_date?: string;
  estimated_hours?: number;
  labels: string[];
}

// Role permission mappings
export const ROLE_PERMISSIONS: Record<OrganizationMember['role'], OrganizationPermission[]> = {
  owner: [
    'organization.manage',
    'organization.settings',
    'organization.billing',
    'members.invite',
    'members.manage',
    'members.remove',
    'projects.create',
    'projects.edit',
    'projects.delete',
    'projects.view_all',
    'tasks.create',
    'tasks.edit',
    'tasks.delete',
    'tasks.assign',
    'channels.create',
    'channels.manage',
    'channels.moderate',
    'analytics.view',
    'billing.manage'
  ],
  admin: [
    'organization.settings',
    'members.invite',
    'members.manage',
    'projects.create',
    'projects.edit',
    'projects.delete',
    'projects.view_all',
    'tasks.create',
    'tasks.edit',
    'tasks.delete',
    'tasks.assign',
    'channels.create',
    'channels.manage',
    'channels.moderate',
    'analytics.view'
  ],
  project_manager: [
    'projects.create',
    'projects.edit',
    'projects.view_all',
    'tasks.create',
    'tasks.edit',
    'tasks.delete',
    'tasks.assign',
    'channels.create',
    'channels.manage',
    'analytics.view'
  ],
  member: [
    'tasks.create',
    'tasks.edit',
    'channels.create'
  ],
  viewer: [],
  guest: []
}; 