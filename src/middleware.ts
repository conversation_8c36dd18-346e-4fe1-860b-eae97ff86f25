import { NextRequest, NextResponse } from 'next/server';
import { RateLimiter } from './lib/security/rateLimiter';

// Helper function to get client IP
function getClientIP(request: NextRequest): string {
  // Check various headers for the real IP
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');

  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }

  if (realIP) {
    return realIP;
  }

  if (cfConnectingIP) {
    return cfConnectingIP;
  }

  // Fallback to connection remote address
  return request.ip || '127.0.0.1';
}

// Security-sensitive endpoints that require rate limiting
const PROTECTED_ENDPOINTS = [
  '/login',
  '/signup',
  '/auth/callback',
  '/api/challenge',
  '/api/auth',
  '/api/security'
];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const userIP = getClientIP(request);

  // Check if this is a protected endpoint
  const isProtectedEndpoint = PROTECTED_ENDPOINTS.some(endpoint =>
    pathname.startsWith(endpoint)
  );

  if (isProtectedEndpoint) {
    try {
      // Apply rate limiting
      const rateLimitResult = await RateLimiter.checkRateLimit(userIP, pathname);

      if (!rateLimitResult.allowed) {
        // Rate limit exceeded
        const response = NextResponse.json(
          {
            error: 'Rate limit exceeded',
            message: rateLimitResult.message,
            isBlocked: rateLimitResult.isBlocked,
            resetTime: rateLimitResult.resetTime,
            blockUntil: rateLimitResult.blockUntil
          },
          { status: 429 }
        );

        // Add rate limit headers
        response.headers.set('X-RateLimit-Limit', '5');
        response.headers.set('X-RateLimit-Remaining', rateLimitResult.remaining.toString());
        response.headers.set('X-RateLimit-Reset', rateLimitResult.resetTime.getTime().toString());

        if (rateLimitResult.blockUntil) {
          response.headers.set('X-RateLimit-BlockUntil', rateLimitResult.blockUntil.getTime().toString());
        }

        return response;
      }

      // Add rate limit info to headers for successful requests
      const response = NextResponse.next();
      response.headers.set('X-RateLimit-Remaining', rateLimitResult.remaining.toString());
      response.headers.set('X-RateLimit-Reset', rateLimitResult.resetTime.getTime().toString());
      response.headers.set('X-Client-IP', userIP);

      return response;

    } catch (error) {
      console.error('Middleware security check failed:', error);
      // On error, allow the request but log it
      const response = NextResponse.next();
      response.headers.set('X-Security-Error', 'true');
      return response;
    }
  }

  // For non-protected endpoints, just add the IP header
  const response = NextResponse.next();
  response.headers.set('X-Client-IP', userIP);
  return response;
}

export const config = {
  matcher: [
    // Security-sensitive routes
    '/login/:path*',
    '/signup/:path*',
    '/auth/:path*',
    '/api/challenge/:path*',
    '/api/auth/:path*',
    '/api/security/:path*',
    // Admin routes (will be protected client-side as well)
    '/admin/:path*',
    '/profile/:path*',
  ],
};
