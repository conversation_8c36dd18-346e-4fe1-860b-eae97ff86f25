import { NextResponse } from 'next/server';

export async function middleware() {
  // For now, we'll handle auth protection on the client side
  // This middleware will be updated when we implement server-side auth

  // Allow all requests to pass through for now
  // Client-side auth protection is handled in the components

  return NextResponse.next();
}

export const config = {
  matcher: [
    // Currently disabled - auth protection handled client-side
    // '/admin/:path*',
    // '/profile/:path*',
  ],
};
