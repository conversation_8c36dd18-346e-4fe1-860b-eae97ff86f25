# Data Migration to Encryption Plan

## Overview
This document outlines the strategy for implementing application-level encryption for sensitive user data in the HielTech application while maintaining zero downtime and ensuring data integrity.

## Migration Strategy

### Phase 1: Infrastructure Setup (Week 1)

#### 1.1 Encryption Keys Setup
- **Deploy Vault Extension**: Ensure Supabase Vault is available
- **Create Encryption Keys**: Generate AES-256 keys for different data types
  - `chat_encryption_key`: For chat messages
  - `profile_encryption_key`: For profile, inquiry, and team application data
  - `analytics_encryption_key`: For analytics data (IP addresses, etc.)
  - `file_encryption_key`: For uploaded files

#### 1.2 Database Schema Updates
```sql
-- Run the encryption_keys_setup.sql migration
-- This adds:
-- - encryption_metadata table
-- - encrypted_fields JSONB columns
-- - encryption helper functions
-- - encryption status tracking columns
```

#### 1.3 Application Services Deployment
- Deploy `EncryptionService`
- Deploy `FileEncryptionService` 
- Update `ChatSecurity` to use new encryption service

### Phase 2: Gradual Data Migration (Week 2-3)

#### 2.1 Migration Approach: Dual-Write System
To avoid downtime, we'll implement a dual-write system:

1. **New data**: Written encrypted to `encrypted_fields` column
2. **Existing data**: Remains in original columns during migration
3. **Reads**: Check `encrypted_fields` first, fallback to original columns
4. **Background migration**: Gradually encrypt existing data

#### 2.2 Migration Script Example
```typescript
// Migration script for profiles table
async function migrateProfilesData() {
  const batchSize = 100;
  let offset = 0;
  
  while (true) {
    const { data: profiles, error } = await supabase
      .from('profiles')
      .select('*')
      .is('encrypted_fields', null) // Only non-migrated records
      .range(offset, offset + batchSize - 1);
    
    if (error || !profiles || profiles.length === 0) break;
    
    for (const profile of profiles) {
      try {
        const encryptedData = await EncryptionUtils.encryptProfile(profile);
        
        await supabase
          .from('profiles')
          .update({
            encrypted_fields: encryptedData.encrypted_fields,
            encryption_version: 1
          })
          .eq('id', profile.id);
          
        console.log(`Migrated profile ${profile.id}`);
      } catch (error) {
        console.error(`Failed to migrate profile ${profile.id}:`, error);
      }
    }
    
    offset += batchSize;
    // Add delay to avoid overwhelming the database
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}
```

#### 2.3 Data Access Pattern During Migration
```typescript
// Example: Reading profile data during migration
async function getProfile(userId: string) {
  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();
    
  if (profile.encrypted_fields && Object.keys(profile.encrypted_fields).length > 0) {
    // Data is encrypted - decrypt it
    return await EncryptionUtils.decryptProfile(profile);
  } else {
    // Data is not yet encrypted - return as is
    return profile;
  }
}
```

### Phase 3: File Encryption Migration (Week 3-4)

#### 3.1 File Migration Strategy
Files require special handling due to their size and storage implications:

1. **New uploads**: Encrypted before storage
2. **Existing files**: Migrate on-demand or background process
3. **File metadata**: Update to track encryption status

#### 3.2 File Migration Process
```typescript
// Background file migration
async function migrateFileToEncrypted(fileRecord: any) {
  try {
    // Download existing file
    const { data: fileData } = await supabase.storage
      .from('uploads')
      .download(fileRecord.file_path);
    
    if (!fileData) throw new Error('File not found');
    
    // Convert to File object
    const file = new File([fileData], fileRecord.original_name, {
      type: fileRecord.mime_type
    });
    
    // Encrypt file
    const encrypted = await FileEncryptionService.encryptFile(file);
    
    // Upload encrypted version
    const { data: uploadData } = await supabase.storage
      .from('uploads')
      .upload(encrypted.metadata.encryptedName, encrypted.encryptedData);
    
    // Update database record
    await supabase
      .from('blog_media')
      .update({
        file_path: encrypted.metadata.encryptedName,
        is_encrypted: true,
        encryption_metadata: encrypted.metadata
      })
      .eq('id', fileRecord.id);
      
    // Delete old file
    await supabase.storage
      .from('uploads')
      .remove([fileRecord.file_path]);
      
  } catch (error) {
    console.error(`Failed to migrate file ${fileRecord.id}:`, error);
  }
}
```

### Phase 4: Cleanup and Finalization (Week 5)

#### 4.1 Data Validation
- Verify all sensitive data is encrypted
- Run integrity checks on migrated data
- Ensure decryption works correctly

#### 4.2 Schema Cleanup
After successful migration:
```sql
-- Remove plaintext columns (after confirming migration success)
-- This should be done with extreme caution and only after thorough testing

-- Example for profiles table:
-- ALTER TABLE profiles DROP COLUMN email;
-- ALTER TABLE profiles DROP COLUMN display_name;
-- ALTER TABLE profiles DROP COLUMN bio;
-- etc.

-- Note: Keep original columns for a safety period before dropping
```

#### 4.3 Application Updates
- Remove dual-read logic
- Update all queries to use encrypted data
- Remove fallback to plaintext columns

## Migration Monitoring and Safety

### 4.1 Monitoring Metrics
Track the following during migration:
- **Migration Progress**: Percentage of records migrated per table
- **Error Rate**: Failed encryption/decryption attempts
- **Performance Impact**: Database response times
- **Data Integrity**: Comparison of original vs decrypted data

### 4.2 Rollback Strategy
If issues arise during migration:

1. **Stop Migration Process**: Halt background migration scripts
2. **Revert Application Code**: Switch back to reading plaintext data
3. **Data Recovery**: Original data remains intact in original columns
4. **Investigation**: Analyze logs and fix issues before retrying

### 4.3 Testing Strategy
Before production migration:

1. **Test Environment**: Run full migration on copy of production data
2. **Performance Testing**: Verify encryption/decryption performance
3. **Data Validation**: Ensure all data can be correctly decrypted
4. **Integration Testing**: Test all application features with encrypted data

## Implementation Timeline

### Week 1: Infrastructure
- [ ] Deploy encryption services
- [ ] Run database migrations
- [ ] Set up monitoring

### Week 2: Data Migration Start
- [ ] Implement dual-write system
- [ ] Start background migration for profiles
- [ ] Monitor and validate

### Week 3: Expand Migration
- [ ] Migrate chat messages
- [ ] Migrate inquiries and team applications
- [ ] Start file encryption migration

### Week 4: File Migration
- [ ] Complete file encryption
- [ ] Validate all data integrity
- [ ] Performance optimization

### Week 5: Cleanup
- [ ] Remove dual-read logic
- [ ] Clean up old columns (after safety period)
- [ ] Documentation and team training

## Security Considerations

### 5.1 Key Management
- **Key Rotation**: Plan for periodic key rotation
- **Access Control**: Only service role can access encryption keys
- **Backup Strategy**: Secure backup of encryption keys

### 5.2 Compliance
- **GDPR**: Encrypted data provides additional protection
- **Data Residency**: Encryption keys stored in same region as data
- **Audit Trail**: Log all encryption/decryption operations

### 5.3 Performance Impact
- **Caching**: Cache decrypted data appropriately
- **Batch Operations**: Optimize bulk encryption/decryption
- **Database Performance**: Monitor query performance impact

## Recovery Procedures

### 6.1 Data Recovery
If data corruption occurs:
1. Original plaintext data preserved during migration period
2. Database backups available for point-in-time recovery
3. Encryption keys backed up securely

### 6.2 Key Recovery
If encryption keys are lost:
1. Immediate incident response
2. Restore from secure key backup
3. Re-encrypt affected data if necessary

## Success Criteria

Migration is considered successful when:
- [ ] 100% of sensitive data is encrypted
- [ ] All application features work with encrypted data
- [ ] Performance within acceptable limits
- [ ] No data integrity issues
- [ ] Security audit passes
- [ ] Team trained on new systems

## Post-Migration Monitoring

Continue monitoring for 30 days post-migration:
- **Performance Metrics**: Response times, error rates
- **Security Metrics**: Failed decryption attempts, access patterns
- **User Experience**: Any user-reported issues
- **Data Integrity**: Regular integrity checks

---

This migration plan ensures a safe, gradual transition to encrypted data storage while maintaining system availability and data integrity throughout the process. 